import { db, uid } from "@/lib/db"
import { workOrders, workOperations, qualityInspections, salesContracts, salesContractItems } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { BusinessLogicError, ErrorCode } from "@/lib/errors"

export interface WorkOrderGenerationOptions {
  priority?: "low" | "normal" | "high" | "urgent"
  dueDateOffset?: number // Days from now
  autoCreateQualityInspections?: boolean
  notes?: string
}

export interface GeneratedWorkOrder {
  id: string
  number: string
  product_id: string
  qty: number
  sales_contract_id: string
  status: string
  operations: Array<{
    id: string
    operation_name: string
    sequence: string
  }>
  qualityInspections: Array<{
    id: string
    inspection_type: string
    status: string
  }>
}

export class WorkOrderGenerationService {
  /**
   * Generate work orders from an approved sales contract
   */
  async generateFromContract(
    contractId: string,
    companyId: string,
    options: WorkOrderGenerationOptions = {}
  ): Promise<GeneratedWorkOrder[]> {
    // Validate contract exists and is approved
    const contract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, contractId),
        eq(salesContracts.company_id, companyId)
      ),
      with: {
        items: {
          with: {
            product: true,
          },
        },
        customer: true,
      },
    })

    if (!contract) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        `Sales contract ${contractId} not found`
      )
    }

    if (contract.status !== "approved") {
      throw new BusinessLogicError(
        ErrorCode.INVALID_WORKFLOW_TRANSITION,
        `Cannot generate work orders from contract with status: ${contract.status}. Contract must be approved.`
      )
    }

    if (!contract.items || contract.items.length === 0) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Cannot generate work orders: contract has no items"
      )
    }

    // Check if work orders already exist for this contract
    const existingWorkOrders = await db.query.workOrders.findMany({
      where: and(
        eq(workOrders.sales_contract_id, contractId),
        eq(workOrders.company_id, companyId)
      ),
    })

    if (existingWorkOrders.length > 0) {
      throw new BusinessLogicError(
        ErrorCode.BUSINESS_RULE_VIOLATION,
        `Work orders already exist for contract ${contract.number}. Cannot generate duplicates.`
      )
    }

    const generatedWorkOrders: GeneratedWorkOrder[] = []

    // Generate work orders in a transaction
    await db.transaction(async (tx) => {
      for (const item of contract.items) {
        const workOrder = await this.createWorkOrderForItem(
          tx,
          contract,
          item,
          companyId,
          options
        )
        generatedWorkOrders.push(workOrder)
      }
    })

    return generatedWorkOrders
  }

  /**
   * Create a work order for a specific contract item
   */
  private async createWorkOrderForItem(
    tx: any,
    contract: any,
    item: any,
    companyId: string,
    options: WorkOrderGenerationOptions
  ): Promise<GeneratedWorkOrder> {
    const workOrderId = uid("wo")
    const workOrderNumber = this.generateWorkOrderNumber(contract.number, item.product.sku)

    // Calculate due date
    const dueDate = options.dueDateOffset
      ? new Date(Date.now() + options.dueDateOffset * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      : undefined

    // Create work order
    const newWorkOrder = {
      id: workOrderId,
      company_id: companyId,
      number: workOrderNumber,
      sales_contract_id: contract.id,
      product_id: item.product_id,
      qty: item.qty,
      due_date: dueDate,
      status: "pending",
      priority: options.priority || "normal",
      notes: options.notes || `Auto-generated from contract ${contract.number}`,
    }

    await tx.insert(workOrders).values(newWorkOrder)

    // Create standard operation sequence
    const operations = await this.createOperationSequence(
      tx,
      workOrderId,
      companyId,
      item.product
    )

    // Create quality inspections if requested
    const inspections = options.autoCreateQualityInspections !== false
      ? await this.createQualityInspections(tx, workOrderId, companyId)
      : []

    return {
      id: workOrderId,
      number: workOrderNumber,
      product_id: item.product_id,
      qty: parseInt(item.qty),
      sales_contract_id: contract.id,
      status: "pending",
      operations,
      qualityInspections: inspections,
    }
  }

  /**
   * Generate professional work order number
   */
  private generateWorkOrderNumber(contractNumber: string, productSku: string): string {
    const date = new Date()
    const year = date.getFullYear().toString().slice(-2)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const random = Math.random().toString(36).substring(2, 6).toUpperCase()

    // Format: WO-YYMMDD-CONTRACT-SKU-RANDOM
    const contractSuffix = contractNumber.replace(/[^A-Z0-9]/g, '').slice(-4)
    const skuSuffix = productSku.replace(/[^A-Z0-9]/g, '').slice(-4)

    return `WO-${year}${month}${day}-${contractSuffix}-${skuSuffix}-${random}`
  }

  /**
   * Create standard operation sequence for manufacturing
   */
  private async createOperationSequence(
    tx: any,
    workOrderId: string,
    companyId: string,
    product: any
  ): Promise<Array<{ id: string; operation_name: string; sequence: string }>> {
    const standardOperations = [
      "Material Preparation",
      "Production Setup",
      "Manufacturing",
      "Quality Inspection",
      "Packaging",
      "Final Review"
    ]

    const operations = []

    for (let i = 0; i < standardOperations.length; i++) {
      const operationId = uid("wop")
      const operation = {
        id: operationId,
        company_id: companyId,
        work_order_id: workOrderId,
        operation_name: standardOperations[i],
        sequence: (i + 1).toString(),
        status: "pending",
      }

      await tx.insert(workOperations).values(operation)
      operations.push({
        id: operationId,
        operation_name: standardOperations[i],
        sequence: (i + 1).toString(),
      })
    }

    return operations
  }

  /**
   * Create quality inspections for the work order (PROPER MANUFACTURING ERP LOGIC)
   * Only create inspections that are actually needed for work orders
   */
  private async createQualityInspections(
    tx: any,
    workOrderId: string,
    companyId: string
  ): Promise<Array<{ id: string; inspection_type: string; status: string }>> {
    // ✅ MANUFACTURING ERP BEST PRACTICE:
    // Work orders should only trigger FINAL inspections for finished goods
    // Incoming inspections are for raw materials (triggered by purchase orders)
    // In-process inspections are optional and workflow-triggered during production

    const inspections = []

    // Create only FINAL inspection for work order completion
    const inspectionId = uid("qi")
    const inspection = {
      id: inspectionId,
      company_id: companyId,
      work_order_id: workOrderId,
      inspection_type: "final", // Only final inspection for work orders
      inspector: "TBD", // To be assigned when work order completes
      inspection_date: new Date().toISOString().split('T')[0],
      status: "scheduled", // Scheduled for when work order completes
      notes: `Final inspection scheduled for work order completion`,
    }

    await tx.insert(qualityInspections).values(inspection)
    inspections.push({
      id: inspectionId,
      inspection_type: "final",
      status: "scheduled",
    })

    return inspections
  }

  /**
   * Get work order generation preview without creating records
   */
  async previewGeneration(
    contractId: string,
    companyId: string,
    options: WorkOrderGenerationOptions = {}
  ): Promise<{
    contract: any
    workOrdersToCreate: Array<{
      product: any
      quantity: number
      estimatedDueDate?: string
      operations: string[]
      qualityInspections: string[]
    }>
    totalWorkOrders: number
  }> {
    const contract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, contractId),
        eq(salesContracts.company_id, companyId)
      ),
      with: {
        items: {
          with: {
            product: true,
          },
        },
        customer: true,
      },
    })

    if (!contract) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        `Sales contract ${contractId} not found`
      )
    }

    const dueDate = options.dueDateOffset
      ? new Date(Date.now() + options.dueDateOffset * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      : undefined

    const workOrdersToCreate = contract.items.map(item => ({
      product: item.product,
      quantity: parseInt(item.qty),
      estimatedDueDate: dueDate,
      operations: [
        "Material Preparation",
        "Production Setup",
        "Manufacturing",
        "Quality Inspection",
        "Packaging",
        "Final Review"
      ],
      qualityInspections: options.autoCreateQualityInspections !== false
        ? ["incoming", "in-process", "final"]
        : [],
    }))

    return {
      contract,
      workOrdersToCreate,
      totalWorkOrders: workOrdersToCreate.length,
    }
  }
}

export const workOrderGenerationService = new WorkOrderGenerationService()
