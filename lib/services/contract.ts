import { BaseService, ServiceContext } from "./base"
import { salesContracts, salesContractItems, purchaseContracts, purchaseContractItems } from "@/lib/schema-postgres"
import { uid } from "@/lib/db"
import { AuditResource, logContractOperation } from "@/lib/audit"
import { salesContractSchema, purchaseContractSchema } from "@/lib/validations"
import { sanitizeFormData } from "@/lib/sanitization"
import { BusinessLogicError, ErrorCode, createInvalidStatusTransitionError } from "@/lib/errors"

// Contract types
export interface ContractItem {
  id: string
  productId: string
  qty: number
  price: number
  product?: {
    id: string
    sku: string
    name: string
    unit: string
  }
}

export interface SalesContract {
  id: string
  number: string
  customerId: string
  date?: string
  templateId?: string
  status: string
  currency: string
  items: ContractItem[]
  customer?: {
    id: string
    name: string
  }
  created_at: string
  updated_at: string
}

export interface CreateSalesContractData {
  number: string
  customerId: string
  currency: string
  items: Array<{
    productId: string
    qty: number
    price: number
  }>
}

export interface UpdateSalesContractData {
  number?: string
  customerId?: string
  currency?: string
  status?: string
  items?: Array<{
    productId: string
    qty: number
    price: number
  }>
}

export class SalesContractService extends BaseService<SalesContract, CreateSalesContractData, UpdateSalesContractData> {
  protected tableName = "salesContracts"
  protected auditResource = AuditResource.SALES_CONTRACT

  protected getTable() {
    return salesContracts
  }

  protected async validateCreate(data: CreateSalesContractData): Promise<void> {
    // Sanitize input data
    const sanitized = sanitizeFormData(data)

    // Validate with Zod schema
    const validation = salesContractSchema.safeParse(sanitized)
    if (!validation.success) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Invalid sales contract data",
        { issues: validation.error.issues }
      )
    }

    // Check for duplicate contract number
    await this.checkDuplicate(
      "number",
      data.number,
      undefined,
      `Sales contract with number "${data.number}" already exists`
    )

    // Validate customer exists
    await this.checkReference(
      "customers",
      "id",
      data.customerId,
      "Customer not found"
    )

    // Validate all products exist
    for (const item of data.items) {
      await this.checkReference(
        "products",
        "id",
        item.productId,
        `Product with ID "${item.productId}" not found`
      )
    }

    // Validate items
    if (data.items.length === 0) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Contract must have at least one item"
      )
    }
  }

  protected async validateUpdate(id: string, data: UpdateSalesContractData): Promise<void> {
    // Get current contract to check status transitions
    const current = await this.findById(id)
    if (!current) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        "Sales contract not found"
      )
    }

    // Validate status transitions
    if (data.status && data.status !== current.status) {
      await this.validateStatusTransition(current.status, data.status)
    }

    // Check for duplicate contract number if number is being updated
    if (data.number) {
      await this.checkDuplicate(
        "number",
        data.number,
        id,
        `Sales contract with number "${data.number}" already exists`
      )
    }

    // Validate customer exists if being updated
    if (data.customerId) {
      await this.checkReference(
        "customers",
        "id",
        data.customerId,
        "Customer not found"
      )
    }

    // Validate all products exist if items are being updated
    if (data.items) {
      for (const item of data.items) {
        await this.checkReference(
          "products",
          "id",
          item.productId,
          `Product with ID "${item.productId}" not found`
        )
      }
    }
  }

  private async validateStatusTransition(currentStatus: string, newStatus: string): Promise<void> {
    const validTransitions: Record<string, string[]> = {
      "draft": ["review", "cancelled"],
      "review": ["approved", "draft", "cancelled"],
      "approved": ["active", "cancelled"],
      "active": ["completed", "cancelled"],
      "completed": [], // No transitions from completed
      "cancelled": [] // No transitions from cancelled
    }

    const allowedStatuses = validTransitions[currentStatus] || []
    if (!allowedStatuses.includes(newStatus)) {
      throw createInvalidStatusTransitionError(currentStatus, newStatus)
    }
  }

  protected mapToEntity(data: any): SalesContract {
    return {
      id: data.id,
      number: data.number,
      customerId: data.customerId,
      date: data.date,
      templateId: data.templateId,
      status: data.status || "draft",
      currency: data.currency,
      items: data.items || [],
      customer: data.customer,
      created_at: data.created_at,
      updated_at: data.updated_at
    }
  }

  protected mapCreateData(data: CreateSalesContractData): any {
    return {
      id: uid("sc"),
      number: data.number,
      customerId: data.customerId,
      currency: data.currency,
      status: "draft"
    }
  }

  protected mapUpdateData(data: UpdateSalesContractData): any {
    const updateData: any = {}

    if (data.number !== undefined) updateData.number = data.number
    if (data.customerId !== undefined) updateData.customerId = data.customerId
    if (data.currency !== undefined) updateData.currency = data.currency
    if (data.status !== undefined) updateData.status = data.status

    return updateData
  }

  // Override create to handle contract items
  async create(data: CreateSalesContractData, user: any): Promise<SalesContract> {
    try {
      await this.validateCreate(data)

      const contractData = this.mapCreateData(data)
      const items = data.items.map(item => ({
        id: uid("sci"),
        contractId: contractData.id,
        productId: item.productId,
        qty: item.qty.toString(),
        price: item.price.toString()
      }))

      // Use transaction to create contract and items
      await this.context.db.transaction(async (tx) => {
        await tx.insert(salesContracts).values(contractData)
        if (items.length > 0) {
          await tx.insert(salesContractItems).values(items)
        }
      })

      // Log contract creation
      logContractOperation(
        user,
        "CREATE",
        AuditResource.SALES_CONTRACT,
        contractData.id,
        contractData.number,
        contractData.status,
        this.context.ipAddress,
        this.context.userAgent
      )

      // Fetch and return the created contract with items
      const created = await this.findById(contractData.id)
      if (!created) {
        throw new BusinessLogicError(
          ErrorCode.DATABASE_ERROR,
          "Failed to retrieve created contract"
        )
      }

      return created
    } catch (error) {
      throw error
    }
  }

  // Override findById to include items and relations
  async findById(id: string): Promise<SalesContract | null> {
    try {
      const row = await this.context.db.query.salesContracts.findFirst({
        where: (contracts: any, { eq }: any) => eq(contracts.id, id),
        with: {
          items: {
            with: {
              product: true
            }
          },
          customer: true
        }
      })

      if (!row) {
        return null
      }

      return this.mapToEntity({
        ...row,
        items: row.items.map((item: any) => ({
          id: item.id,
          productId: item.productId,
          qty: parseFloat(item.qty),
          price: parseFloat(item.price),
          product: item.product
        }))
      })
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to find sales contract"
      )
    }
  }

  // Override findAll to include relations
  async findAll(filters?: Record<string, any>): Promise<SalesContract[]> {
    try {
      const rows = await this.context.db.query.salesContracts.findMany({
        with: {
          items: {
            with: {
              product: true
            }
          },
          customer: true
        },
        orderBy: (contracts: any, { desc }: any) => [desc(contracts.created_at)]
      })

      return rows.map(row => this.mapToEntity({
        ...row,
        items: row.items.map((item: any) => ({
          id: item.id,
          productId: item.productId,
          qty: parseFloat(item.qty),
          price: parseFloat(item.price),
          product: item.product
        }))
      }))
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to fetch sales contracts"
      )
    }
  }

  // Business methods
  async findByStatus(status: string): Promise<SalesContract[]> {
    try {
      const rows = await this.context.db.query.salesContracts.findMany({
        where: (contracts: any, { eq }: any) => eq(contracts.status, status),
        with: {
          items: {
            with: {
              product: true
            }
          },
          customer: true
        },
        orderBy: (contracts: any, { desc }: any) => [desc(contracts.created_at)]
      })

      return rows.map(row => this.mapToEntity({
        ...row,
        items: row.items.map((item: any) => ({
          id: item.id,
          productId: item.productId,
          qty: parseFloat(item.qty),
          price: parseFloat(item.price),
          product: item.product
        }))
      }))
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to fetch contracts by status"
      )
    }
  }

  async updateStatus(id: string, status: string, user: any): Promise<SalesContract> {
    const updated = await this.update(id, { status }, user)

    // Log status change
    logContractOperation(
      user,
      "UPDATE",
      AuditResource.SALES_CONTRACT,
      id,
      updated.number,
      status,
      this.context.ipAddress,
      this.context.userAgent
    )

    return updated
  }

  async getContractValue(id: string): Promise<number> {
    const contract = await this.findById(id)
    if (!contract) {
      throw new BusinessLogicError(
        ErrorCode.REFERENCE_NOT_FOUND,
        "Contract not found"
      )
    }

    return contract.items.reduce((total, item) => {
      return total + (item.qty * item.price)
    }, 0)
  }

  async getContractStats(): Promise<{
    total: number
    byStatus: Record<string, number>
    totalValue: number
  }> {
    try {
      const contracts = await this.findAll()

      const byStatus: Record<string, number> = {}
      let totalValue = 0

      for (const contract of contracts) {
        // Count by status
        byStatus[contract.status] = (byStatus[contract.status] || 0) + 1

        // Calculate total value
        const contractValue = contract.items.reduce((sum, item) => {
          return sum + (item.qty * item.price)
        }, 0)
        totalValue += contractValue
      }

      return {
        total: contracts.length,
        byStatus,
        totalValue
      }
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to get contract statistics"
      )
    }
  }
}
