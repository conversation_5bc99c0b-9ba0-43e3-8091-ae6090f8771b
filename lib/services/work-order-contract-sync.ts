/**
 * ✅ PROFESSIONAL ERP: Work Order - Sales Contract Synchronization Service
 * Based on SAP, Oracle, Odoo, and ERPNext best practices
 */

import { db } from "@/lib/db"
import { workOrders, salesContracts, salesContractItems } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"

export interface WorkOrderChange {
  workOrderId: string
  field: string
  oldValue: any
  newValue: any
  userId: string
  companyId: string
}

export interface PropagationResult {
  shouldPropagate: boolean
  requiresApproval: boolean
  notifications: string[]
  contractUpdates?: any
  reason: string
}

export class WorkOrderContractSyncService {
  
  /**
   * ✅ PROFESSIONAL ERP: Analyze if work order change should propagate to contract
   */
  static async analyzePropagation(change: WorkOrderChange): Promise<PropagationResult> {
    const { workOrderId, field, oldValue, newValue, companyId } = change
    
    // Get work order with contract relationship
    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, workOrderId),
        eq(workOrders.company_id, companyId)
      ),
      with: {
        salesContract: {
          with: {
            items: true
          }
        },
        product: true
      }
    })

    if (!workOrder?.salesContract) {
      return {
        shouldPropagate: false,
        requiresApproval: false,
        notifications: [],
        reason: "No associated sales contract"
      }
    }

    // Apply business rules based on field
    switch (field) {
      case 'qty':
        return this.analyzeQuantityChange(workOrder, oldValue, newValue)
      
      case 'status':
        return this.analyzeStatusChange(workOrder, oldValue, newValue)
      
      case 'due_date':
        return this.analyzeDateChange(workOrder, oldValue, newValue)
      
      case 'priority':
      case 'notes':
        return {
          shouldPropagate: false,
          requiresApproval: false,
          notifications: [],
          reason: `${field} changes are internal to production`
        }
      
      default:
        return {
          shouldPropagate: false,
          requiresApproval: false,
          notifications: [],
          reason: "Unknown field type"
        }
    }
  }

  /**
   * ✅ PROFESSIONAL ERP: Quantity Change Analysis (SAP-style)
   */
  private static analyzeQuantityChange(workOrder: any, oldQty: string, newQty: string): PropagationResult {
    const oldQuantity = parseFloat(oldQty) || 0
    const newQuantity = parseFloat(newQty) || 0
    const variance = Math.abs(newQuantity - oldQuantity) / oldQuantity
    const isIncrease = newQuantity > oldQuantity

    // Industry standard: 5% variance threshold
    if (variance > 0.05) {
      return {
        shouldPropagate: true,
        requiresApproval: isIncrease, // Increases require approval
        notifications: ['sales_team', 'customer_service', 'planning'],
        contractUpdates: {
          // Find matching contract item and update quantity
          updateContractItem: true,
          newQuantity: newQuantity,
          variancePercent: Math.round(variance * 100)
        },
        reason: `Quantity variance of ${Math.round(variance * 100)}% exceeds 5% threshold`
      }
    }

    return {
      shouldPropagate: false,
      requiresApproval: false,
      notifications: [],
      reason: "Quantity variance within acceptable range (<5%)"
    }
  }

  /**
   * ✅ PROFESSIONAL ERP: Status Change Analysis (Oracle-style)
   */
  private static analyzeStatusChange(workOrder: any, oldStatus: string, newStatus: string): PropagationResult {
    const criticalStatuses = ['completed', 'cancelled', 'on_hold']
    
    if (criticalStatuses.includes(newStatus)) {
      const contractStatusMap: Record<string, string> = {
        'completed': 'in_production', // Or next appropriate status
        'cancelled': 'cancelled',
        'on_hold': 'on_hold'
      }

      return {
        shouldPropagate: true,
        requiresApproval: newStatus === 'cancelled',
        notifications: ['sales_team', 'customer_service', 'planning'],
        contractUpdates: {
          updateContractStatus: true,
          newStatus: contractStatusMap[newStatus],
          affectedWorkOrder: workOrder.id
        },
        reason: `Status change to '${newStatus}' affects contract fulfillment`
      }
    }

    return {
      shouldPropagate: false,
      requiresApproval: false,
      notifications: [],
      reason: "Status change does not affect contract"
    }
  }

  /**
   * ✅ PROFESSIONAL ERP: Due Date Change Analysis (Odoo-style)
   */
  private static analyzeDateChange(workOrder: any, oldDate: string, newDate: string): PropagationResult {
    if (!oldDate || !newDate) {
      return {
        shouldPropagate: false,
        requiresApproval: false,
        notifications: [],
        reason: "Date change does not affect contract delivery"
      }
    }

    const oldDueDate = new Date(oldDate)
    const newDueDate = new Date(newDate)
    const isDelay = newDueDate > oldDueDate
    const daysDifference = Math.abs(newDueDate.getTime() - oldDueDate.getTime()) / (1000 * 60 * 60 * 24)

    // Industry standard: 3+ day changes affect contract
    if (daysDifference >= 3) {
      return {
        shouldPropagate: true,
        requiresApproval: isDelay, // Delays require approval
        notifications: ['customer_service', 'logistics', 'sales_team'],
        contractUpdates: {
          updateDeliveryDate: true,
          newDeliveryDate: newDate,
          daysDifference: Math.round(daysDifference),
          isDelay: isDelay
        },
        reason: `Due date change of ${Math.round(daysDifference)} days affects delivery commitment`
      }
    }

    return {
      shouldPropagate: false,
      requiresApproval: false,
      notifications: [],
      reason: "Due date change within acceptable range (<3 days)"
    }
  }

  /**
   * ✅ PROFESSIONAL ERP: Execute Contract Updates (ERPNext-style)
   */
  static async executeContractUpdates(
    workOrderId: string, 
    contractUpdates: any, 
    companyId: string,
    userId: string
  ): Promise<boolean> {
    try {
      const workOrder = await db.query.workOrders.findFirst({
        where: and(
          eq(workOrders.id, workOrderId),
          eq(workOrders.company_id, companyId)
        ),
        with: {
          salesContract: true
        }
      })

      if (!workOrder?.salesContract) {
        throw new Error("Work order or contract not found")
      }

      const contractId = workOrder.sales_contract_id

      // Update contract based on the type of change
      if (contractUpdates.updateContractItem) {
        // Update contract item quantity
        await db.update(salesContractItems)
          .set({ 
            qty: contractUpdates.newQuantity.toString(),
            // Add audit fields
            updated_at: new Date(),
          })
          .where(and(
            eq(salesContractItems.sales_contract_id, contractId),
            eq(salesContractItems.product_id, workOrder.product_id)
          ))
      }

      if (contractUpdates.updateContractStatus) {
        // Update contract status
        await db.update(salesContracts)
          .set({ 
            status: contractUpdates.newStatus,
            updated_at: new Date(),
          })
          .where(eq(salesContracts.id, contractId))
      }

      if (contractUpdates.updateDeliveryDate) {
        // Update contract delivery date
        await db.update(salesContracts)
          .set({ 
            delivery_date: contractUpdates.newDeliveryDate,
            updated_at: new Date(),
          })
          .where(eq(salesContracts.id, contractId))
      }

      // TODO: Add audit trail logging
      // TODO: Send notifications to stakeholders
      
      return true
    } catch (error) {
      console.error("Contract update failed:", error)
      return false
    }
  }
}
