/**
 * Quality-Inventory Integration Service
 * 
 * Handles automated workflow integration between Quality Control and Inventory modules
 * Following established patterns with multi-tenant security and zero-breaking-changes philosophy
 */

import { db, uid } from "@/lib/db"
import { stockLots, stockTxns, qualityInspections } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"

export interface QualityInventoryContext {
  companyId: string
  userId?: string
}

export interface StockLotUpdate {
  quality_status: "pending" | "approved" | "rejected" | "quarantined"
  quality_approved_date?: string
  quality_approved_by?: string
  quality_notes?: string
  inspection_id?: string
}

export interface StockTransactionData {
  type: "inbound" | "outbound"
  product_id: string
  qty: string
  workflow_trigger: "quality_approved" | "quality_rejected" | "work_order_completed" | "manual"
  reference_id: string
  location?: string
  notes?: string
}

/**
 * Quality-Inventory Integration Service
 * Handles automated workflows between Quality Control and Inventory modules
 */
export class QualityInventoryIntegrationService {

  /**
   * Triggered when quality inspection status changes to 'passed' or 'completed'
   * Automatically updates related stock lot quality status and creates transactions
   * Professional 4-status system: pending → approved
   */
  async onQualityInspectionPassed(
    inspectionId: string,
    context: QualityInventoryContext
  ): Promise<void> {
    try {
      // 1. Get quality inspection details with multi-tenant security
      const inspection = await db.query.qualityInspections.findFirst({
        where: and(
          eq(qualityInspections.id, inspectionId),
          eq(qualityInspections.company_id, context.companyId)
        ),
        with: {
          workOrder: {
            with: {
              product: true
            }
          }
        }
      })

      if (!inspection) {
        throw new Error(`Quality inspection ${inspectionId} not found or access denied`)
      }

      // 2. Find stock lot linked to this inspection (Professional Workflow)
      let relatedStockLots = await db.query.stockLots.findMany({
        where: and(
          eq(stockLots.company_id, context.companyId),
          eq(stockLots.inspection_id, inspectionId)
        )
      })

      // 3. If no direct link, find by work order (for existing data compatibility)
      if (relatedStockLots.length === 0 && inspection.work_order_id) {
        relatedStockLots = await db.query.stockLots.findMany({
          where: and(
            eq(stockLots.company_id, context.companyId),
            eq(stockLots.work_order_id, inspection.work_order_id)
          )
        })

        // Link found stock lots to this inspection
        if (relatedStockLots.length > 0) {
          for (const stockLot of relatedStockLots) {
            await db.update(stockLots)
              .set({ inspection_id: inspectionId })
              .where(and(
                eq(stockLots.id, stockLot.id),
                eq(stockLots.company_id, context.companyId)
              ))
          }
          console.log(`🔗 Quality-Inventory Integration: Linked ${relatedStockLots.length} stock lots to inspection ${inspectionId}`)
        }
      }

      // 4. ✅ PROFESSIONAL ERP: Quality integration should NEVER create new stock lots
      // Stock lots must be created by work order completion, quality only updates status
      if (relatedStockLots.length === 0) {
        console.error(`❌ CRITICAL: No stock lots found for inspection ${inspectionId}`)
        console.error(`❌ Work Order ID: ${inspection.work_order_id}`)
        console.error(`❌ This indicates work order integration failed or stock lot was not created properly`)

        throw new Error(
          `No stock lots found for quality inspection ${inspectionId}. ` +
          `Stock lots must be created by work order completion before quality approval. ` +
          `This indicates a workflow integration issue.`
        )
      }

      // 5. ✅ PROFESSIONAL ERP: Update existing stock lot quality status to approved
      console.log(`📋 Quality-Inventory Integration: Updating ${relatedStockLots.length} stock lots to approved status`)
      for (const stockLot of relatedStockLots) {
        console.log(`📦 Updating stock lot ${stockLot.id} from ${stockLot.quality_status} to approved`)
        await this.updateStockLotQualityStatus(stockLot.id, {
          quality_status: "approved",
          quality_approved_date: new Date().toISOString(),
          quality_approved_by: inspection.inspector,
          quality_notes: `Quality approved via inspection ${inspectionId}`,
          inspection_id: inspectionId
        }, context)
        console.log(`✅ Stock lot ${stockLot.id} successfully updated to approved status`)
      }

      // 4. Create quality approval stock transaction
      if (inspection.workOrder) {
        await this.createStockTransaction({
          type: "inbound",
          product_id: inspection.workOrder.product_id,
          qty: inspection.workOrder.qty,
          workflow_trigger: "quality_approved",
          reference_id: inspectionId,
          location: "finished_goods",
          notes: `Quality approved by ${inspection.inspector} - ready for shipping`
        }, context)
      }

      console.log(`✅ Quality-Inventory Integration: Inspection ${inspectionId} approved, inventory updated`)

    } catch (error) {
      console.error(`❌ Quality-Inventory Integration Error:`, error)
      throw error
    }
  }

  /**
   * Triggered when quality inspection status changes to 'failed' or 'rejected'
   * Updates stock lot to quarantined status for review/rework
   * Professional workflow: failed inspection → quarantined (not rejected)
   */
  async onQualityInspectionFailed(
    inspectionId: string,
    context: QualityInventoryContext
  ): Promise<void> {
    try {
      // Get quality inspection details
      const inspection = await db.query.qualityInspections.findFirst({
        where: and(
          eq(qualityInspections.id, inspectionId),
          eq(qualityInspections.company_id, context.companyId)
        ),
        with: {
          workOrder: {
            with: {
              product: true
            }
          }
        }
      })

      if (!inspection) {
        throw new Error(`Quality inspection ${inspectionId} not found or access denied`)
      }

      // Find stock lots linked to this inspection (Professional Workflow)
      let relatedStockLots = await db.query.stockLots.findMany({
        where: and(
          eq(stockLots.company_id, context.companyId),
          eq(stockLots.inspection_id, inspectionId)
        )
      })

      // If no direct link, find by work order (for existing data compatibility)
      if (relatedStockLots.length === 0 && inspection.work_order_id) {
        relatedStockLots = await db.query.stockLots.findMany({
          where: and(
            eq(stockLots.company_id, context.companyId),
            eq(stockLots.work_order_id, inspection.work_order_id)
          )
        })
      }

      for (const stockLot of relatedStockLots) {
        await this.updateStockLotQualityStatus(stockLot.id, {
          quality_status: "quarantined",
          quality_approved_date: new Date().toISOString(),
          quality_approved_by: inspection.inspector,
          quality_notes: `Quality failed via inspection ${inspectionId} - quarantined`,
          inspection_id: inspectionId
        }, context)
      }

      // Create quarantine transaction
      if (inspection.workOrder) {
        await this.createStockTransaction({
          type: "outbound",
          product_id: inspection.workOrder.product_id,
          qty: inspection.workOrder.qty,
          workflow_trigger: "quality_rejected",
          reference_id: inspectionId,
          location: "quarantine",
          notes: `Quality failed by ${inspection.inspector} - moved to quarantine`
        }, context)
      }

      console.log(`⚠️ Quality-Inventory Integration: Inspection ${inspectionId} failed, stock quarantined`)

    } catch (error) {
      console.error(`❌ Quality-Inventory Integration Error:`, error)
      throw error
    }
  }

  /**
   * Creates stock lot with pending quality status (Professional ERP Standard)
   * Used when stock is first created and awaiting quality inspection
   */
  async createPendingStockLot(
    inspectionId: string,
    context: QualityInventoryContext
  ): Promise<void> {
    try {
      // Get quality inspection details with work order relationship
      const inspection = await db.query.qualityInspections.findFirst({
        where: and(
          eq(qualityInspections.id, inspectionId),
          eq(qualityInspections.company_id, context.companyId)
        ),
        with: {
          workOrder: {
            with: {
              product: true,
            },
          },
        },
      })

      if (!inspection?.workOrder) {
        throw new Error(`Quality inspection ${inspectionId} not found or missing work order`)
      }

      // Create stock lot with pending status
      const stockLotId = await this.createFinishedGoodsStockLot({
        product_id: inspection.workOrder.product_id,
        qty: inspection.workOrder.qty,
        work_order_id: inspection.work_order_id!,
        location: "finished_goods",
        quality_status: "pending", // Professional ERP: Start as pending
        inspection_id: inspectionId,
        quality_approved_by: undefined,
        quality_notes: `Stock lot created, awaiting quality inspection ${inspectionId}`
      }, context)

      console.log(`📋 Quality-Inventory Integration: Stock lot ${stockLotId} created with pending status for inspection ${inspectionId}`)

    } catch (error) {
      console.error(`❌ Quality-Inventory Integration Error:`, error)
      throw error
    }
  }

  /**
   * Update stock lot quality status with multi-tenant security
   */
  async updateStockLotQualityStatus(
    stockLotId: string,
    updates: StockLotUpdate,
    context: QualityInventoryContext
  ): Promise<void> {
    await db.update(stockLots)
      .set({
        quality_status: updates.quality_status,
        quality_approved_date: updates.quality_approved_date,
        quality_approved_by: updates.quality_approved_by,
        quality_notes: updates.quality_notes,
        inspection_id: updates.inspection_id,
      })
      .where(and(
        eq(stockLots.id, stockLotId),
        eq(stockLots.company_id, context.companyId)
      ))
  }

  /**
   * Marks stock lot as rejected (Professional ERP Standard)
   * Used when stock fails quality and cannot be reworked
   */
  async rejectStockLot(
    inspectionId: string,
    context: QualityInventoryContext
  ): Promise<void> {
    try {
      // Get quality inspection details
      const inspection = await db.query.qualityInspections.findFirst({
        where: and(
          eq(qualityInspections.id, inspectionId),
          eq(qualityInspections.company_id, context.companyId)
        ),
        with: {
          workOrder: {
            with: {
              product: true,
            },
          },
        },
      })

      if (!inspection?.workOrder) {
        throw new Error(`Quality inspection ${inspectionId} not found or missing work order`)
      }

      // Find related stock lots for this inspection
      const relatedStockLots = await db.query.stockLots.findMany({
        where: and(
          eq(stockLots.company_id, context.companyId),
          eq(stockLots.inspection_id, inspectionId)
        )
      })

      // Update existing stock lots to rejected status
      for (const stockLot of relatedStockLots) {
        await this.updateStockLotQualityStatus(stockLot.id, {
          quality_status: "rejected",
          quality_approved_date: new Date().toISOString(),
          quality_approved_by: inspection.inspector,
          quality_notes: `Stock rejected via inspection ${inspectionId} - marked for disposal`,
          inspection_id: inspectionId
        }, context)
      }

      console.log(`❌ Quality-Inventory Integration: Inspection ${inspectionId} rejected, stock marked for disposal`)

    } catch (error) {
      console.error(`❌ Quality-Inventory Integration Error:`, error)
      throw error
    }
  }

  /**
   * Create finished goods stock lot from quality approval
   */
  async createFinishedGoodsStockLot(
    data: {
      product_id: string
      qty: string
      work_order_id: string
      location: string
      quality_status: "pending" | "approved" | "rejected" | "quarantined"
      inspection_id?: string
      quality_approved_by?: string
      quality_notes?: string
    },
    context: QualityInventoryContext
  ): Promise<string> {
    const stockLotId = uid("lot")

    await db.insert(stockLots).values({
      id: stockLotId,
      company_id: context.companyId,
      product_id: data.product_id,
      qty: data.qty,
      location: data.location,
      lot_number: `LOT-${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}-${stockLotId.slice(-6)}`,
      status: "available",
      quality_status: data.quality_status,
      work_order_id: data.work_order_id,
      inspection_id: data.inspection_id,
      quality_approved_date: data.quality_status === "approved" ? new Date().toISOString() : undefined,
      quality_approved_by: data.quality_approved_by,
      quality_notes: data.quality_notes,
      created_at: new Date(),
    })

    return stockLotId
  }

  /**
   * Create stock transaction with workflow tracking
   */
  async createStockTransaction(
    data: StockTransactionData,
    context: QualityInventoryContext
  ): Promise<string> {
    const transactionId = uid("stxn")

    await db.insert(stockTxns).values({
      id: transactionId,
      company_id: context.companyId,
      type: data.type,
      product_id: data.product_id,
      qty: data.qty,
      reference: `${data.workflow_trigger}:${data.reference_id}`,
      workflow_trigger: data.workflow_trigger,
      reference_id: data.reference_id,
      location: data.location || "general",
      notes: data.notes,
      created_at: new Date(),
    })

    return transactionId
  }

  /**
   * Get stock lots by quality status with multi-tenant security
   */
  async getStockLotsByQualityStatus(
    qualityStatus: "pending" | "approved" | "rejected" | "quarantined",
    context: QualityInventoryContext
  ) {
    return await db.query.stockLots.findMany({
      where: and(
        eq(stockLots.company_id, context.companyId),
        eq(stockLots.quality_status, qualityStatus)
      ),
      with: {
        product: true,
        inspection: true,
        workOrder: true,
      },
      orderBy: (stockLots, { desc }) => [desc(stockLots.created_at)]
    })
  }

  /**
   * Get stock transactions by workflow trigger
   */
  async getStockTransactionsByWorkflow(
    workflowTrigger: string,
    context: QualityInventoryContext
  ) {
    return await db.query.stockTxns.findMany({
      where: and(
        eq(stockTxns.company_id, context.companyId),
        eq(stockTxns.workflow_trigger, workflowTrigger)
      ),
      with: {
        product: true,
      },
      orderBy: (stockTxns, { desc }) => [desc(stockTxns.created_at)]
    })
  }
}

// Export singleton instance
export const qualityInventoryIntegrationService = new QualityInventoryIntegrationService()
