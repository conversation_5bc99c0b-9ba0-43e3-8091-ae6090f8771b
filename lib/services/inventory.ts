import { BaseService, ServiceContext } from "./base"
import { stockLots, stockTxns } from "@/lib/schema-postgres"
import { uid } from "@/lib/db"
import { AuditResource, logInventoryOperation } from "@/lib/audit"
import { inboundSchema, outboundSchema } from "@/lib/validations"
import { sanitizeFormData } from "@/lib/sanitization"
import { BusinessLogicError, ErrorCode, createInsufficientStockError } from "@/lib/errors"
import { User } from "@/lib/auth"

// Inventory types
export interface StockLot {
  id: string
  productId: string
  qty: number
  location: string
  note?: string
  created_at: string
  product?: {
    id: string
    sku: string
    name: string
    unit: string
  }
}

export interface StockTransaction {
  id: string
  type: "inbound" | "outbound"
  productId: string
  qty: number
  location: string
  ref?: string
  created_at: string
  product?: {
    id: string
    sku: string
    name: string
    unit: string
  }
}

export interface InboundData {
  product_id: string
  qty: number
  location: string
  note?: string
}

export interface OutboundData {
  product_id: string
  qty: number
  location: string
  ref?: string
}

export class InventoryService {
  private context: ServiceContext

  constructor(context: ServiceContext) {
    this.context = context
  }

  // Inbound operations (receiving stock)
  async processInbound(data: InboundData, user: User): Promise<StockLot> {
    try {
      // Validate input data
      const sanitized = sanitizeFormData(data)
      const validation = inboundSchema.safeParse(sanitized)

      if (!validation.success) {
        throw new BusinessLogicError(
          ErrorCode.VALIDATION_ERROR,
          "Invalid inbound data",
          { issues: validation.error.issues }
        )
      }

      // Validate product exists
      const product = await this.context.db.query.products.findFirst({
        where: (products: any, { eq }: any) => eq(products.id, data.product_id)
      })

      if (!product) {
        throw new BusinessLogicError(
          ErrorCode.REFERENCE_NOT_FOUND,
          "Product not found"
        )
      }

      const lotId = uid("lot")
      const txnId = uid("txn")

      // Create stock lot and transaction in a single transaction
      await this.context.db.transaction(async (tx) => {
        // Create stock lot
        await tx.insert(stockLots).values({
          id: lotId,
          productId: data.product_id,
          qty: data.qty.toString(),
          location: data.location,
          note: data.note
        })

        // Create stock transaction
        await tx.insert(stockTxns).values({
          id: txnId,
          type: "inbound",
          productId: data.product_id,
          qty: data.qty.toString(),
          location: data.location,
          ref: lotId
        })
      })

      // Log inventory operation
      logInventoryOperation(
        user,
        "CREATE",
        AuditResource.STOCK_LOT,
        lotId,
        data.product_id,
        data.qty,
        data.location,
        this.context.ipAddress,
        this.context.userAgent
      )

      // Fetch and return the created lot
      const lot = await this.context.db.query.stockLots.findFirst({
        where: (lots: any, { eq }: any) => eq(lots.id, lotId),
        with: {
          product: true
        }
      })

      if (!lot) {
        throw new BusinessLogicError(
          ErrorCode.DATABASE_ERROR,
          "Failed to retrieve created stock lot"
        )
      }

      return {
        id: lot.id,
        productId: lot.productId,
        qty: parseFloat(lot.qty),
        location: lot.location,
        note: lot.note,
        created_at: lot.created_at,
        product: lot.product
      }
    } catch (error) {
      throw error
    }
  }

  // Outbound operations (shipping stock) - FIFO
  async processOutbound(data: OutboundData, user: User): Promise<void> {
    try {
      // Validate input data
      const sanitized = sanitizeFormData(data)
      const validation = outboundSchema.safeParse(sanitized)

      if (!validation.success) {
        throw new BusinessLogicError(
          ErrorCode.VALIDATION_ERROR,
          "Invalid outbound data",
          { issues: validation.error.issues }
        )
      }

      // Validate product exists
      const product = await this.context.db.query.products.findFirst({
        where: (products: any, { eq }: any) => eq(products.id, data.product_id)
      })

      if (!product) {
        throw new BusinessLogicError(
          ErrorCode.REFERENCE_NOT_FOUND,
          "Product not found"
        )
      }

      // Get available stock lots for this product and location (FIFO order)
      const availableLots = await this.context.db.query.stockLots.findMany({
        where: (lots: any, { eq, and, gt }: any) => and(
          eq(lots.productId, data.product_id),
          eq(lots.location, data.location),
          gt(lots.qty, "0")
        ),
        orderBy: (lots: any, { asc }: any) => [asc(lots.created_at)]
      })

      // Check if we have enough stock
      const totalAvailable = availableLots.reduce((sum, lot) => sum + parseFloat(lot.qty), 0)
      if (totalAvailable < data.qty) {
        throw createInsufficientStockError(product.name, totalAvailable, data.qty)
      }

      // Process outbound using FIFO
      let remaining = data.qty
      const txnId = uid("txn")

      await this.context.db.transaction(async (tx) => {
        for (const lot of availableLots) {
          if (remaining <= 0) break

          const lotQty = parseFloat(lot.qty)
          const takeQty = Math.min(remaining, lotQty)
          const newQty = lotQty - takeQty

          // Update lot quantity
          await tx
            .update(stockLots)
            .set({ qty: newQty.toString() })
            .where((lots: any, { eq }: any) => eq(lots.id, lot.id))

          remaining -= takeQty
        }

        // Create outbound transaction
        await tx.insert(stockTxns).values({
          id: txnId,
          type: "outbound",
          productId: data.product_id,
          qty: data.qty.toString(),
          location: data.location,
          ref: data.ref
        })
      })

      // Log inventory operation
      logInventoryOperation(
        user,
        "CREATE",
        AuditResource.STOCK_TRANSACTION,
        txnId,
        data.product_id,
        data.qty,
        data.location,
        this.context.ipAddress,
        this.context.userAgent
      )

      if (remaining > 0) {
        throw createInsufficientStockError(product.name, totalAvailable - remaining, data.qty)
      }
    } catch (error) {
      throw error
    }
  }

  // Get current stock levels for a product
  async getStockLevel(productId: string, location?: string): Promise<{
    totalStock: number
    stockLots: StockLot[]
  }> {
    try {
      const whereCondition = location
        ? (lots: any, { eq, and, gt }: any) => and(
          eq(lots.productId, productId),
          eq(lots.location, location),
          gt(lots.qty, "0")
        )
        : (lots: any, { eq, gt }: any) => and(
          eq(lots.productId, productId),
          gt(lots.qty, "0")
        )

      const lots = await this.context.db.query.stockLots.findMany({
        where: whereCondition,
        with: {
          product: true
        },
        orderBy: (lots: any, { asc }: any) => [asc(lots.created_at)]
      })

      const stockLots: StockLot[] = lots.map(lot => ({
        id: lot.id,
        productId: lot.productId,
        qty: parseFloat(lot.qty),
        location: lot.location,
        note: lot.note,
        created_at: lot.created_at,
        product: lot.product
      }))

      const totalStock = stockLots.reduce((sum, lot) => sum + lot.qty, 0)

      return {
        totalStock,
        stockLots
      }
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to get stock level"
      )
    }
  }

  // Get stock transactions for a product
  async getStockTransactions(
    productId?: string,
    location?: string,
    type?: "inbound" | "outbound"
  ): Promise<StockTransaction[]> {
    try {
      let whereConditions: any[] = []

      if (productId) {
        whereConditions.push((txns: any, { eq }: any) => eq(txns.productId, productId))
      }
      if (location) {
        whereConditions.push((txns: any, { eq }: any) => eq(txns.location, location))
      }
      if (type) {
        whereConditions.push((txns: any, { eq }: any) => eq(txns.type, type))
      }

      const whereCondition = whereConditions.length > 0
        ? (txns: any, { and }: any) => and(...whereConditions.map(cond => cond(txns, { eq: (a: any, b: any) => a === b })))
        : undefined

      const transactions = await this.context.db.query.stockTxns.findMany({
        where: whereCondition,
        with: {
          product: true
        },
        orderBy: (txns: any, { desc }: any) => [desc(txns.created_at)]
      })

      return transactions.map(txn => ({
        id: txn.id,
        type: txn.type as "inbound" | "outbound",
        productId: txn.productId,
        qty: parseFloat(txn.qty),
        location: txn.location,
        ref: txn.ref,
        created_at: txn.created_at,
        product: txn.product
      }))
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to get stock transactions"
      )
    }
  }

  // Get inventory summary by location
  async getInventorySummary(): Promise<{
    byLocation: Record<string, {
      totalProducts: number
      totalStock: number
      totalValue: number
    }>
    lowStockProducts: Array<{
      productId: string
      productName: string
      currentStock: number
      location: string
    }>
  }> {
    try {
      const lots = await this.context.db.query.stockLots.findMany({
        where: (lots: any, { gt }: any) => gt(lots.qty, "0"),
        with: {
          product: true
        }
      })

      const byLocation: Record<string, {
        totalProducts: number
        totalStock: number
        totalValue: number
      }> = {}

      const productStockByLocation: Record<string, Record<string, number>> = {}

      lots.forEach(lot => {
        const location = lot.location
        const qty = parseFloat(lot.qty)

        // Initialize location if not exists
        if (!byLocation[location]) {
          byLocation[location] = {
            totalProducts: 0,
            totalStock: 0,
            totalValue: 0
          }
        }

        // Track stock by product and location
        if (!productStockByLocation[location]) {
          productStockByLocation[location] = {}
        }
        if (!productStockByLocation[location][lot.productId]) {
          productStockByLocation[location][lot.productId] = 0
          byLocation[location].totalProducts++
        }
        productStockByLocation[location][lot.productId] += qty

        byLocation[location].totalStock += qty
        // Note: totalValue would need pricing data to calculate accurately
      })

      // Find low stock products (less than 10 units)
      const lowStockProducts: Array<{
        productId: string
        productName: string
        currentStock: number
        location: string
      }> = []

      Object.entries(productStockByLocation).forEach(([location, products]) => {
        Object.entries(products).forEach(([productId, stock]) => {
          if (stock < 10) { // Threshold for low stock
            const product = lots.find(lot => lot.productId === productId)?.product
            if (product) {
              lowStockProducts.push({
                productId,
                productName: product.name,
                currentStock: stock,
                location
              })
            }
          }
        })
      })

      return {
        byLocation,
        lowStockProducts
      }
    } catch (error) {
      throw new BusinessLogicError(
        ErrorCode.DATABASE_ERROR,
        "Failed to get inventory summary"
      )
    }
  }
}
