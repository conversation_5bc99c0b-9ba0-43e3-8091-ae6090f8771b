import { db, uid } from "@/lib/db"
import { inventoryTransactions, stockLots } from "@/lib/schema-postgres"
import { eq, and, desc, sum, sql } from "drizzle-orm"
import {
  TransactionType,
  TransactionStatus,
  ReferenceType,
  inventoryTransactionSchema,
  inboundTransactionSchema,
  outboundTransactionSchema,
  transferTransactionSchema,
  adjustmentTransactionSchema
} from "@/lib/validations"
import { z } from "zod"

/**
 * Manufacturing ERP - Comprehensive Inventory Transaction Service
 * 
 * Provides business logic for inventory transactions with:
 * - Multi-tenant security
 * - Transaction validation and processing
 * - Stock quantity updates
 * - Audit trail management
 * - Integration with work orders and quality systems
 */

export interface TransactionContext {
  companyId: string
  userId: string
}

export interface CreateTransactionData {
  transactionType: TransactionType
  stockLotId: string
  quantity: number
  unitCost?: number
  locationFrom?: string
  locationTo?: string
  referenceType?: ReferenceType
  referenceId?: string
  referenceNumber?: string
  notes?: string
  transactionDate?: Date
}

export interface TransactionSummary {
  totalTransactions: number
  totalInbound: number
  totalOutbound: number
  totalTransfers: number
  totalAdjustments: number
  pendingTransactions: number
  completedTransactions: number
}

export class InventoryTransactionService {
  /**
   * Create a new inventory transaction
   */
  static async createTransaction(
    data: CreateTransactionData,
    context: TransactionContext
  ): Promise<any> {
    // Validate input based on transaction type
    let validatedData: any

    switch (data.transactionType) {
      case TransactionType.INBOUND:
        validatedData = inboundTransactionSchema.parse({
          ...data,
          locationTo: data.locationTo || data.locationFrom
        })
        break
      case TransactionType.OUTBOUND:
        validatedData = outboundTransactionSchema.parse({
          ...data,
          locationFrom: data.locationFrom || data.locationTo
        })
        break
      case TransactionType.TRANSFER:
        validatedData = transferTransactionSchema.parse(data)
        break
      case TransactionType.ADJUSTMENT:
        validatedData = adjustmentTransactionSchema.parse(data)
        break
      default:
        throw new Error(`Invalid transaction type: ${data.transactionType}`)
    }

    // Verify stock lot exists and belongs to company
    const stockLot = await db.query.stockLots.findFirst({
      where: and(
        eq(stockLots.id, data.stockLotId),
        eq(stockLots.company_id, context.companyId)
      ),
      with: {
        product: true
      }
    })

    if (!stockLot) {
      throw new Error("Stock lot not found or access denied")
    }

    // Validate stock availability for outbound transactions
    if (data.transactionType === TransactionType.OUTBOUND) {
      const availableQuantity = await this.getAvailableQuantity(data.stockLotId, context.companyId)
      if (availableQuantity < data.quantity) {
        throw new Error(`Insufficient stock. Available: ${availableQuantity}, Requested: ${data.quantity}`)
      }
    }

    // Generate transaction number
    const transactionNumber = await this.generateTransactionNumber(data.transactionType)

    // Calculate total cost
    const totalCost = data.unitCost
      ? (data.unitCost * Math.abs(data.quantity)).toString()
      : undefined

    // Create transaction record
    const transactionData = {
      id: uid(),
      company_id: context.companyId,
      transaction_number: transactionNumber,
      transaction_type: data.transactionType,
      transaction_status: TransactionStatus.PENDING,
      stock_lot_id: data.stockLotId,
      quantity: data.quantity.toString(),
      unit_cost: data.unitCost?.toString(),
      total_cost: totalCost,
      location_from: data.locationFrom,
      location_to: data.locationTo,
      reference_type: data.referenceType,
      reference_id: data.referenceId,
      reference_number: data.referenceNumber,
      created_by: context.userId,
      notes: data.notes,
      transaction_date: data.transactionDate || new Date(),
    }

    const [newTransaction] = await db.insert(inventoryTransactions)
      .values(transactionData)
      .returning()

    // Auto-complete certain transaction types
    if (data.transactionType === TransactionType.ADJUSTMENT ||
      (data.referenceType === ReferenceType.WORK_ORDER && data.transactionType === TransactionType.INBOUND)) {
      await this.completeTransaction(newTransaction.id, context)
    }

    return newTransaction
  }

  /**
   * Complete a pending transaction and update stock quantities
   */
  static async completeTransaction(
    transactionId: string,
    context: TransactionContext
  ): Promise<any> {
    // Get transaction details
    const transaction = await db.query.inventoryTransactions.findFirst({
      where: and(
        eq(inventoryTransactions.id, transactionId),
        eq(inventoryTransactions.company_id, context.companyId)
      ),
      with: {
        stockLot: true
      }
    })

    if (!transaction) {
      throw new Error("Transaction not found")
    }

    if (transaction.transaction_status !== TransactionStatus.PENDING) {
      throw new Error(`Transaction is already ${transaction.transaction_status?.toLowerCase() || 'processed'}`)
    }

    // Update stock lot quantity based on transaction type
    await this.updateStockQuantities(transaction, context)

    // Mark transaction as completed
    const [completedTransaction] = await db.update(inventoryTransactions)
      .set({
        transaction_status: TransactionStatus.COMPLETED,
        approved_by: context.userId,
        approved_at: new Date(),
        updated_at: new Date()
      })
      .where(and(
        eq(inventoryTransactions.id, transactionId),
        eq(inventoryTransactions.company_id, context.companyId)
      ))
      .returning()

    return completedTransaction
  }

  /**
   * Update stock lot quantities based on transaction
   */
  private static async updateStockQuantities(
    transaction: any,
    context: TransactionContext
  ): Promise<void> {
    const currentQuantity = parseFloat(transaction.stockLot.qty)
    const transactionQuantity = parseFloat(transaction.quantity)
    let newQuantity: number

    switch (transaction.transaction_type) {
      case TransactionType.INBOUND:
        newQuantity = currentQuantity + transactionQuantity
        break
      case TransactionType.OUTBOUND:
        newQuantity = currentQuantity - transactionQuantity
        if (newQuantity < 0) {
          throw new Error("Insufficient stock for outbound transaction")
        }
        break
      case TransactionType.ADJUSTMENT:
        newQuantity = currentQuantity + transactionQuantity // Can be positive or negative
        break
      case TransactionType.TRANSFER:
        // For transfers, we'll handle this differently in a future enhancement
        // For now, treat as adjustment
        newQuantity = currentQuantity + transactionQuantity
        break
      default:
        throw new Error(`Unsupported transaction type: ${transaction.transaction_type}`)
    }

    // Update stock lot quantity
    await db.update(stockLots)
      .set({
        qty: newQuantity.toString()
      })
      .where(and(
        eq(stockLots.id, transaction.stock_lot_id),
        eq(stockLots.company_id, context.companyId)
      ))
  }

  /**
   * Get available quantity for a stock lot
   */
  static async getAvailableQuantity(stockLotId: string, companyId: string): Promise<number> {
    const stockLot = await db.query.stockLots.findFirst({
      where: and(
        eq(stockLots.id, stockLotId),
        eq(stockLots.company_id, companyId)
      )
    })

    return stockLot ? parseFloat(stockLot.qty) : 0
  }

  /**
   * Generate unique transaction number
   */
  private static async generateTransactionNumber(transactionType: TransactionType): Promise<string> {
    const prefix = transactionType.substring(0, 3).toUpperCase()
    const timestamp = Date.now()
    const random = uid().slice(0, 4).toUpperCase()
    return `${prefix}-${timestamp}-${random}`
  }

  /**
   * Validate transaction before processing
   */
  static async validateTransaction(
    data: CreateTransactionData,
    context: TransactionContext
  ): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = []

    // Check stock lot exists
    const stockLot = await db.query.stockLots.findFirst({
      where: and(
        eq(stockLots.id, data.stockLotId),
        eq(stockLots.company_id, context.companyId)
      )
    })

    if (!stockLot) {
      errors.push("Stock lot not found")
    }

    // Check stock availability for outbound transactions
    if (data.transactionType === TransactionType.OUTBOUND && stockLot) {
      const availableQuantity = parseFloat(stockLot.qty)
      if (availableQuantity < data.quantity) {
        errors.push(`Insufficient stock. Available: ${availableQuantity}, Requested: ${data.quantity}`)
      }
    }

    // Validate transfer locations
    if (data.transactionType === TransactionType.TRANSFER) {
      if (!data.locationFrom || !data.locationTo) {
        errors.push("Transfer transactions require both source and destination locations")
      }
      if (data.locationFrom === data.locationTo) {
        errors.push("Source and destination locations must be different")
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Get transaction summary for a company
   */
  static async getTransactionSummary(companyId: string): Promise<TransactionSummary> {
    const transactions = await db.query.inventoryTransactions.findMany({
      where: eq(inventoryTransactions.company_id, companyId)
    })

    const summary: TransactionSummary = {
      totalTransactions: transactions.length,
      totalInbound: transactions.filter(t => t.transaction_type === TransactionType.INBOUND).length,
      totalOutbound: transactions.filter(t => t.transaction_type === TransactionType.OUTBOUND).length,
      totalTransfers: transactions.filter(t => t.transaction_type === TransactionType.TRANSFER).length,
      totalAdjustments: transactions.filter(t => t.transaction_type === TransactionType.ADJUSTMENT).length,
      pendingTransactions: transactions.filter(t => t.transaction_status === TransactionStatus.PENDING).length,
      completedTransactions: transactions.filter(t => t.transaction_status === TransactionStatus.COMPLETED).length,
    }

    return summary
  }

  /**
   * Create audit trail entry for transaction
   */
  static async auditTrail(
    transactionId: string,
    action: string,
    details: any,
    context: TransactionContext
  ): Promise<void> {
    // This would integrate with your existing audit system
    console.log(`AUDIT: Transaction ${transactionId} - ${action}`, {
      userId: context.userId,
      companyId: context.companyId,
      timestamp: new Date().toISOString(),
      details
    })
  }

  /**
   * Get transaction history for a stock lot
   */
  static async getTransactionHistory(
    stockLotId: string,
    companyId: string
  ): Promise<any[]> {
    return await db.query.inventoryTransactions.findMany({
      where: and(
        eq(inventoryTransactions.stock_lot_id, stockLotId),
        eq(inventoryTransactions.company_id, companyId)
      ),
      orderBy: [desc(inventoryTransactions.created_at)],
      with: {
        stockLot: {
          with: {
            product: true
          }
        }
      }
    })
  }

  /**
   * Cancel a pending transaction
   */
  static async cancelTransaction(
    transactionId: string,
    reason: string,
    context: TransactionContext
  ): Promise<any> {
    const [cancelledTransaction] = await db.update(inventoryTransactions)
      .set({
        transaction_status: TransactionStatus.CANCELLED,
        notes: reason,
        updated_at: new Date()
      })
      .where(and(
        eq(inventoryTransactions.id, transactionId),
        eq(inventoryTransactions.company_id, context.companyId),
        eq(inventoryTransactions.transaction_status, TransactionStatus.PENDING)
      ))
      .returning()

    if (!cancelledTransaction) {
      throw new Error("Transaction not found or cannot be cancelled")
    }

    await this.auditTrail(transactionId, "CANCELLED", { reason }, context)
    return cancelledTransaction
  }
}
