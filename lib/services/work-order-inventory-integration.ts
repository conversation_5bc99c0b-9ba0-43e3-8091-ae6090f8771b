/**
 * Work Order-Inventory Integration Service
 * 
 * Handles automated workflow integration between Work Orders and Inventory modules
 * Following established patterns with multi-tenant security and zero-breaking-changes philosophy
 */

import { db, uid } from "@/lib/db"
import { stockLots, stockTxns, inventoryTransactions, workOrders, qualityInspections } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { InventoryTransactionService } from "./inventory-transactions"
import { TransactionType, ReferenceType } from "@/lib/validations"

export interface WorkOrderInventoryContext {
  companyId: string
  userId?: string
}

export interface FinishedGoodsLotData {
  product_id: string
  qty: string
  work_order_id: string
  location: string
  lot_number?: string
  quality_status: "pending" | "approved" | "rejected" | "quarantined"
}

/**
 * Work Order-Inventory Integration Service
 * Handles automated workflows between Work Orders and Inventory modules
 */
export class WorkOrderInventoryIntegrationService {

  /**
   * Triggered when work order status changes to 'completed'
   * Automatically creates finished goods inventory and triggers quality inspection
   */
  async onWorkOrderCompleted(
    workOrderId: string,
    context: WorkOrderInventoryContext
  ): Promise<void> {
    console.log(`🚀 Work Order-Inventory Integration: Starting integration for work order ${workOrderId}`)
    console.log(`🔐 Context: Company ID ${context.companyId}, User ID ${context.userId}`)

    try {
      // 1. Get work order details with multi-tenant security
      console.log(`📋 Step 1: Fetching work order details for ${workOrderId}`)
      const workOrder = await db.query.workOrders.findFirst({
        where: and(
          eq(workOrders.id, workOrderId),
          eq(workOrders.company_id, context.companyId)
        ),
        with: {
          product: true,
          salesContract: true,
        }
      })

      if (!workOrder) {
        const error = `Work order ${workOrderId} not found or access denied for company ${context.companyId}`
        console.error(`❌ Integration Error: ${error}`)
        throw new Error(error)
      }

      console.log(`✅ Work order found: ${workOrder.number} (${workOrder.status})`)

      if (!workOrder.product) {
        const error = `Product not found for work order ${workOrderId}`
        console.error(`❌ Integration Error: ${error}`)
        throw new Error(error)
      }

      console.log(`✅ Product found: ${workOrder.product.name} (SKU: ${workOrder.product.sku})`)
      console.log(`📊 Product inspection required: ${workOrder.product.inspection_required}`)

      // 2. Check if quality inspection is required for this product
      console.log(`📋 Step 2: Checking quality requirements`)
      const requiresInspection = workOrder.product.inspection_required === "true"

      // 3. Determine initial quality status based on inspection requirement
      const initialQualityStatus = requiresInspection ? "pending" : "approved"

      console.log(`🔍 Quality Decision: Product ${workOrder.product.name} ${requiresInspection ? 'REQUIRES' : 'DOES NOT REQUIRE'} quality inspection`)
      console.log(`📊 Initial quality status will be: ${initialQualityStatus}`)

      // 4. ✅ PROFESSIONAL ERP: Check for existing stock lots to prevent duplicates
      console.log(`📋 Step 3: Checking for existing stock lots for work order ${workOrderId}`)
      const existingStockLots = await db.query.stockLots.findMany({
        where: and(
          eq(stockLots.work_order_id, workOrderId),
          eq(stockLots.company_id, context.companyId)
        )
      })

      if (existingStockLots.length > 0) {
        console.log(`⚠️ Stock lots already exist for work order ${workOrderId}:`)
        existingStockLots.forEach(lot => {
          console.log(`   - Lot ${lot.id}: ${lot.lot_number} (${lot.quality_status})`)
        })
        console.log(`✅ Work Order-Inventory Integration: Skipping stock lot creation - already exists`)
        return // Exit early to prevent duplicates
      }

      // 5. Create finished goods stock lot
      console.log(`📋 Step 4: Creating finished goods stock lot`)
      const lotNumber = this.generateLotNumber(workOrder.product.sku)
      console.log(`🏷️ Generated lot number: ${lotNumber}`)

      const finishedGoodsLotId = await this.createFinishedGoodsLot({
        product_id: workOrder.product_id,
        qty: workOrder.qty,
        work_order_id: workOrderId,
        location: "finished_goods",
        lot_number: lotNumber,
        quality_status: initialQualityStatus
      }, context)

      console.log(`✅ Stock lot created with ID: ${finishedGoodsLotId}`)

      // 6. Create inbound stock transaction for finished goods (legacy)
      console.log(`📋 Step 5: Creating legacy stock transaction`)
      const transactionId = await this.createStockTransaction({
        type: "inbound",
        product_id: workOrder.product_id,
        qty: workOrder.qty,
        workflow_trigger: "work_order_completed",
        reference_id: workOrderId,
        location: "finished_goods",
        notes: requiresInspection
          ? `Finished goods created from work order ${workOrder.number} - awaiting quality inspection`
          : `Finished goods created from work order ${workOrder.number} - no inspection required`
      }, context)

      console.log(`✅ Legacy stock transaction created with ID: ${transactionId}`)

      // 7. Create comprehensive inventory transaction
      console.log(`📋 Step 6: Creating comprehensive inventory transaction`)
      const inventoryTransactionId = await this.createInventoryTransaction({
        stockLotId: finishedGoodsLotId,
        transactionType: TransactionType.INBOUND,
        quantity: parseFloat(workOrder.qty),
        locationTo: "finished_goods",
        referenceType: ReferenceType.WORK_ORDER,
        referenceId: workOrderId,
        referenceNumber: workOrder.number,
        notes: requiresInspection
          ? `Finished goods from work order ${workOrder.number} - pending quality inspection`
          : `Finished goods from work order ${workOrder.number} - approved (no inspection required)`
      }, context)

      console.log(`✅ Inventory transaction created with ID: ${inventoryTransactionId}`)

      // 8. Create quality inspection only if required
      if (requiresInspection) {
        console.log(`📋 Step 7: Creating quality inspection (required)`)
        const inspectionId = await this.createQualityInspection({
          work_order_id: workOrderId,
          stock_lot_id: finishedGoodsLotId,
          inspection_type: "final",
          inspector: "Auto-generated",
          inspection_date: new Date().toISOString().split('T')[0],
          status: "pending",
          notes: `Final inspection required for work order ${workOrder.number}`
        }, context)

        console.log(`✅ Quality inspection created with ID: ${inspectionId}`)

        // Link stock lot to quality inspection
        console.log(`📋 Step 8: Linking stock lot to quality inspection`)
        await db.update(stockLots)
          .set({ inspection_id: inspectionId })
          .where(and(
            eq(stockLots.id, finishedGoodsLotId),
            eq(stockLots.company_id, context.companyId)
          ))

        console.log(`✅ Work Order-Inventory Integration: Work order ${workOrder.number} completed, finished goods created with pending quality inspection`)
      } else {
        console.log(`📋 Step 6: Skipping quality inspection (not required)`)
        console.log(`✅ Work Order-Inventory Integration: Work order ${workOrder.number} completed, finished goods approved immediately (no inspection required)`)
      }

      // 5. TODO: Consume raw materials (if BOM exists)
      // await this.consumeRawMaterials(workOrder, context)

      console.log(`🎉 Work Order-Inventory Integration: SUCCESSFULLY COMPLETED for work order ${workOrderId}`)
      console.log(`📊 Summary: Stock lot ${finishedGoodsLotId} created with ${initialQualityStatus} status`)

    } catch (error) {
      console.error(`🚨 CRITICAL: Work Order-Inventory Integration FAILED for work order ${workOrderId}`)
      console.error(`❌ Error Type: ${error instanceof Error ? error.constructor.name : typeof error}`)
      console.error(`❌ Error Message: ${error instanceof Error ? error.message : String(error)}`)
      console.error(`❌ Error Stack:`, error instanceof Error ? error.stack : 'No stack trace available')
      console.error(`🔐 Context: Company ID ${context.companyId}, User ID ${context.userId}`)

      // Re-throw to ensure the error is properly handled upstream
      throw error
    }
  }

  /**
   * Create finished goods stock lot
   */
  async createFinishedGoodsLot(
    data: FinishedGoodsLotData,
    context: WorkOrderInventoryContext
  ): Promise<string> {
    try {
      console.log(`📦 Creating stock lot for product ${data.product_id}`)
      console.log(`📊 Stock lot data:`, {
        product_id: data.product_id,
        qty: data.qty,
        location: data.location,
        lot_number: data.lot_number,
        quality_status: data.quality_status,
        work_order_id: data.work_order_id
      })

      const stockLotId = uid("lot")
      console.log(`🆔 Generated stock lot ID: ${stockLotId}`)

      await db.insert(stockLots).values({
        id: stockLotId,
        company_id: context.companyId,
        product_id: data.product_id,
        qty: data.qty,
        location: data.location,
        lot_number: data.lot_number,
        status: "available",
        quality_status: data.quality_status,
        work_order_id: data.work_order_id,
        created_at: new Date(),
      })

      console.log(`✅ Stock lot ${stockLotId} successfully inserted into database`)
      return stockLotId

    } catch (error) {
      console.error(`❌ Failed to create stock lot:`, error)
      console.error(`📊 Failed data:`, data)
      throw error
    }
  }

  /**
   * Create stock transaction with workflow tracking
   */
  async createStockTransaction(
    data: {
      type: "inbound" | "outbound"
      product_id: string
      qty: string
      workflow_trigger: string
      reference_id: string
      location?: string
      notes?: string
    },
    context: WorkOrderInventoryContext
  ): Promise<string> {
    try {
      console.log(`💰 Creating stock transaction: ${data.type} for product ${data.product_id}`)
      console.log(`📊 Transaction data:`, data)

      const transactionId = uid("stxn")
      console.log(`🆔 Generated transaction ID: ${transactionId}`)

      await db.insert(stockTxns).values({
        id: transactionId,
        company_id: context.companyId,
        type: data.type,
        product_id: data.product_id,
        qty: data.qty,
        reference: `${data.workflow_trigger}:${data.reference_id}`,
        workflow_trigger: data.workflow_trigger,
        reference_id: data.reference_id,
        location: data.location || "general",
        notes: data.notes,
        created_at: new Date(),
      })

      console.log(`✅ Stock transaction ${transactionId} successfully inserted into database`)
      return transactionId

    } catch (error) {
      console.error(`❌ Failed to create stock transaction:`, error)
      console.error(`📊 Failed data:`, data)
      throw error
    }
  }

  /**
   * Create quality inspection for finished goods
   */
  async createQualityInspection(
    data: {
      work_order_id: string
      stock_lot_id: string
      inspection_type: string
      inspector: string
      inspection_date: string
      status: string
      notes?: string
    },
    context: WorkOrderInventoryContext
  ): Promise<string> {
    const inspectionId = uid("qi")

    await db.insert(qualityInspections).values({
      id: inspectionId,
      company_id: context.companyId,
      work_order_id: data.work_order_id,
      inspection_type: data.inspection_type,
      inspector: data.inspector,
      inspection_date: data.inspection_date,
      status: data.status,
      notes: data.notes,
      created_at: new Date(),
    })

    // Update stock lot with inspection reference
    await db.update(stockLots)
      .set({
        inspection_id: inspectionId
      })
      .where(and(
        eq(stockLots.id, data.stock_lot_id),
        eq(stockLots.company_id, context.companyId)
      ))

    return inspectionId
  }

  /**
   * Generate lot number for finished goods
   */
  generateLotNumber(productSku: string): string {
    const date = new Date()
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '')
    const timeStr = date.toTimeString().slice(0, 5).replace(':', '')
    return `${productSku}-${dateStr}-${timeStr}`
  }

  /**
   * Get finished goods by work order
   */
  async getFinishedGoodsByWorkOrder(
    workOrderId: string,
    context: WorkOrderInventoryContext
  ) {
    return await db.query.stockLots.findMany({
      where: and(
        eq(stockLots.company_id, context.companyId),
        eq(stockLots.work_order_id, workOrderId)
      ),
      with: {
        product: true,
        inspection: true,
        workOrder: true,
      },
      orderBy: (stockLots, { desc }) => [desc(stockLots.created_at)]
    })
  }

  /**
   * Get work order completion statistics
   */
  async getWorkOrderCompletionStats(context: WorkOrderInventoryContext) {
    const completedWorkOrders = await db.query.workOrders.findMany({
      where: and(
        eq(workOrders.company_id, context.companyId),
        eq(workOrders.status, "completed")
      ),
      with: {
        stockLots: true,
      }
    })

    const stats = {
      totalCompleted: completedWorkOrders.length,
      withFinishedGoods: completedWorkOrders.filter(wo => wo.stockLots.length > 0).length,
      pendingQuality: 0,
      approvedQuality: 0,
    }

    // Calculate quality status statistics
    for (const workOrder of completedWorkOrders) {
      for (const stockLot of workOrder.stockLots) {
        if (stockLot.quality_status === "pending") stats.pendingQuality++
        if (stockLot.quality_status === "approved") stats.approvedQuality++
      }
    }

    return stats
  }

  /**
   * Create comprehensive inventory transaction using the new transaction system
   */
  async createInventoryTransaction(
    data: {
      stockLotId: string
      transactionType: TransactionType
      quantity: number
      unitCost?: number
      locationFrom?: string
      locationTo?: string
      referenceType?: ReferenceType
      referenceId?: string
      referenceNumber?: string
      notes?: string
    },
    context: WorkOrderInventoryContext
  ): Promise<string> {
    try {
      console.log(`💰 Creating comprehensive inventory transaction: ${data.transactionType} for stock lot ${data.stockLotId}`)
      console.log(`📊 Transaction data:`, data)

      const transaction = await InventoryTransactionService.createTransaction({
        transactionType: data.transactionType,
        stockLotId: data.stockLotId,
        quantity: data.quantity,
        unitCost: data.unitCost,
        locationFrom: data.locationFrom,
        locationTo: data.locationTo,
        referenceType: data.referenceType,
        referenceId: data.referenceId,
        referenceNumber: data.referenceNumber,
        notes: data.notes,
        transactionDate: new Date()
      }, {
        companyId: context.companyId,
        userId: context.userId || 'system'
      })

      console.log(`✅ Comprehensive inventory transaction ${transaction.id} successfully created`)
      return transaction.id

    } catch (error) {
      console.error(`❌ Failed to create comprehensive inventory transaction:`, error)
      console.error(`📊 Failed data:`, data)
      throw error
    }
  }

  /**
   * TODO: Consume raw materials based on Bill of Materials (BOM)
   * This would be implemented when BOM functionality is added
   */
  async consumeRawMaterials(
    workOrder: any,
    context: WorkOrderInventoryContext
  ): Promise<void> {
    // Future implementation:
    // 1. Get BOM for the product
    // 2. Calculate required raw material quantities
    // 3. Allocate stock using FIFO method
    // 4. Create outbound transactions for consumed materials
    // 5. Update raw material stock levels

    console.log(`📋 Raw material consumption for work order ${workOrder.id} - TODO: Implement BOM integration`)
  }
}

// Export singleton instance
export const workOrderInventoryIntegrationService = new WorkOrderInventoryIntegrationService()
