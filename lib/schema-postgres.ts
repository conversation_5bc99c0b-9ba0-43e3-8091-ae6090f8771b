import { relations } from "drizzle-orm"
import { index, pgTable, text, timestamp, boolean } from "drizzle-orm/pg-core"

// BASIC ENTITIES
export const customers = pgTable("customers", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  name: text("name").notNull(),
  contact_name: text("contact_name"),
  contact_phone: text("contact_phone"),
  contact_email: text("contact_email"),
  address: text("address"),
  tax_id: text("tax_id"),
  bank: text("bank"),
  incoterm: text("incoterm"),
  payment_term: text("payment_term"),
  status: text("status").default("active"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("customers_company_id_idx").on(table.company_id),
}))

export const suppliers = pgTable("suppliers", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  name: text("name").notNull(),
  contact_name: text("contact_name"),
  contact_phone: text("contact_phone"),
  contact_email: text("contact_email"),
  address: text("address"),
  tax_id: text("tax_id"),
  bank: text("bank"),
  status: text("status").default("active"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("suppliers_company_id_idx").on(table.company_id),
}))

// COMPANY PROFILE - Auth0 Integration
export const companies = pgTable("companies", {
  id: text("id").primaryKey(),
  auth0_user_id: text("auth0_user_id").notNull().unique(), // Auth0 user ID (sub claim)

  // Basic Company Information
  name: text("name").notNull(),
  legal_name: text("legal_name"), // Full legal company name
  registration_number: text("registration_number"), // Business registration number
  tax_id: text("tax_id"), // Tax identification number
  vat_number: text("vat_number"), // VAT registration number

  // Contact Information
  email: text("email"),
  phone: text("phone"),
  website: text("website"),

  // Address Information
  address_line1: text("address_line1"),
  address_line2: text("address_line2"),
  city: text("city"),
  state_province: text("state_province"),
  postal_code: text("postal_code"),
  country: text("country"),

  // Business Information
  industry: text("industry"),
  business_type: text("business_type"), // "manufacturer", "trader", "service", etc.
  employee_count: text("employee_count"),
  annual_revenue: text("annual_revenue"),

  // Banking Information
  bank_name: text("bank_name"),
  bank_account: text("bank_account"),
  bank_swift: text("bank_swift"),
  bank_address: text("bank_address"),
  bank_iban: text("bank_iban"),

  // Settings and Preferences
  currency: text("currency").default("USD"),
  timezone: text("timezone").default("UTC"),
  date_format: text("date_format").default("YYYY-MM-DD"),
  language: text("language").default("en"),

  // Trade Information
  export_license: text("export_license"),
  customs_code: text("customs_code"),
  preferred_incoterms: text("preferred_incoterms").default("FOB"),
  preferred_payment_terms: text("preferred_payment_terms").default("30 days"),

  // System Fields
  status: text("status").default("active"), // "active", "suspended", "pending"
  onboarding_completed: text("onboarding_completed").default("false"),
  onboarding_step: text("onboarding_step").default("basic_info"),
  subscription_plan: text("subscription_plan").default("free"), // "free", "basic", "premium", "enterprise"
  subscription_status: text("subscription_status").default("active"),
  trial_ends_at: timestamp("trial_ends_at", { withTimezone: true }),

  // Audit Fields
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  last_login_at: timestamp("last_login_at", { withTimezone: true }),
}, (table) => ({
  auth0UserIdIdx: index("companies_auth0_user_id_idx").on(table.auth0_user_id),
  nameIdx: index("companies_name_idx").on(table.name),
}))

export const products = pgTable("products", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  sku: text("sku").notNull(),
  name: text("name").notNull(),
  unit: text("unit").notNull(),
  hs_code: text("hs_code"),
  origin: text("origin"),
  package: text("package"),
  image: text("image"),
  category: text("category"),
  description: text("description"),
  price: text("price"),
  inspection_required: text("inspection_required").default("false"),
  quality_tolerance: text("quality_tolerance"),
  quality_notes: text("quality_notes"),
  status: text("status").default("active"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("products_company_id_idx").on(table.company_id),
  skuCompanyIdx: index("products_sku_company_idx").on(table.sku, table.company_id), // Ensure SKU uniqueness per company
}))

export const samples = pgTable("samples", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  code: text("code").notNull(),
  name: text("name").notNull(),
  date: text("date").notNull(),
  status: text("status").default("active"),

  // ✅ BIDIRECTIONAL SAMPLE WORKFLOW FIELDS
  sample_direction: text("sample_direction").notNull().default("outbound"), // "outbound" | "inbound" | "internal"
  sample_purpose: text("sample_purpose"), // "customer_evaluation" | "manufacturing_quote" | "material_testing" | "quality_control"
  sender_type: text("sender_type"), // "customer" | "supplier" | "internal" | null
  receiver_type: text("receiver_type"), // "customer" | "supplier" | "internal" | null

  // ✅ ENHANCED RELATIONSHIP FIELDS (Context-Aware)
  customer_id: text("customer_id").references(() => customers.id), // Customer involved (sender OR receiver)
  product_id: text("product_id").references(() => products.id),
  supplier_id: text("supplier_id").references(() => suppliers.id), // Supplier involved (sender OR receiver)
  // work_order_id: text("work_order_id").references(() => workOrders.id), // TODO: Enable after database migration

  // ✅ APPROVAL WORKFLOW FIELDS
  sample_type: text("sample_type").default("development"), // "development", "production", "quality", "prototype"
  approval_status: text("approval_status").default("pending"), // "pending", "approved", "rejected", "revision_required"
  approved_by: text("approved_by"),
  approved_date: text("approved_date"),
  rejection_reason: text("rejection_reason"),

  // ✅ INBOUND SAMPLE TRACKING FIELDS
  received_date: text("received_date"), // When inbound sample was received
  testing_status: text("testing_status").default("not_started"), // "not_started" | "in_progress" | "completed" | "failed"
  testing_results: text("testing_results"), // QC/analysis results for inbound samples
  quote_requested: boolean("quote_requested").default(false), // Whether customer requested manufacturing quote
  quote_provided: boolean("quote_provided").default(false), // Whether we provided quote

  // ✅ BUSINESS SPECIFICATION FIELDS
  notes: text("notes"),
  quantity: text("quantity"),
  unit: text("unit"),
  specifications: text("specifications"), // JSON field for technical specifications
  quality_requirements: text("quality_requirements"),
  delivery_date: text("delivery_date"),
  priority: text("priority").default("normal"), // "low", "normal", "high", "urgent"

  // ✅ COMMERCIAL FIELDS
  cost: text("cost"),
  currency: text("currency").default("USD"),

  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  // ✅ PERFORMANCE INDEXES FOR ALL RELATIONSHIPS
  companyIdIdx: index("samples_company_id_idx").on(table.company_id),
  customerIdIdx: index("samples_customer_id_idx").on(table.customer_id),
  productIdIdx: index("samples_product_id_idx").on(table.product_id),
  supplierIdIdx: index("samples_supplier_id_idx").on(table.supplier_id),
  // workOrderIdIdx: index("samples_work_order_id_idx").on(table.work_order_id), // TODO: Enable after database migration

  // ✅ WORKFLOW AND CLASSIFICATION INDEXES
  sampleDirectionIdx: index("samples_direction_idx").on(table.sample_direction),
  approvalStatusIdx: index("samples_approval_status_idx").on(table.approval_status),
  testingStatusIdx: index("samples_testing_status_idx").on(table.testing_status),
  sampleTypeIdx: index("samples_sample_type_idx").on(table.sample_type),
  priorityIdx: index("samples_priority_idx").on(table.priority),
}))

export const salesContracts = pgTable("sales_contracts", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  customer_id: text("customer_id").notNull().references(() => customers.id),
  template_id: text("template_id").references(() => contractTemplates.id),
  date: text("date").notNull(),
  currency: text("currency"),
  status: text("status").default("draft"),
  content: text("content"), // Rich text contract content
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("sales_contracts_company_id_idx").on(table.company_id),
}))

export const salesContractItems = pgTable("sales_contract_items", {
  id: text("id").primaryKey(),
  contract_id: text("contract_id").notNull().references(() => salesContracts.id),
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  price: text("price").notNull(),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
})

export const purchaseContracts = pgTable("purchase_contracts", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  supplier_id: text("supplier_id").notNull().references(() => suppliers.id),
  template_id: text("template_id").references(() => contractTemplates.id),
  date: text("date").notNull(),
  currency: text("currency"),
  status: text("status").default("draft"),
  content: text("content"), // Rich text contract content
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("purchase_contracts_company_id_idx").on(table.company_id),
}))

export const purchaseContractItems = pgTable("purchase_contract_items", {
  id: text("id").primaryKey(),
  contract_id: text("contract_id").notNull().references(() => purchaseContracts.id),
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  price: text("price").notNull(),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
})

// Contract Templates Schema
export const contractTemplates = pgTable("contract_templates", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  name: text("name").notNull(),
  type: text("type").notNull(), // 'sales' | 'purchase'
  content: text("content").notNull(), // Rich text template content

  // Template metadata
  currency: text("currency"),
  payment_terms: text("payment_terms"),
  delivery_terms: text("delivery_terms"),
  language: text("language").default("en"),
  version: text("version").default("1"),
  description: text("description"),
  is_default: text("is_default").default("false"), // "true" | "false"
  is_active: text("is_active").default("true"),
  status: text("status").default("active"), // "active" | "inactive" | "archived"

  // Audit fields
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("contract_templates_company_id_idx").on(table.company_id),
}))

export const workOrders = pgTable("work_orders", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  sales_contract_id: text("sales_contract_id").references(() => salesContracts.id),
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  due_date: text("due_date"),
  status: text("status").default("pending"),
  priority: text("priority").default("normal"), // "low", "normal", "high", "urgent"
  notes: text("notes"), // Production notes and instructions
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("work_orders_company_id_idx").on(table.company_id),
}))

export const workOperations = pgTable("work_operations", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  work_order_id: text("work_order_id").notNull().references(() => workOrders.id),
  operation_name: text("operation_name").notNull(),
  sequence: text("sequence").notNull(),
  status: text("status").default("pending"), // "pending", "in_progress", "completed", "skipped"
  start_time: timestamp("start_time", { withTimezone: true }),
  end_time: timestamp("end_time", { withTimezone: true }),
  notes: text("notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("work_operations_company_id_idx").on(table.company_id),
  workOrderIdIdx: index("work_operations_work_order_id_idx").on(table.work_order_id),
}))

export const stockLots = pgTable("stock_lots", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  location: text("location").notNull(),
  lot_number: text("lot_number"),
  expiry_date: text("expiry_date"),
  status: text("status").default("available"),

  // ✅ QUALITY CONTROL INTEGRATION FIELDS
  quality_status: text("quality_status").default("pending"), // "pending", "approved", "rejected", "quarantined"
  inspection_id: text("inspection_id").references(() => qualityInspections.id),
  quality_approved_date: text("quality_approved_date"),
  quality_approved_by: text("quality_approved_by"),
  quality_notes: text("quality_notes"),

  // ✅ WORK ORDER INTEGRATION FIELDS
  work_order_id: text("work_order_id").references(() => workOrders.id),

  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("stock_lots_company_id_idx").on(table.company_id),
  qualityStatusIdx: index("stock_lots_quality_status_idx").on(table.quality_status),
  inspectionIdIdx: index("stock_lots_inspection_id_idx").on(table.inspection_id),
  workOrderIdIdx: index("stock_lots_work_order_id_idx").on(table.work_order_id),
}))

export const stockTxns = pgTable("stock_txns", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  type: text("type").notNull(), // "inbound" or "outbound"
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  reference: text("reference"),

  // ✅ WORKFLOW AUTOMATION TRACKING FIELDS
  workflow_trigger: text("workflow_trigger"), // "quality_approved", "work_order_completed", "manual", "contract_approved"
  reference_id: text("reference_id"), // quality_inspection_id, work_order_id, sales_contract_id, etc.
  location: text("location"), // "raw_materials", "finished_goods", "work_in_progress", "quarantine"
  notes: text("notes"), // Additional transaction notes

  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("stock_txns_company_id_idx").on(table.company_id),
  workflowTriggerIdx: index("stock_txns_workflow_trigger_idx").on(table.workflow_trigger),
  referenceIdIdx: index("stock_txns_reference_id_idx").on(table.reference_id),
}))

// ✅ COMPREHENSIVE INVENTORY TRANSACTIONS ENGINE
export const inventoryTransactions = pgTable("inventory_transactions", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company

  // ✅ TRANSACTION CORE FIELDS
  transaction_number: text("transaction_number").notNull(), // Auto-generated unique transaction number
  transaction_type: text("transaction_type").notNull(), // INBOUND, OUTBOUND, TRANSFER, ADJUSTMENT
  transaction_status: text("transaction_status").default("PENDING"), // PENDING, COMPLETED, CANCELLED

  // ✅ STOCK LOT REFERENCE
  stock_lot_id: text("stock_lot_id").notNull().references(() => stockLots.id),

  // ✅ QUANTITY AND COST TRACKING
  quantity: text("quantity").notNull(), // Transaction quantity (positive for inbound, negative for outbound)
  unit_cost: text("unit_cost"), // Cost per unit for this transaction
  total_cost: text("total_cost"), // Total cost (quantity * unit_cost)

  // ✅ LOCATION TRACKING (for transfers)
  location_from: text("location_from"), // Source location for transfers
  location_to: text("location_to"), // Destination location for transfers/receipts

  // ✅ REFERENCE AND WORKFLOW INTEGRATION
  reference_type: text("reference_type"), // "work_order", "quality_inspection", "purchase_receipt", "sales_shipment", "manual"
  reference_id: text("reference_id"), // ID of the referenced document
  reference_number: text("reference_number"), // Human-readable reference number

  // ✅ AUDIT AND TRACKING
  created_by: text("created_by").notNull(), // User who created the transaction
  approved_by: text("approved_by"), // User who approved the transaction (if required)
  approved_at: timestamp("approved_at", { withTimezone: true }), // Approval timestamp
  notes: text("notes"), // Additional transaction notes

  // ✅ TIMESTAMPS
  transaction_date: timestamp("transaction_date", { withTimezone: true }).notNull(), // When transaction occurred
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("inventory_transactions_company_id_idx").on(table.company_id),
  stockLotIdIdx: index("inventory_transactions_stock_lot_id_idx").on(table.stock_lot_id),
  transactionTypeIdx: index("inventory_transactions_type_idx").on(table.transaction_type),
  transactionStatusIdx: index("inventory_transactions_status_idx").on(table.transaction_status),
  referenceIdx: index("inventory_transactions_reference_idx").on(table.reference_type, table.reference_id),
  transactionDateIdx: index("inventory_transactions_date_idx").on(table.transaction_date),
  transactionNumberIdx: index("inventory_transactions_number_idx").on(table.transaction_number),
}))

export const declarations = pgTable("declarations", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  status: text("status").default("draft"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("declarations_company_id_idx").on(table.company_id),
}))

export const declarationItems = pgTable("declaration_items", {
  id: text("id").primaryKey(),
  declaration_id: text("declaration_id").notNull().references(() => declarations.id),
  product_id: text("product_id").notNull().references(() => products.id),
  qty: text("qty").notNull(),
  // Quality Integration Fields
  quality_inspection_id: text("quality_inspection_id").references(() => qualityInspections.id),
  quality_status: text("quality_status").default("pending"), // "pending", "approved", "rejected"
  quality_notes: text("quality_notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
})

export const documents = pgTable("documents", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  declarationId: text("declaration_id").references(() => declarations.id),
  filename: text("filename").notNull(),
  url: text("url").notNull(),
  filetype: text("filetype"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("documents_company_id_idx").on(table.company_id),
}))

export const arInvoices = pgTable("ar_invoices", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  customer_id: text("customer_id").notNull().references(() => customers.id),
  date: text("date").notNull(),
  amount: text("amount").notNull(),
  status: text("status").default("draft"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("ar_invoices_company_id_idx").on(table.company_id),
}))

export const apInvoices = pgTable("ap_invoices", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  number: text("number").notNull(),
  supplier_id: text("supplier_id").notNull().references(() => suppliers.id),
  date: text("date").notNull(),
  amount: text("amount").notNull(),
  status: text("status").default("draft"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("ap_invoices_company_id_idx").on(table.company_id),
}))

// QUALITY CONTROL TABLES
export const qualityInspections = pgTable("quality_inspections", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  work_order_id: text("work_order_id").references(() => workOrders.id),
  inspection_type: text("inspection_type").notNull(), // "incoming", "in-process", "final", "pre-shipment"
  inspector: text("inspector").notNull(),
  inspection_date: text("inspection_date").notNull(),
  status: text("status").default("pending"), // "pending", "in-progress", "completed", "failed"
  notes: text("notes"),
  // ✅ ATTACHMENT FIELDS: Document and photo storage
  attachments: text("attachments"), // JSON array of document filenames
  photos: text("photos"),          // JSON array of photo filenames
  // ✅ ARCHIVE FIELDS: For compliance and audit trail
  archived: boolean("archived").default(false),
  archived_at: timestamp("archived_at", { withTimezone: true }),
  archived_by: text("archived_by"),
  archive_reason: text("archive_reason"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_inspections_company_id_idx").on(table.company_id),
}))

export const qualityDefects = pgTable("quality_defects", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  inspection_id: text("inspection_id").references(() => qualityInspections.id),
  work_order_id: text("work_order_id").references(() => workOrders.id),
  product_id: text("product_id").references(() => products.id),
  defect_type: text("defect_type").notNull(),
  severity: text("severity").notNull(), // "minor", "major", "critical"
  description: text("description").notNull(),
  quantity_affected: text("quantity_affected"),
  corrective_action: text("corrective_action"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_defects_company_id_idx").on(table.company_id),
}))

export const qualityStandards = pgTable("quality_standards", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  product_id: text("product_id").references(() => products.id),
  standard_name: text("standard_name").notNull(),
  specification: text("specification").notNull(),
  tolerance: text("tolerance"),
  test_method: text("test_method"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_standards_company_id_idx").on(table.company_id),
}))

export const qualityCertificates = pgTable("quality_certificates", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id), // MULTI-TENANT: Link to company
  inspection_id: text("inspection_id").references(() => qualityInspections.id),
  certificate_number: text("certificate_number").notNull(),
  certificate_type: text("certificate_type").notNull(), // "COA", "COC", "test_report", "compliance"
  issued_date: text("issued_date").notNull(),
  valid_until: text("valid_until"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("quality_certificates_company_id_idx").on(table.company_id),
}))

export const inspectionResults = pgTable("inspection_results", {
  id: text("id").primaryKey(),
  inspection_id: text("inspection_id").notNull().references(() => qualityInspections.id),
  standard_id: text("standard_id").references(() => qualityStandards.id),
  measured_value: text("measured_value"),
  result: text("result").notNull(), // "pass", "fail", "conditional"
  notes: text("notes"),
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
})

// RELATIONS (same as SQLite schema)
export const companiesRelations = relations(companies, ({ many }) => ({
  customers: many(customers),
  suppliers: many(suppliers),
  products: many(products),
  samples: many(samples),
  salesContracts: many(salesContracts),
  purchaseContracts: many(purchaseContracts),
  contractTemplates: many(contractTemplates),
  workOrders: many(workOrders),
  stockLots: many(stockLots),
  stockTxns: many(stockTxns),
  inventoryTransactions: many(inventoryTransactions), // ✅ NEW: Comprehensive inventory transactions
  declarations: many(declarations),
  arInvoices: many(arInvoices),
  apInvoices: many(apInvoices),
  qualityInspections: many(qualityInspections),
  qualityDefects: many(qualityDefects),
  qualityStandards: many(qualityStandards),
  qualityCertificates: many(qualityCertificates),
}))

export const customersRelations = relations(customers, ({ one, many }) => ({
  company: one(companies, {
    fields: [customers.company_id],
    references: [companies.id],
  }),
  // ✅ ENHANCED: Sample relationship integration
  samples: many(samples),
  salesContracts: many(salesContracts),
  arInvoices: many(arInvoices),
}))

export const suppliersRelations = relations(suppliers, ({ one, many }) => ({
  company: one(companies, {
    fields: [suppliers.company_id],
    references: [companies.id],
  }),
  // ✅ ENHANCED: Sample relationship integration
  samples: many(samples),
  purchaseContracts: many(purchaseContracts),
  apInvoices: many(apInvoices),
}))

export const productsRelations = relations(products, ({ one, many }) => ({
  company: one(companies, {
    fields: [products.company_id],
    references: [companies.id],
  }),
  // ✅ ENHANCED: Sample relationship integration
  samples: many(samples),
  salesContractItems: many(salesContractItems),
  purchaseContractItems: many(purchaseContractItems),
  workOrders: many(workOrders),
  stockLots: many(stockLots),
  stockTxns: many(stockTxns),
  declarationItems: many(declarationItems),
  qualityDefects: many(qualityDefects),
  qualityStandards: many(qualityStandards),
}))

export const samplesRelations = relations(samples, ({ one, many }) => ({
  // ✅ CORE ENTITY RELATIONS
  company: one(companies, {
    fields: [samples.company_id],
    references: [companies.id],
  }),
  customer: one(customers, {
    fields: [samples.customer_id],
    references: [customers.id],
  }),
  product: one(products, {
    fields: [samples.product_id],
    references: [products.id],
  }),
  supplier: one(suppliers, {
    fields: [samples.supplier_id],
    references: [suppliers.id],
  }),
  // workOrder: one(workOrders, {
  //   fields: [samples.work_order_id],
  //   references: [workOrders.id],
  // }), // TODO: Enable after database migration

  // ✅ MANUFACTURING WORKFLOW RELATIONS (Future Phase 2 Integration)
  // workOrders: many(workOrders),           // Sample → Work Order generation
  // salesContracts: many(salesContracts),   // Sample → Sales Contract creation

  // ✅ QUALITY CONTROL RELATIONS (Future Phase 3 Integration)
  // qualityInspections: many(qualityInspections), // Sample quality validation
  // qualityStandards: many(qualityStandards),     // Sample quality requirements

  // ✅ INVENTORY RELATIONS (Future Integration)
  // stockLots: many(stockLots),             // Sample inventory tracking
  // stockTxns: many(stockTxns),             // Sample stock movements

  // ✅ EXPORT & COMPLIANCE RELATIONS (Future Integration)
  // declarations: many(declarations),        // Sample-based export docs

  // ✅ FINANCIAL RELATIONS (Future Integration)
  // arInvoices: many(arInvoices),           // Sample billing to customers
}))

export const salesContractsRelations = relations(salesContracts, ({ one, many }) => ({
  company: one(companies, {
    fields: [salesContracts.company_id],
    references: [companies.id],
  }),
  customer: one(customers, {
    fields: [salesContracts.customer_id],
    references: [customers.id],
  }),
  template: one(contractTemplates, {
    fields: [salesContracts.template_id],
    references: [contractTemplates.id],
  }),
  items: many(salesContractItems),
  workOrders: many(workOrders),
}))

export const salesContractItemsRelations = relations(salesContractItems, ({ one }) => ({
  contract: one(salesContracts, {
    fields: [salesContractItems.contract_id],
    references: [salesContracts.id],
  }),
  product: one(products, {
    fields: [salesContractItems.product_id],
    references: [products.id],
  }),
}))

export const purchaseContractsRelations = relations(purchaseContracts, ({ one, many }) => ({
  company: one(companies, {
    fields: [purchaseContracts.company_id],
    references: [companies.id],
  }),
  supplier: one(suppliers, {
    fields: [purchaseContracts.supplier_id],
    references: [suppliers.id],
  }),
  template: one(contractTemplates, {
    fields: [purchaseContracts.template_id],
    references: [contractTemplates.id],
  }),
  items: many(purchaseContractItems),
}))

export const purchaseContractItemsRelations = relations(purchaseContractItems, ({ one }) => ({
  contract: one(purchaseContracts, {
    fields: [purchaseContractItems.contract_id],
    references: [purchaseContracts.id],
  }),
  product: one(products, {
    fields: [purchaseContractItems.product_id],
    references: [products.id],
  }),
}))

export const contractTemplatesRelations = relations(contractTemplates, ({ one, many }) => ({
  company: one(companies, {
    fields: [contractTemplates.company_id],
    references: [companies.id],
  }),
  salesContracts: many(salesContracts),
  purchaseContracts: many(purchaseContracts),
}))

export const arInvoicesRelations = relations(arInvoices, ({ one }) => ({
  company: one(companies, {
    fields: [arInvoices.company_id],
    references: [companies.id],
  }),
  customer: one(customers, {
    fields: [arInvoices.customer_id],
    references: [customers.id],
  }),
}))

export const apInvoicesRelations = relations(apInvoices, ({ one }) => ({
  company: one(companies, {
    fields: [apInvoices.company_id],
    references: [companies.id],
  }),
  supplier: one(suppliers, {
    fields: [apInvoices.supplier_id],
    references: [suppliers.id],
  }),
}))

// ✅ WORK ORDERS RELATIONS
export const workOrdersRelations = relations(workOrders, ({ one, many }) => ({
  company: one(companies, {
    fields: [workOrders.company_id],
    references: [companies.id],
  }),
  salesContract: one(salesContracts, {
    fields: [workOrders.sales_contract_id],
    references: [salesContracts.id],
  }),
  product: one(products, {
    fields: [workOrders.product_id],
    references: [products.id],
  }),
  // ✅ REVERSE RELATIONSHIP: Work Order ← Sample
  samples: many(samples),
  operations: many(workOperations),
  qualityInspections: many(qualityInspections),
  // ✅ NEW: Work Order → Stock lots relationship
  stockLots: many(stockLots),
}))

export const workOperationsRelations = relations(workOperations, ({ one }) => ({
  company: one(companies, {
    fields: [workOperations.company_id],
    references: [companies.id],
  }),
  workOrder: one(workOrders, {
    fields: [workOperations.work_order_id],
    references: [workOrders.id],
  }),
}))

// ✅ QUALITY INSPECTIONS RELATIONS
export const qualityInspectionsRelations = relations(qualityInspections, ({ one, many }) => ({
  company: one(companies, {
    fields: [qualityInspections.company_id],
    references: [companies.id],
  }),
  workOrder: one(workOrders, {
    fields: [qualityInspections.work_order_id],
    references: [workOrders.id],
  }),
  // ✅ NEW: Quality inspection → Stock lots relationship
  stockLots: many(stockLots),
}))

// ✅ INVENTORY RELATIONS
export const stockLotsRelations = relations(stockLots, ({ one, many }) => ({
  company: one(companies, {
    fields: [stockLots.company_id],
    references: [companies.id],
  }),
  product: one(products, {
    fields: [stockLots.product_id],
    references: [products.id],
  }),
  // ✅ NEW: Quality integration relationships
  inspection: one(qualityInspections, {
    fields: [stockLots.inspection_id],
    references: [qualityInspections.id],
  }),
  workOrder: one(workOrders, {
    fields: [stockLots.work_order_id],
    references: [workOrders.id],
  }),
  // ✅ NEW: Inventory transactions relationship
  inventoryTransactions: many(inventoryTransactions),
}))

export const stockTxnsRelations = relations(stockTxns, ({ one }) => ({
  company: one(companies, {
    fields: [stockTxns.company_id],
    references: [companies.id],
  }),
  product: one(products, {
    fields: [stockTxns.product_id],
    references: [products.id],
  }),
}))

// ✅ INVENTORY TRANSACTIONS RELATIONS
export const inventoryTransactionsRelations = relations(inventoryTransactions, ({ one }) => ({
  company: one(companies, {
    fields: [inventoryTransactions.company_id],
    references: [companies.id],
  }),
  stockLot: one(stockLots, {
    fields: [inventoryTransactions.stock_lot_id],
    references: [stockLots.id],
  }),
}))
