import { z } from "zod"

// Base validation schemas for core entities
export const contactSchema = z.object({
  name: z.string().min(1, "Contact name is required").max(255),
  phone: z.string().max(50).optional(),
  email: z.string().email("Invalid email format").optional().or(z.literal(""))
})

export const customerSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Customer name is required").max(255),
  contact_name: z.string().max(255).optional(),
  contact_phone: z.string().max(50).optional(),
  contact_email: z.string().email("Invalid email format").optional().or(z.literal("")),
  address: z.string().max(500).optional(),
  tax_id: z.string().max(50).optional(),
  bank: z.string().max(255).optional(),
  incoterm: z.string().max(50).optional(),
  payment_term: z.string().max(100).optional(),
  status: z.enum(["active", "inactive"]).default("active")
})

export const supplierSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Supplier name is required").max(255),
  contact: contactSchema.optional(),
  address: z.string().max(500).optional(),
  taxId: z.string().max(50).optional(),
  bank: z.string().max(255).optional(),
  status: z.enum(["active", "inactive"]).default("active")
})

export const productSchema = z.object({
  id: z.string().optional(),
  sku: z.string().min(1, "SKU is required").max(100),
  name: z.string().min(1, "Product name is required").max(255),
  unit: z.string().min(1, "Unit is required").max(50),
  hsCode: z.string().max(20).optional(),
  origin: z.string().max(100).optional(),
  package: z.string().max(255).optional(),
  image: z.string().url("Invalid image URL").optional().or(z.literal("")),
  // Quality Requirements
  inspection_required: z.string().optional().default("false"),
  quality_tolerance: z.string().max(255).optional(),
  quality_notes: z.string().max(1000).optional(),
})

// ✅ ENHANCED SAMPLES VALIDATION SCHEMA
export const sampleSchema = z.object({
  // ✅ CORE IDENTIFICATION FIELDS
  id: z.string().optional(),
  code: z.string().min(1, "Sample code is required").max(100),
  name: z.string().min(1, "Sample name is required").max(255),
  date: z.string().min(1, "Date is required"),
  status: z.enum(["active", "inactive", "archived"]).default("active"),

  // ✅ BIDIRECTIONAL WORKFLOW FIELDS
  sample_direction: z.enum(["outbound", "inbound", "internal"]).default("outbound"),
  sample_purpose: z.string().max(100).optional().or(z.literal("")),
  sender_type: z.enum(["customer", "supplier", "internal"]).optional().or(z.literal("")),
  receiver_type: z.enum(["customer", "supplier", "internal"]).optional().or(z.literal("")),

  // ✅ INBOUND SAMPLE FIELDS
  received_date: z.string().optional().or(z.literal("")),
  testing_status: z.enum(["not_started", "in_progress", "completed", "failed"]).default("not_started"),
  testing_results: z.string().max(2000).optional().or(z.literal("")),
  quote_requested: z.boolean().default(false),
  quote_provided: z.boolean().default(false),

  // ✅ RELATIONSHIP FIELDS (HANDLE EMPTY STRINGS)
  customer_id: z.string().optional().or(z.literal("")),
  product_id: z.string().optional().or(z.literal("")),
  supplier_id: z.string().optional().or(z.literal("")),

  // ✅ APPROVAL WORKFLOW FIELDS
  sample_type: z.enum(["development", "production", "quality", "prototype"]).default("development"),
  approval_status: z.enum(["pending", "approved", "rejected", "revision_required"]).default("pending"),
  approved_by: z.string().max(255).optional().or(z.literal("")),
  approved_date: z.string().optional().or(z.literal("")),
  rejection_reason: z.string().max(500).optional().or(z.literal("")),

  // ✅ BUSINESS SPECIFICATION FIELDS
  notes: z.string().max(2000).optional().or(z.literal("")),
  quantity: z.string().max(50).optional().or(z.literal("")),
  unit: z.string().max(20).optional().or(z.literal("")),
  specifications: z.string().max(5000).optional().or(z.literal("")), // JSON field for technical specifications
  quality_requirements: z.string().max(2000).optional().or(z.literal("")),
  delivery_date: z.string().optional().or(z.literal("")),
  priority: z.enum(["low", "normal", "high", "urgent"]).default("normal"),

  // ✅ COMMERCIAL FIELDS
  cost: z.string().max(50).optional().or(z.literal("")),
  currency: z.string().max(10).default("USD"),

  // ✅ LEGACY COMPATIBILITY FIELDS
  image: z.string().url("Invalid image URL").optional().or(z.literal("")),
  specification: z.string().max(1000).optional(), // Legacy field
  moq: z.number().int().min(1, "MOQ must be at least 1").optional(),
  availableFromStock: z.boolean().default(false),
  adminNotes: z.string().max(1000).optional(), // Legacy field
}).refine((data) => {
  // ✅ BUSINESS RULE: Outbound samples require product selection
  if (data.sample_direction === "outbound" && (!data.product_id || data.product_id === "")) {
    return false
  }
  return true
}, {
  message: "Product selection is required for outbound samples",
  path: ["product_id"]
})

// ✅ ENHANCED SAMPLE APPROVAL WORKFLOW SCHEMA
export const sampleApprovalSchema = z.object({
  approval_status: z.enum(["approved", "rejected", "revision_required"]),
  approved_by: z.string().min(1, "Approver is required").max(255),
  approved_date: z.string().min(1, "Approval date is required"),
  rejection_reason: z.string().max(500).optional(),
  notes: z.string().max(2000).optional(),

  // ✅ ENHANCED APPROVAL FIELDS
  approval_reason: z.string().max(1000).optional(), // Why was it approved/rejected
  next_steps: z.string().max(1000).optional(), // What happens next
  priority_change: z.enum(["low", "normal", "high", "urgent"]).optional(), // Change priority during approval

  // ✅ WORKFLOW AUTOMATION FLAGS
  auto_generate_contract: z.boolean().default(false), // Phase 2: Auto-generate sales contract
  auto_create_quality_check: z.boolean().default(false), // Phase 2: Create quality inspection
  notify_customer: z.boolean().default(true), // Send notification to customer

  // ✅ BATCH APPROVAL SUPPORT
  batch_operation: z.boolean().default(false), // Is this part of a batch operation
  batch_id: z.string().optional(), // Batch operation identifier
}).refine(data => {
  // Rejection reason required for rejected status
  if (data.approval_status === 'rejected' && !data.rejection_reason) {
    return false
  }
  // Approval reason recommended for approved status
  if (data.approval_status === 'approved' && !data.approval_reason && !data.notes) {
    return false
  }
  return true
}, {
  message: "Rejection reason is required for rejected samples, and approval reason or notes are recommended for approved samples"
})

// ✅ BATCH APPROVAL SCHEMA
export const sampleBatchApprovalSchema = z.object({
  sample_ids: z.array(z.string().min(1, "Sample ID is required")).min(1, "At least one sample ID is required").max(20, "Cannot approve more than 20 samples at once"),
  approval_status: z.enum(["approved", "rejected", "revision_required"]),
  approved_by: z.string().min(1, "Approver is required").max(255),
  approved_date: z.string().min(1, "Approval date is required"),
  rejection_reason: z.string().max(500).optional(),
  notes: z.string().max(2000).optional(),
  approval_reason: z.string().max(1000).optional(),

  // ✅ BATCH-SPECIFIC FIELDS
  batch_notes: z.string().max(2000).optional(), // Notes for the entire batch
  auto_generate_contracts: z.boolean().default(false), // Auto-generate contracts for approved samples
  notify_customers: z.boolean().default(true), // Send notifications
}).refine(data => {
  if (data.approval_status === 'rejected' && !data.rejection_reason) {
    return false
  }
  return true
}, {
  message: "Rejection reason is required for rejected samples"
})

// ✅ SAMPLE UPDATE SCHEMA (for PATCH operations)
export const sampleUpdateSchema = z.object({
  name: z.string().min(1, "Sample name is required").max(255).optional(),
  customer_id: z.string().min(1, "Customer is required").optional(),
  product_id: z.string().min(1, "Product is required").optional(),
  supplier_id: z.string().min(1, "Supplier is required").optional(),
  sample_type: z.enum(["development", "production", "quality", "prototype"]).optional(),
  priority: z.enum(["low", "normal", "high", "urgent"]).optional(),
  notes: z.string().max(2000).optional(),
  quantity: z.string().max(50).optional(),
  unit: z.string().max(20).optional(),
  specifications: z.string().max(5000).optional(),
  quality_requirements: z.string().max(2000).optional(),
  delivery_date: z.string().optional(),
  cost: z.string().max(50).optional(),
  currency: z.string().max(10).optional(),
  status: z.enum(["active", "inactive", "archived"]).optional(),
})

// ✅ ENHANCED SAMPLE QUERY PARAMETERS SCHEMA
export const sampleQuerySchema = z.object({
  // ✅ RELATIONSHIP FILTERING
  customer_id: z.string().optional(),
  product_id: z.string().optional(),
  supplier_id: z.string().optional(),

  // ✅ WORKFLOW FILTERING
  approval_status: z.enum(["pending", "approved", "rejected", "revision_required"]).optional(),
  sample_type: z.enum(["development", "production", "quality", "prototype"]).optional(),
  priority: z.enum(["low", "normal", "high", "urgent"]).optional(),
  status: z.enum(["active", "inactive", "archived"]).optional(),

  // ✅ BIDIRECTIONAL WORKFLOW FILTERING
  sample_direction: z.enum(["outbound", "inbound", "internal"]).optional(),
  sample_purpose: z.string().optional(),
  testing_status: z.enum(["not_started", "in_progress", "completed", "failed"]).optional(),

  // ✅ DATE RANGE FILTERING
  date_from: z.string().optional(), // ISO date string
  date_to: z.string().optional(),   // ISO date string
  created_from: z.string().optional(), // Created date range
  created_to: z.string().optional(),
  approved_from: z.string().optional(), // Approval date range
  approved_to: z.string().optional(),

  // ✅ ADVANCED SEARCH
  search: z.string().optional(),
  search_fields: z.string().optional(), // Comma-separated list of fields to search

  // ✅ PAGINATION & SORTING
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().int().min(1)).default("1"),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().int().min(1).max(100)).default("20"),
  sortBy: z.enum(["name", "code", "date", "created_at", "approval_status", "priority"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),

  // ✅ RESPONSE OPTIONS
  include_count: z.string().transform(val => val === "true").default("false"), // Include total count
  include_relationships: z.string().transform(val => val === "true").default("true"), // Include related data
})

// ✅ BULK OPERATIONS SCHEMA
export const sampleBulkUpdateSchema = z.object({
  ids: z.array(z.string().min(1, "Sample ID is required")).min(1, "At least one sample ID is required"),
  updates: z.object({
    approval_status: z.enum(["pending", "approved", "rejected", "revision_required"]).optional(),
    priority: z.enum(["low", "normal", "high", "urgent"]).optional(),
    status: z.enum(["active", "inactive", "archived"]).optional(),
    notes: z.string().max(2000).optional(),
  }).refine(data => Object.keys(data).length > 0, {
    message: "At least one field must be provided for update"
  })
})

// ✅ BULK DELETE SCHEMA
export const sampleBulkDeleteSchema = z.object({
  ids: z.array(z.string().min(1, "Sample ID is required")).min(1, "At least one sample ID is required").max(50, "Cannot delete more than 50 samples at once"),
  confirm: z.boolean().refine(val => val === true, {
    message: "Confirmation is required for bulk delete operations"
  })
})

// ✅ SAMPLE DETAIL QUERY PARAMETERS SCHEMA
export const sampleDetailQuerySchema = z.object({
  // ✅ CONDITIONAL RELATIONSHIP LOADING
  include: z.string().optional(), // Comma-separated list: "customer,product,supplier,workflow,history"

  // ✅ RELATIONSHIP DEPTH CONTROL
  include_customer_details: z.string().transform(val => val === "true").default("false"), // Include customer contact info
  include_product_details: z.string().transform(val => val === "true").default("false"), // Include product specifications
  include_supplier_details: z.string().transform(val => val === "true").default("false"), // Include supplier contact info

  // ✅ WORKFLOW & HISTORY OPTIONS
  include_approval_history: z.string().transform(val => val === "true").default("false"), // Include approval timeline
  include_workflow_actions: z.string().transform(val => val === "true").default("false"), // Include next possible actions

  // ✅ PERFORMANCE OPTIONS
  include_metadata: z.string().transform(val => val === "true").default("true"), // Include response metadata
  format: z.enum(["standard", "detailed", "minimal"]).default("standard"), // Response format level
})

// Contract validation schemas
export const contractItemSchema = z.object({
  product_id: z.string().min(1, "Product is required").optional(),
  productId: z.string().min(1, "Product is required").optional(), // Support both for compatibility
  qty: z.coerce.number().positive("Quantity must be positive"), // Coerce string to number
  price: z.coerce.number().positive("Price must be positive") // Coerce string to number
}).refine(data => data.product_id || data.productId, {
  message: "Product is required",
  path: ["productId"]
})

export const salesContractSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Contract number is required").max(100),
  customer_id: z.string().min(1, "Customer is required"),
  customerId: z.string().min(1, "Customer is required").optional(), // Support both for compatibility
  date: z.string().optional(),
  template_id: z.string().optional(),
  templateId: z.string().optional(), // Support both field names
  status: z.enum(["draft", "review", "approved", "active", "completed", "cancelled"]).default("draft"),
  currency: z.string().min(1, "Currency is required").max(10).optional(),
  items: z.array(contractItemSchema).min(1, "At least one item is required")
})

export const purchaseContractSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Contract number is required").max(100),
  supplier_id: z.string().optional(), // Make both optional
  supplierId: z.string().optional(), // Make both optional
  date: z.string().optional(), // Accept date string format
  template_id: z.string().optional(),
  templateId: z.string().optional(), // Support both field names
  status: z.enum(["draft", "review", "approved", "active", "completed", "cancelled"]).default("draft"),
  currency: z.string().min(1, "Currency is required").max(10).optional(), // Make optional
  items: z.array(contractItemSchema).min(1, "At least one item is required")
}).refine(data => data.supplier_id || data.supplierId, {
  message: "Supplier is required",
  path: ["supplier_id"]
})

// Contract Template Validation Schema
export const contractTemplateSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Template name is required").max(200),
  type: z.enum(["sales", "purchase"], {
    required_error: "Template type is required",
    invalid_type_error: "Template type must be either 'sales' or 'purchase'"
  }),
  content: z.string().min(1, "Template content is required"),
  currency: z.string().max(10).optional(),
  payment_terms: z.string().max(500).optional(),
  delivery_terms: z.string().max(500).optional(),
  language: z.string().max(10).default("en"),
  version: z.number().int().positive().default(1),
  is_active: z.boolean().default(true)
})

// Production validation schemas
export const workOperationSchema = z.object({
  name: z.string().min(1, "Operation name is required").max(255),
  status: z.enum(["not-started", "in-progress", "done"]).default("not-started"),
  startedAt: z.string().datetime().optional(),
  completedAt: z.string().datetime().optional()
})

export const workOrderSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Work order number is required").max(100),
  salesContractId: z.string().min(1, "Sales contract is required"),
  productId: z.string().min(1, "Product is required"),
  qty: z.number().int().positive("Quantity must be positive"),
  operations: z.array(workOperationSchema).optional()
})

// Inventory validation schemas
export const stockLotSchema = z.object({
  id: z.string().optional(),
  productId: z.string().min(1, "Product is required"),
  qty: z.number().positive("Quantity must be positive"),
  location: z.string().min(1, "Location is required").max(100),
  note: z.string().max(500).optional(),
  createdAt: z.string().datetime().optional()
})

export const stockTxnSchema = z.object({
  id: z.string().optional(),
  type: z.enum(["inbound", "outbound"]),
  productId: z.string().min(1, "Product is required"),
  qty: z.number().positive("Quantity must be positive"),
  location: z.string().min(1, "Location is required").max(100),
  ref: z.string().max(255).optional(),
  createdAt: z.string().datetime().optional()
})

// ✅ COMPREHENSIVE INVENTORY TRANSACTION TYPES & ENUMS
export const TransactionType = {
  INBOUND: "INBOUND",
  OUTBOUND: "OUTBOUND",
  TRANSFER: "TRANSFER",
  ADJUSTMENT: "ADJUSTMENT"
} as const

export const TransactionStatus = {
  PENDING: "PENDING",
  COMPLETED: "COMPLETED",
  CANCELLED: "CANCELLED"
} as const

export const ReferenceType = {
  WORK_ORDER: "work_order",
  QUALITY_INSPECTION: "quality_inspection",
  PURCHASE_RECEIPT: "purchase_receipt",
  SALES_SHIPMENT: "sales_shipment",
  MANUAL: "manual"
} as const

export type TransactionType = typeof TransactionType[keyof typeof TransactionType]
export type TransactionStatus = typeof TransactionStatus[keyof typeof TransactionStatus]
export type ReferenceType = typeof ReferenceType[keyof typeof ReferenceType]

// ✅ INVENTORY TRANSACTION VALIDATION SCHEMAS
export const inventoryTransactionSchema = z.object({
  id: z.string().optional(),
  transactionNumber: z.string().optional(), // Auto-generated
  transactionType: z.enum([
    TransactionType.INBOUND,
    TransactionType.OUTBOUND,
    TransactionType.TRANSFER,
    TransactionType.ADJUSTMENT
  ]),
  transactionStatus: z.enum([
    TransactionStatus.PENDING,
    TransactionStatus.COMPLETED,
    TransactionStatus.CANCELLED
  ]).default(TransactionStatus.PENDING),
  stockLotId: z.string().min(1, "Stock lot is required"),
  quantity: z.number().refine(
    (val) => val !== 0,
    { message: "Quantity cannot be zero" }
  ),
  unitCost: z.number().positive("Unit cost must be positive").optional(),
  totalCost: z.number().optional(),
  locationFrom: z.string().max(100).optional(),
  locationTo: z.string().max(100).optional(),
  referenceType: z.enum([
    ReferenceType.WORK_ORDER,
    ReferenceType.QUALITY_INSPECTION,
    ReferenceType.PURCHASE_RECEIPT,
    ReferenceType.SALES_SHIPMENT,
    ReferenceType.MANUAL
  ]).optional(),
  referenceId: z.string().optional(),
  referenceNumber: z.string().max(100).optional(),
  notes: z.string().max(500).optional(),
  transactionDate: z.string().datetime().optional(),
})

// ✅ SPECIFIC TRANSACTION TYPE SCHEMAS
export const inboundTransactionSchema = inventoryTransactionSchema.extend({
  transactionType: z.literal(TransactionType.INBOUND),
  quantity: z.number().positive("Inbound quantity must be positive"),
  locationTo: z.string().min(1, "Destination location is required").max(100),
})

export const outboundTransactionSchema = inventoryTransactionSchema.extend({
  transactionType: z.literal(TransactionType.OUTBOUND),
  quantity: z.number().positive("Outbound quantity must be positive"),
  locationFrom: z.string().min(1, "Source location is required").max(100),
})

export const transferTransactionSchema = inventoryTransactionSchema.extend({
  transactionType: z.literal(TransactionType.TRANSFER),
  quantity: z.number().positive("Transfer quantity must be positive"),
  locationFrom: z.string().min(1, "Source location is required").max(100),
  locationTo: z.string().min(1, "Destination location is required").max(100),
}).refine(
  (data) => data.locationFrom !== data.locationTo,
  { message: "Source and destination locations must be different", path: ["locationTo"] }
)

export const adjustmentTransactionSchema = inventoryTransactionSchema.extend({
  transactionType: z.literal(TransactionType.ADJUSTMENT),
  notes: z.string().min(1, "Adjustment reason is required").max(500),
})

// Export validation schemas
export const declarationItemSchema = z.object({
  productId: z.string().min(1, "Product is required"),
  qty: z.number().positive("Quantity must be positive"),
  hsCode: z.string().max(20).optional()
})

export const declarationSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Declaration number is required").max(100),
  date: z.string().datetime().optional(),
  status: z.enum(["draft", "submitted", "cleared", "exception"]).default("draft"),
  items: z.array(declarationItemSchema).min(1, "At least one item is required"),
  packingPhotoUrls: z.array(z.string().url()).optional(),
  taxRefundInfo: z.string().max(1000).optional()
})

// Finance validation schemas
export const arInvoiceSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Invoice number is required").max(100),
  customerId: z.string().min(1, "Customer is required"),
  date: z.string().datetime().optional(),
  currency: z.string().min(1, "Currency is required").max(10),
  amount: z.number().positive("Amount must be positive"),
  received: z.number().min(0, "Received amount cannot be negative").default(0)
})

export const apInvoiceSchema = z.object({
  id: z.string().optional(),
  number: z.string().min(1, "Invoice number is required").max(100),
  supplierId: z.string().min(1, "Supplier is required"),
  date: z.string().datetime().optional(),
  currency: z.string().min(1, "Currency is required").max(10),
  amount: z.number().positive("Amount must be positive"),
  paid: z.number().min(0, "Paid amount cannot be negative").default(0)
})

// Inbound/Outbound operation schemas
export const inboundSchema = z.object({
  product_id: z.string().min(1, "Product is required"),
  qty: z.number().positive("Quantity must be positive"),
  location: z.string().min(1, "Location is required").max(100),
  note: z.string().max(500).optional()
})

export const outboundSchema = z.object({
  product_id: z.string().min(1, "Product is required"),
  qty: z.number().positive("Quantity must be positive"),
  location: z.string().min(1, "Location is required").max(100),
  ref: z.string().max(255).optional()
})

// Quality Control validation schemas
export const qualityInspectionSchema = z.object({
  id: z.string().optional(),
  work_order_id: z.string().min(1, "Work order is required"),
  inspection_type: z.enum(["incoming", "in_process", "final", "pre_shipment"], {
    errorMap: () => ({ message: "Invalid inspection type" })
  }),
  inspector: z.string().min(1, "Inspector name is required").max(255),
  scheduled_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format").optional(),
  completed_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format").optional(),
  status: z.enum(["scheduled", "in-progress", "completed", "failed"]).default("scheduled"),
  notes: z.string().max(1000).optional()
})

export const qualityDefectSchema = z.object({
  id: z.string().optional(),
  inspection_id: z.string().min(1, "Inspection is required").optional(),
  work_order_id: z.string().min(1, "Work order is required"),
  product_id: z.string().min(1, "Product is required"),
  defect_type: z.enum(["visual", "dimensional", "functional", "material"], {
    errorMap: () => ({ message: "Invalid defect type" })
  }),
  severity: z.enum(["minor", "major", "critical"], {
    errorMap: () => ({ message: "Invalid severity level" })
  }),
  quantity: z.string().min(1, "Quantity is required"),
  description: z.string().min(1, "Description is required").max(1000),
  corrective_action: z.string().max(1000).optional(),
  status: z.enum(["open", "in-progress", "resolved", "closed"]).default("open")
})

export const qualityStandardSchema = z.object({
  id: z.string().optional(),
  product_id: z.string().min(1, "Product is required"),
  standard_name: z.string().min(1, "Standard name is required").max(255),
  specification: z.string().min(1, "Specification is required").max(1000),
  tolerance: z.string().max(255).optional(),
  test_method: z.string().max(500).optional(),
  acceptance_criteria: z.string().min(1, "Acceptance criteria is required").max(1000)
})

export const qualityCertificateSchema = z.object({
  id: z.string().optional(),
  inspection_id: z.string().min(1, "Inspection is required"),
  certificate_number: z.string().min(1, "Certificate number is required").max(100),
  certificate_type: z.enum(["COA", "COC", "test_report", "compliance"], {
    errorMap: () => ({ message: "Invalid certificate type" })
  }),
  issued_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  valid_until: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format").optional(),
  file_path: z.string().max(500).optional()
})

export const inspectionResultSchema = z.object({
  id: z.string().optional(),
  inspection_id: z.string().min(1, "Inspection is required"),
  standard_id: z.string().min(1, "Standard is required").optional(),
  measured_value: z.string().max(255).optional(),
  result: z.enum(["pass", "fail", "conditional"], {
    errorMap: () => ({ message: "Invalid result value" })
  }),
  notes: z.string().max(1000).optional()
})
