import { workflowService } from "./state-machine"
import { BusinessLogicError, ErrorCode } from "@/lib/errors"
import { logContractOperation, AuditAction, AuditResource } from "@/lib/audit"
import { workOrderGenerationService } from "@/lib/services/work-order-generation"
import { db } from "@/lib/db"
import { workOrders } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { TenantContext } from "@/lib/tenant-utils"

// Contract workflow actions
export interface ContractWorkflowData {
  contractId: string
  contractNumber: string
  customerId: string
  items: Array<{
    productId: string
    qty: number
    price: number
  }>
  totalValue: number
  allWorkOrdersCompleted?: boolean
}

export class ContractWorkflowService {
  async submitForReview(
    contractId: string,
    contractData: ContractWorkflowData,
    context: TenantContext
  ): Promise<string> {
    // Validate contract has required data
    if (!contractData.items || contractData.items.length === 0) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Contract must have at least one item before submitting for review"
      )
    }

    if (!contractData.customerId) {
      throw new BusinessLogicError(
        ErrorCode.VALIDATION_ERROR,
        "Contract must have a customer before submitting for review"
      )
    }

    // Calculate total value
    const totalValue = contractData.items.reduce((sum, item) => {
      return sum + (item.qty * item.price)
    }, 0)

    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      "draft",
      "review",
      { id: context.userId, companyId: context.companyId },
      { ...contractData, totalValue }
    )

    // Log the action (simplified for now)
    console.log(`Contract ${contractData.contractNumber} submitted for review by user ${context.userId}`)

    return newState
  }

  async approveContract(
    contractId: string,
    contractData: ContractWorkflowData,
    context: TenantContext
  ): Promise<string> {
    // Note: In the current system, we'll skip role-based approval limits
    // and focus on the core workflow automation. Role-based permissions
    // can be added later when user roles are implemented in the tenant system.

    // For now, any authenticated user in the company can approve contracts
    // This matches the current ERP system behavior

    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      "review",
      "approved",
      { id: context.userId, companyId: context.companyId }, // Convert context to user-like object
      contractData
    )

    // Log the approval (simplified for now)
    console.log(`Contract ${contractData.contractNumber} approved by user ${context.userId}`)

    return newState
  }

  async rejectContract(
    contractId: string,
    contractData: ContractWorkflowData,
    context: TenantContext,
    reason?: string
  ): Promise<string> {
    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      "review",
      "draft",
      { id: context.userId, companyId: context.companyId },
      contractData
    )

    // Log the rejection (simplified for now)
    console.log(`Contract ${contractData.contractNumber} rejected by user ${context.userId}. Reason: ${reason || 'No reason provided'}`)

    // In a real system, you might want to store the rejection reason
    // and notify the contract creator

    return newState
  }

  async activateContract(
    contractId: string,
    contractData: ContractWorkflowData,
    context: TenantContext
  ): Promise<{
    newState: string
    workOrdersCreated: string[]
  }> {
    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      "approved",
      "active",
      { id: context.userId, companyId: context.companyId },
      contractData
    )

    // Auto-generate work orders for each contract item
    const workOrdersCreated = await this.generateWorkOrders(contractId, contractData, context)

    // Log the activation (simplified for now)
    console.log(`Contract ${contractData.contractNumber} activated by user ${context.userId}. Created ${workOrdersCreated.length} work orders.`)

    return {
      newState,
      workOrdersCreated
    }
  }

  async completeContract(
    contractId: string,
    contractData: ContractWorkflowData,
    context: TenantContext
  ): Promise<string> {
    // Check if all work orders are completed
    const allWorkOrdersCompleted = await this.checkAllWorkOrdersCompleted(contractId)

    if (!allWorkOrdersCompleted) {
      throw new BusinessLogicError(
        ErrorCode.INVALID_WORKFLOW_TRANSITION,
        "Cannot complete contract until all work orders are completed"
      )
    }

    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      "active",
      "completed",
      { id: context.userId, companyId: context.companyId },
      { ...contractData, allWorkOrdersCompleted: true }
    )

    // Log the completion (simplified for now)
    console.log(`Contract ${contractData.contractNumber} completed by user ${context.userId}`)

    return newState
  }

  async cancelContract(
    contractId: string,
    contractData: ContractWorkflowData,
    context: TenantContext,
    reason?: string
  ): Promise<string> {
    // Determine current state and validate cancellation
    const currentState = contractData.contractNumber // This would come from the database

    // Contracts can be cancelled from most states except completed
    const validCancellationStates = ["draft", "review", "approved", "active"]

    const newState = await workflowService.transitionState(
      "contract",
      contractId,
      currentState,
      "cancelled",
      { id: context.userId, companyId: context.companyId },
      contractData
    )

    // If contract was active, cancel associated work orders
    if (currentState === "active") {
      await this.cancelAssociatedWorkOrders(contractId, context)
    }

    // Log the cancellation (simplified for now)
    console.log(`Contract ${contractData.contractNumber} cancelled by user ${context.userId}. Reason: ${reason || 'No reason provided'}`)

    return newState
  }

  getAvailableActions(
    currentState: string,
    contractData: ContractWorkflowData,
    context: TenantContext
  ): Array<{
    action: string
    targetState: string
    canExecute: boolean
    reason?: string
  }> {
    return workflowService.getAvailableActions(
      "contract",
      currentState,
      { id: context.userId, companyId: context.companyId },
      contractData
    )
  }

  // Helper methods
  private async generateWorkOrders(
    contractId: string,
    contractData: ContractWorkflowData,
    context: TenantContext
  ): Promise<string[]> {
    try {
      // Use the work order generation service
      const generatedWorkOrders = await workOrderGenerationService.generateFromContract(
        contractId,
        context.companyId,
        {
          priority: "normal",
          dueDateOffset: 14, // 2 weeks default
          autoCreateQualityInspections: true,
          notes: `Auto-generated from contract ${contractData.contractNumber} by user ${context.userId}`,
        }
      )

      return generatedWorkOrders.map(wo => wo.id)
    } catch (error) {
      console.error("Error generating work orders in workflow:", error)
      throw new BusinessLogicError(
        ErrorCode.BUSINESS_RULE_VIOLATION,
        `Failed to generate work orders: ${error instanceof Error ? error.message : "Unknown error"}`
      )
    }
  }

  private async checkAllWorkOrdersCompleted(contractId: string): Promise<boolean> {
    try {
      // Get all work orders for this contract
      const contractWorkOrders = await db.query.workOrders.findMany({
        where: eq(workOrders.sales_contract_id, contractId),
      })

      if (contractWorkOrders.length === 0) {
        return false // No work orders exist
      }

      // Check if all work orders are completed
      return contractWorkOrders.every(wo => wo.status === "completed")
    } catch (error) {
      console.error("Error checking work order completion status:", error)
      return false
    }
  }

  private async cancelAssociatedWorkOrders(contractId: string, context: TenantContext): Promise<void> {
    try {
      // Find all work orders for this contract
      const contractWorkOrders = await db.query.workOrders.findMany({
        where: and(
          eq(workOrders.sales_contract_id, contractId),
          eq(workOrders.company_id, context.companyId)
        ),
      })

      // Cancel work orders that are not completed
      for (const workOrder of contractWorkOrders) {
        if (workOrder.status !== "completed" && workOrder.status !== "cancelled") {
          await db.update(workOrders)
            .set({
              status: "cancelled",
              notes: `${workOrder.notes || ""}\n\nCancelled due to contract cancellation by user ${context.userId}`.trim()
            })
            .where(and(
              eq(workOrders.id, workOrder.id),
              eq(workOrders.company_id, context.companyId)
            ))
        }
      }

      console.log(`Cancelled ${contractWorkOrders.length} work orders for contract ${contractId}`)
    } catch (error) {
      console.error("Error cancelling associated work orders:", error)
      throw new BusinessLogicError(
        ErrorCode.BUSINESS_RULE_VIOLATION,
        `Failed to cancel associated work orders: ${error instanceof Error ? error.message : "Unknown error"}`
      )
    }
  }

  // Business rules validation
  async validateContractForApproval(contractData: ContractWorkflowData): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }> {
    const errors: string[] = []
    const warnings: string[] = []

    // Check minimum contract value
    if (contractData.totalValue < 1000) {
      warnings.push("Contract value is below recommended minimum of $1,000")
    }

    // Check maximum contract value
    if (contractData.totalValue > 1000000) {
      errors.push("Contract value exceeds maximum limit of $1,000,000")
    }

    // Check item quantities
    for (const item of contractData.items) {
      if (item.qty <= 0) {
        errors.push(`Invalid quantity for product ${item.productId}`)
      }
      if (item.price <= 0) {
        errors.push(`Invalid price for product ${item.productId}`)
      }
    }

    // Check for duplicate products
    const productIds = contractData.items.map(item => item.productId)
    const uniqueProductIds = new Set(productIds)
    if (productIds.length !== uniqueProductIds.size) {
      warnings.push("Contract contains duplicate products")
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}

// Export singleton instance
export const contractWorkflowService = new ContractWorkflowService()
