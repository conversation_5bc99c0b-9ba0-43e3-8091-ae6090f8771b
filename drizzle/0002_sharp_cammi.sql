CREATE TABLE "documents" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"declaration_id" text,
	"filename" text NOT NULL,
	"url" text NOT NULL,
	"filetype" text,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "work_operations" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"work_order_id" text NOT NULL,
	"operation_name" text NOT NULL,
	"sequence" text NOT NULL,
	"status" text DEFAULT 'pending',
	"start_time" timestamp with time zone,
	"end_time" timestamp with time zone,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "documents" ADD CONSTRAINT "documents_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "documents" ADD CONSTRAINT "documents_declaration_id_declarations_id_fk" FOREIGN KEY ("declaration_id") REFERENCES "public"."declarations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "work_operations" ADD CONSTRAINT "work_operations_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "work_operations" ADD CONSTRAINT "work_operations_work_order_id_work_orders_id_fk" FOREIGN KEY ("work_order_id") REFERENCES "public"."work_orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "documents_company_id_idx" ON "documents" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "work_operations_company_id_idx" ON "work_operations" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "work_operations_work_order_id_idx" ON "work_operations" USING btree ("work_order_id");