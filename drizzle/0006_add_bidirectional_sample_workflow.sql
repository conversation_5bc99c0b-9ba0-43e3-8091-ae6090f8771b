ALTER TABLE "samples" ADD COLUMN "sample_direction" text DEFAULT 'outbound' NOT NULL;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "sample_purpose" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "sender_type" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "receiver_type" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "received_date" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "testing_status" text DEFAULT 'not_started';--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "testing_results" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "quote_requested" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "quote_provided" boolean DEFAULT false;--> statement-breakpoint
CREATE INDEX "samples_direction_idx" ON "samples" USING btree ("sample_direction");--> statement-breakpoint
CREATE INDEX "samples_testing_status_idx" ON "samples" USING btree ("testing_status");