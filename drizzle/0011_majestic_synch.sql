CREATE TABLE "inventory_transactions" (
	"id" text PRIMARY KEY NOT NULL,
	"company_id" text NOT NULL,
	"transaction_number" text NOT NULL,
	"transaction_type" text NOT NULL,
	"transaction_status" text DEFAULT 'PENDING',
	"stock_lot_id" text NOT NULL,
	"quantity" text NOT NULL,
	"unit_cost" text,
	"total_cost" text,
	"location_from" text,
	"location_to" text,
	"reference_type" text,
	"reference_id" text,
	"reference_number" text,
	"created_by" text NOT NULL,
	"approved_by" text,
	"approved_at" timestamp with time zone,
	"notes" text,
	"transaction_date" timestamp with time zone NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "inventory_transactions" ADD CONSTRAINT "inventory_transactions_company_id_companies_id_fk" FOREIGN KEY ("company_id") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "inventory_transactions" ADD CONSTRAINT "inventory_transactions_stock_lot_id_stock_lots_id_fk" FOREIGN KEY ("stock_lot_id") REFERENCES "public"."stock_lots"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "inventory_transactions_company_id_idx" ON "inventory_transactions" USING btree ("company_id");--> statement-breakpoint
CREATE INDEX "inventory_transactions_stock_lot_id_idx" ON "inventory_transactions" USING btree ("stock_lot_id");--> statement-breakpoint
CREATE INDEX "inventory_transactions_type_idx" ON "inventory_transactions" USING btree ("transaction_type");--> statement-breakpoint
CREATE INDEX "inventory_transactions_status_idx" ON "inventory_transactions" USING btree ("transaction_status");--> statement-breakpoint
CREATE INDEX "inventory_transactions_reference_idx" ON "inventory_transactions" USING btree ("reference_type","reference_id");--> statement-breakpoint
CREATE INDEX "inventory_transactions_date_idx" ON "inventory_transactions" USING btree ("transaction_date");--> statement-breakpoint
CREATE INDEX "inventory_transactions_number_idx" ON "inventory_transactions" USING btree ("transaction_number");