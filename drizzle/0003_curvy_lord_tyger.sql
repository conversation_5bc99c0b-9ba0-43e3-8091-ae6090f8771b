ALTER TABLE "samples" ADD COLUMN "customer_id" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "product_id" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "supplier_id" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "sample_type" text DEFAULT 'development';--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "approval_status" text DEFAULT 'pending';--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "approved_by" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "approved_date" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "rejection_reason" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "notes" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "quantity" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "unit" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "specifications" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "quality_requirements" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "delivery_date" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "priority" text DEFAULT 'normal';--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "cost" text;--> statement-breakpoint
ALTER TABLE "samples" ADD COLUMN "currency" text DEFAULT 'USD';--> statement-breakpoint
ALTER TABLE "samples" ADD CONSTRAINT "samples_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "samples" ADD CONSTRAINT "samples_product_id_products_id_fk" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "samples" ADD CONSTRAINT "samples_supplier_id_suppliers_id_fk" FOREIGN KEY ("supplier_id") REFERENCES "public"."suppliers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "samples_customer_id_idx" ON "samples" USING btree ("customer_id");--> statement-breakpoint
CREATE INDEX "samples_product_id_idx" ON "samples" USING btree ("product_id");--> statement-breakpoint
CREATE INDEX "samples_supplier_id_idx" ON "samples" USING btree ("supplier_id");--> statement-breakpoint
CREATE INDEX "samples_approval_status_idx" ON "samples" USING btree ("approval_status");--> statement-breakpoint
CREATE INDEX "samples_sample_type_idx" ON "samples" USING btree ("sample_type");--> statement-breakpoint
CREATE INDEX "samples_priority_idx" ON "samples" USING btree ("priority");