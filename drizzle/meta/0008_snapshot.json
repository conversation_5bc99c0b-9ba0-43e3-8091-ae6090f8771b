{"id": "bf00ea76-3a8b-41ca-9626-99a682eb6f2b", "prevId": "ff9fc8c2-c06f-4653-8154-b9d60728a01b", "version": "7", "dialect": "postgresql", "tables": {"public.ap_invoices": {"name": "ap_invoices", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "supplier_id": {"name": "supplier_id", "type": "text", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"ap_invoices_company_id_idx": {"name": "ap_invoices_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ap_invoices_company_id_companies_id_fk": {"name": "ap_invoices_company_id_companies_id_fk", "tableFrom": "ap_invoices", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ap_invoices_supplier_id_suppliers_id_fk": {"name": "ap_invoices_supplier_id_suppliers_id_fk", "tableFrom": "ap_invoices", "tableTo": "suppliers", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ar_invoices": {"name": "ar_invoices", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"ar_invoices_company_id_idx": {"name": "ar_invoices_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ar_invoices_company_id_companies_id_fk": {"name": "ar_invoices_company_id_companies_id_fk", "tableFrom": "ar_invoices", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ar_invoices_customer_id_customers_id_fk": {"name": "ar_invoices_customer_id_customers_id_fk", "tableFrom": "ar_invoices", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.companies": {"name": "companies", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "auth0_user_id": {"name": "auth0_user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "legal_name": {"name": "legal_name", "type": "text", "primaryKey": false, "notNull": false}, "registration_number": {"name": "registration_number", "type": "text", "primaryKey": false, "notNull": false}, "tax_id": {"name": "tax_id", "type": "text", "primaryKey": false, "notNull": false}, "vat_number": {"name": "vat_number", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "address_line1": {"name": "address_line1", "type": "text", "primaryKey": false, "notNull": false}, "address_line2": {"name": "address_line2", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state_province": {"name": "state_province", "type": "text", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": false}, "business_type": {"name": "business_type", "type": "text", "primaryKey": false, "notNull": false}, "employee_count": {"name": "employee_count", "type": "text", "primaryKey": false, "notNull": false}, "annual_revenue": {"name": "annual_revenue", "type": "text", "primaryKey": false, "notNull": false}, "bank_name": {"name": "bank_name", "type": "text", "primaryKey": false, "notNull": false}, "bank_account": {"name": "bank_account", "type": "text", "primaryKey": false, "notNull": false}, "bank_swift": {"name": "bank_swift", "type": "text", "primaryKey": false, "notNull": false}, "bank_address": {"name": "bank_address", "type": "text", "primaryKey": false, "notNull": false}, "bank_iban": {"name": "bank_iban", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": false, "default": "'UTC'"}, "date_format": {"name": "date_format", "type": "text", "primaryKey": false, "notNull": false, "default": "'YYYY-MM-DD'"}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false, "default": "'en'"}, "export_license": {"name": "export_license", "type": "text", "primaryKey": false, "notNull": false}, "customs_code": {"name": "customs_code", "type": "text", "primaryKey": false, "notNull": false}, "preferred_incoterms": {"name": "preferred_incoterms", "type": "text", "primaryKey": false, "notNull": false, "default": "'FOB'"}, "preferred_payment_terms": {"name": "preferred_payment_terms", "type": "text", "primaryKey": false, "notNull": false, "default": "'30 days'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "onboarding_completed": {"name": "onboarding_completed", "type": "text", "primaryKey": false, "notNull": false, "default": "'false'"}, "onboarding_step": {"name": "onboarding_step", "type": "text", "primaryKey": false, "notNull": false, "default": "'basic_info'"}, "subscription_plan": {"name": "subscription_plan", "type": "text", "primaryKey": false, "notNull": false, "default": "'free'"}, "subscription_status": {"name": "subscription_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "trial_ends_at": {"name": "trial_ends_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "last_login_at": {"name": "last_login_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"companies_auth0_user_id_idx": {"name": "companies_auth0_user_id_idx", "columns": [{"expression": "auth0_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "companies_name_idx": {"name": "companies_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"companies_auth0_user_id_unique": {"name": "companies_auth0_user_id_unique", "nullsNotDistinct": false, "columns": ["auth0_user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.contract_templates": {"name": "contract_templates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "payment_terms": {"name": "payment_terms", "type": "text", "primaryKey": false, "notNull": false}, "delivery_terms": {"name": "delivery_terms", "type": "text", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false, "default": "'en'"}, "version": {"name": "version", "type": "text", "primaryKey": false, "notNull": false, "default": "'1'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_default": {"name": "is_default", "type": "text", "primaryKey": false, "notNull": false, "default": "'false'"}, "is_active": {"name": "is_active", "type": "text", "primaryKey": false, "notNull": false, "default": "'true'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"contract_templates_company_id_idx": {"name": "contract_templates_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"contract_templates_company_id_companies_id_fk": {"name": "contract_templates_company_id_companies_id_fk", "tableFrom": "contract_templates", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customers": {"name": "customers", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "contact_name": {"name": "contact_name", "type": "text", "primaryKey": false, "notNull": false}, "contact_phone": {"name": "contact_phone", "type": "text", "primaryKey": false, "notNull": false}, "contact_email": {"name": "contact_email", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "tax_id": {"name": "tax_id", "type": "text", "primaryKey": false, "notNull": false}, "bank": {"name": "bank", "type": "text", "primaryKey": false, "notNull": false}, "incoterm": {"name": "incoterm", "type": "text", "primaryKey": false, "notNull": false}, "payment_term": {"name": "payment_term", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"customers_company_id_idx": {"name": "customers_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customers_company_id_companies_id_fk": {"name": "customers_company_id_companies_id_fk", "tableFrom": "customers", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.declaration_items": {"name": "declaration_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "declaration_id": {"name": "declaration_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "quality_inspection_id": {"name": "quality_inspection_id", "type": "text", "primaryKey": false, "notNull": false}, "quality_status": {"name": "quality_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "quality_notes": {"name": "quality_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"declaration_items_declaration_id_declarations_id_fk": {"name": "declaration_items_declaration_id_declarations_id_fk", "tableFrom": "declaration_items", "tableTo": "declarations", "columnsFrom": ["declaration_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "declaration_items_product_id_products_id_fk": {"name": "declaration_items_product_id_products_id_fk", "tableFrom": "declaration_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "declaration_items_quality_inspection_id_quality_inspections_id_fk": {"name": "declaration_items_quality_inspection_id_quality_inspections_id_fk", "tableFrom": "declaration_items", "tableTo": "quality_inspections", "columnsFrom": ["quality_inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.declarations": {"name": "declarations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"declarations_company_id_idx": {"name": "declarations_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"declarations_company_id_companies_id_fk": {"name": "declarations_company_id_companies_id_fk", "tableFrom": "declarations", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "declaration_id": {"name": "declaration_id", "type": "text", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "filetype": {"name": "filetype", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"documents_company_id_idx": {"name": "documents_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"documents_company_id_companies_id_fk": {"name": "documents_company_id_companies_id_fk", "tableFrom": "documents", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "documents_declaration_id_declarations_id_fk": {"name": "documents_declaration_id_declarations_id_fk", "tableFrom": "documents", "tableTo": "declarations", "columnsFrom": ["declaration_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.inspection_results": {"name": "inspection_results", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "inspection_id": {"name": "inspection_id", "type": "text", "primaryKey": false, "notNull": true}, "standard_id": {"name": "standard_id", "type": "text", "primaryKey": false, "notNull": false}, "measured_value": {"name": "measured_value", "type": "text", "primaryKey": false, "notNull": false}, "result": {"name": "result", "type": "text", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"inspection_results_inspection_id_quality_inspections_id_fk": {"name": "inspection_results_inspection_id_quality_inspections_id_fk", "tableFrom": "inspection_results", "tableTo": "quality_inspections", "columnsFrom": ["inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inspection_results_standard_id_quality_standards_id_fk": {"name": "inspection_results_standard_id_quality_standards_id_fk", "tableFrom": "inspection_results", "tableTo": "quality_standards", "columnsFrom": ["standard_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "sku": {"name": "sku", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": true}, "hs_code": {"name": "hs_code", "type": "text", "primaryKey": false, "notNull": false}, "origin": {"name": "origin", "type": "text", "primaryKey": false, "notNull": false}, "package": {"name": "package", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": false}, "inspection_required": {"name": "inspection_required", "type": "text", "primaryKey": false, "notNull": false, "default": "'false'"}, "quality_tolerance": {"name": "quality_tolerance", "type": "text", "primaryKey": false, "notNull": false}, "quality_notes": {"name": "quality_notes", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"products_company_id_idx": {"name": "products_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_sku_company_idx": {"name": "products_sku_company_idx", "columns": [{"expression": "sku", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"products_company_id_companies_id_fk": {"name": "products_company_id_companies_id_fk", "tableFrom": "products", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.purchase_contract_items": {"name": "purchase_contract_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "contract_id": {"name": "contract_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"purchase_contract_items_contract_id_purchase_contracts_id_fk": {"name": "purchase_contract_items_contract_id_purchase_contracts_id_fk", "tableFrom": "purchase_contract_items", "tableTo": "purchase_contracts", "columnsFrom": ["contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "purchase_contract_items_product_id_products_id_fk": {"name": "purchase_contract_items_product_id_products_id_fk", "tableFrom": "purchase_contract_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.purchase_contracts": {"name": "purchase_contracts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "supplier_id": {"name": "supplier_id", "type": "text", "primaryKey": false, "notNull": true}, "template_id": {"name": "template_id", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"purchase_contracts_company_id_idx": {"name": "purchase_contracts_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"purchase_contracts_company_id_companies_id_fk": {"name": "purchase_contracts_company_id_companies_id_fk", "tableFrom": "purchase_contracts", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "purchase_contracts_supplier_id_suppliers_id_fk": {"name": "purchase_contracts_supplier_id_suppliers_id_fk", "tableFrom": "purchase_contracts", "tableTo": "suppliers", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "purchase_contracts_template_id_contract_templates_id_fk": {"name": "purchase_contracts_template_id_contract_templates_id_fk", "tableFrom": "purchase_contracts", "tableTo": "contract_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quality_certificates": {"name": "quality_certificates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "inspection_id": {"name": "inspection_id", "type": "text", "primaryKey": false, "notNull": false}, "certificate_number": {"name": "certificate_number", "type": "text", "primaryKey": false, "notNull": true}, "certificate_type": {"name": "certificate_type", "type": "text", "primaryKey": false, "notNull": true}, "issued_date": {"name": "issued_date", "type": "text", "primaryKey": false, "notNull": true}, "valid_until": {"name": "valid_until", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"quality_certificates_company_id_idx": {"name": "quality_certificates_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quality_certificates_company_id_companies_id_fk": {"name": "quality_certificates_company_id_companies_id_fk", "tableFrom": "quality_certificates", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_certificates_inspection_id_quality_inspections_id_fk": {"name": "quality_certificates_inspection_id_quality_inspections_id_fk", "tableFrom": "quality_certificates", "tableTo": "quality_inspections", "columnsFrom": ["inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quality_defects": {"name": "quality_defects", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "inspection_id": {"name": "inspection_id", "type": "text", "primaryKey": false, "notNull": false}, "work_order_id": {"name": "work_order_id", "type": "text", "primaryKey": false, "notNull": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false}, "defect_type": {"name": "defect_type", "type": "text", "primaryKey": false, "notNull": true}, "severity": {"name": "severity", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "quantity_affected": {"name": "quantity_affected", "type": "text", "primaryKey": false, "notNull": false}, "corrective_action": {"name": "corrective_action", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"quality_defects_company_id_idx": {"name": "quality_defects_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quality_defects_company_id_companies_id_fk": {"name": "quality_defects_company_id_companies_id_fk", "tableFrom": "quality_defects", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_defects_inspection_id_quality_inspections_id_fk": {"name": "quality_defects_inspection_id_quality_inspections_id_fk", "tableFrom": "quality_defects", "tableTo": "quality_inspections", "columnsFrom": ["inspection_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_defects_work_order_id_work_orders_id_fk": {"name": "quality_defects_work_order_id_work_orders_id_fk", "tableFrom": "quality_defects", "tableTo": "work_orders", "columnsFrom": ["work_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_defects_product_id_products_id_fk": {"name": "quality_defects_product_id_products_id_fk", "tableFrom": "quality_defects", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quality_inspections": {"name": "quality_inspections", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "work_order_id": {"name": "work_order_id", "type": "text", "primaryKey": false, "notNull": false}, "inspection_type": {"name": "inspection_type", "type": "text", "primaryKey": false, "notNull": true}, "inspector": {"name": "inspector", "type": "text", "primaryKey": false, "notNull": true}, "inspection_date": {"name": "inspection_date", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false}, "photos": {"name": "photos", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"quality_inspections_company_id_idx": {"name": "quality_inspections_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quality_inspections_company_id_companies_id_fk": {"name": "quality_inspections_company_id_companies_id_fk", "tableFrom": "quality_inspections", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_inspections_work_order_id_work_orders_id_fk": {"name": "quality_inspections_work_order_id_work_orders_id_fk", "tableFrom": "quality_inspections", "tableTo": "work_orders", "columnsFrom": ["work_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quality_standards": {"name": "quality_standards", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false}, "standard_name": {"name": "standard_name", "type": "text", "primaryKey": false, "notNull": true}, "specification": {"name": "specification", "type": "text", "primaryKey": false, "notNull": true}, "tolerance": {"name": "tolerance", "type": "text", "primaryKey": false, "notNull": false}, "test_method": {"name": "test_method", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"quality_standards_company_id_idx": {"name": "quality_standards_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quality_standards_company_id_companies_id_fk": {"name": "quality_standards_company_id_companies_id_fk", "tableFrom": "quality_standards", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quality_standards_product_id_products_id_fk": {"name": "quality_standards_product_id_products_id_fk", "tableFrom": "quality_standards", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sales_contract_items": {"name": "sales_contract_items", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "contract_id": {"name": "contract_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"sales_contract_items_contract_id_sales_contracts_id_fk": {"name": "sales_contract_items_contract_id_sales_contracts_id_fk", "tableFrom": "sales_contract_items", "tableTo": "sales_contracts", "columnsFrom": ["contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "sales_contract_items_product_id_products_id_fk": {"name": "sales_contract_items_product_id_products_id_fk", "tableFrom": "sales_contract_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sales_contracts": {"name": "sales_contracts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": true}, "template_id": {"name": "template_id", "type": "text", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"sales_contracts_company_id_idx": {"name": "sales_contracts_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sales_contracts_company_id_companies_id_fk": {"name": "sales_contracts_company_id_companies_id_fk", "tableFrom": "sales_contracts", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "sales_contracts_customer_id_customers_id_fk": {"name": "sales_contracts_customer_id_customers_id_fk", "tableFrom": "sales_contracts", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "sales_contracts_template_id_contract_templates_id_fk": {"name": "sales_contracts_template_id_contract_templates_id_fk", "tableFrom": "sales_contracts", "tableTo": "contract_templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.samples": {"name": "samples", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "sample_direction": {"name": "sample_direction", "type": "text", "primaryKey": false, "notNull": true, "default": "'outbound'"}, "sample_purpose": {"name": "sample_purpose", "type": "text", "primaryKey": false, "notNull": false}, "sender_type": {"name": "sender_type", "type": "text", "primaryKey": false, "notNull": false}, "receiver_type": {"name": "receiver_type", "type": "text", "primaryKey": false, "notNull": false}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false}, "supplier_id": {"name": "supplier_id", "type": "text", "primaryKey": false, "notNull": false}, "sample_type": {"name": "sample_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'development'"}, "approval_status": {"name": "approval_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "approved_by": {"name": "approved_by", "type": "text", "primaryKey": false, "notNull": false}, "approved_date": {"name": "approved_date", "type": "text", "primaryKey": false, "notNull": false}, "rejection_reason": {"name": "rejection_reason", "type": "text", "primaryKey": false, "notNull": false}, "received_date": {"name": "received_date", "type": "text", "primaryKey": false, "notNull": false}, "testing_status": {"name": "testing_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'not_started'"}, "testing_results": {"name": "testing_results", "type": "text", "primaryKey": false, "notNull": false}, "quote_requested": {"name": "quote_requested", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "quote_provided": {"name": "quote_provided", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "text", "primaryKey": false, "notNull": false}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": false}, "specifications": {"name": "specifications", "type": "text", "primaryKey": false, "notNull": false}, "quality_requirements": {"name": "quality_requirements", "type": "text", "primaryKey": false, "notNull": false}, "delivery_date": {"name": "delivery_date", "type": "text", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": false, "default": "'normal'"}, "cost": {"name": "cost", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"samples_company_id_idx": {"name": "samples_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_customer_id_idx": {"name": "samples_customer_id_idx", "columns": [{"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_product_id_idx": {"name": "samples_product_id_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_supplier_id_idx": {"name": "samples_supplier_id_idx", "columns": [{"expression": "supplier_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_direction_idx": {"name": "samples_direction_idx", "columns": [{"expression": "sample_direction", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_approval_status_idx": {"name": "samples_approval_status_idx", "columns": [{"expression": "approval_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_testing_status_idx": {"name": "samples_testing_status_idx", "columns": [{"expression": "testing_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_sample_type_idx": {"name": "samples_sample_type_idx", "columns": [{"expression": "sample_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "samples_priority_idx": {"name": "samples_priority_idx", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"samples_company_id_companies_id_fk": {"name": "samples_company_id_companies_id_fk", "tableFrom": "samples", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "samples_customer_id_customers_id_fk": {"name": "samples_customer_id_customers_id_fk", "tableFrom": "samples", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "samples_product_id_products_id_fk": {"name": "samples_product_id_products_id_fk", "tableFrom": "samples", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "samples_supplier_id_suppliers_id_fk": {"name": "samples_supplier_id_suppliers_id_fk", "tableFrom": "samples", "tableTo": "suppliers", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stock_lots": {"name": "stock_lots", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true}, "lot_number": {"name": "lot_number", "type": "text", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'available'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"stock_lots_company_id_idx": {"name": "stock_lots_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"stock_lots_company_id_companies_id_fk": {"name": "stock_lots_company_id_companies_id_fk", "tableFrom": "stock_lots", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "stock_lots_product_id_products_id_fk": {"name": "stock_lots_product_id_products_id_fk", "tableFrom": "stock_lots", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stock_txns": {"name": "stock_txns", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "reference": {"name": "reference", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"stock_txns_company_id_idx": {"name": "stock_txns_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"stock_txns_company_id_companies_id_fk": {"name": "stock_txns_company_id_companies_id_fk", "tableFrom": "stock_txns", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "stock_txns_product_id_products_id_fk": {"name": "stock_txns_product_id_products_id_fk", "tableFrom": "stock_txns", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.suppliers": {"name": "suppliers", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "contact_name": {"name": "contact_name", "type": "text", "primaryKey": false, "notNull": false}, "contact_phone": {"name": "contact_phone", "type": "text", "primaryKey": false, "notNull": false}, "contact_email": {"name": "contact_email", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "tax_id": {"name": "tax_id", "type": "text", "primaryKey": false, "notNull": false}, "bank": {"name": "bank", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"suppliers_company_id_idx": {"name": "suppliers_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"suppliers_company_id_companies_id_fk": {"name": "suppliers_company_id_companies_id_fk", "tableFrom": "suppliers", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_operations": {"name": "work_operations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "work_order_id": {"name": "work_order_id", "type": "text", "primaryKey": false, "notNull": true}, "operation_name": {"name": "operation_name", "type": "text", "primaryKey": false, "notNull": true}, "sequence": {"name": "sequence", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "start_time": {"name": "start_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"work_operations_company_id_idx": {"name": "work_operations_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "work_operations_work_order_id_idx": {"name": "work_operations_work_order_id_idx", "columns": [{"expression": "work_order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"work_operations_company_id_companies_id_fk": {"name": "work_operations_company_id_companies_id_fk", "tableFrom": "work_operations", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_operations_work_order_id_work_orders_id_fk": {"name": "work_operations_work_order_id_work_orders_id_fk", "tableFrom": "work_operations", "tableTo": "work_orders", "columnsFrom": ["work_order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.work_orders": {"name": "work_orders", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "text", "primaryKey": false, "notNull": true}, "number": {"name": "number", "type": "text", "primaryKey": false, "notNull": true}, "sales_contract_id": {"name": "sales_contract_id", "type": "text", "primaryKey": false, "notNull": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true}, "qty": {"name": "qty", "type": "text", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": false, "default": "'normal'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"work_orders_company_id_idx": {"name": "work_orders_company_id_idx", "columns": [{"expression": "company_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"work_orders_company_id_companies_id_fk": {"name": "work_orders_company_id_companies_id_fk", "tableFrom": "work_orders", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_orders_sales_contract_id_sales_contracts_id_fk": {"name": "work_orders_sales_contract_id_sales_contracts_id_fk", "tableFrom": "work_orders", "tableTo": "sales_contracts", "columnsFrom": ["sales_contract_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "work_orders_product_id_products_id_fk": {"name": "work_orders_product_id_products_id_fk", "tableFrom": "work_orders", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}