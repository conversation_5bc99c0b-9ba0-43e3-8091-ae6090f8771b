ALTER TABLE "stock_lots" ADD COLUMN "quality_status" text DEFAULT 'pending';--> statement-breakpoint
ALTER TABLE "stock_lots" ADD COLUMN "inspection_id" text;--> statement-breakpoint
ALTER TABLE "stock_lots" ADD COLUMN "quality_approved_date" text;--> statement-breakpoint
ALTER TABLE "stock_lots" ADD COLUMN "quality_approved_by" text;--> statement-breakpoint
ALTER TABLE "stock_lots" ADD COLUMN "quality_notes" text;--> statement-breakpoint
ALTER TABLE "stock_lots" ADD COLUMN "work_order_id" text;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "workflow_trigger" text;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "reference_id" text;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "location" text;--> statement-breakpoint
ALTER TABLE "stock_txns" ADD COLUMN "notes" text;--> statement-breakpoint
ALTER TABLE "stock_lots" ADD CONSTRAINT "stock_lots_inspection_id_quality_inspections_id_fk" FOREIGN KEY ("inspection_id") REFERENCES "public"."quality_inspections"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "stock_lots" ADD CONSTRAINT "stock_lots_work_order_id_work_orders_id_fk" FOREIGN KEY ("work_order_id") REFERENCES "public"."work_orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "stock_lots_quality_status_idx" ON "stock_lots" USING btree ("quality_status");--> statement-breakpoint
CREATE INDEX "stock_lots_inspection_id_idx" ON "stock_lots" USING btree ("inspection_id");--> statement-breakpoint
CREATE INDEX "stock_lots_work_order_id_idx" ON "stock_lots" USING btree ("work_order_id");--> statement-breakpoint
CREATE INDEX "stock_txns_workflow_trigger_idx" ON "stock_txns" USING btree ("workflow_trigger");--> statement-breakpoint
CREATE INDEX "stock_txns_reference_id_idx" ON "stock_txns" USING btree ("reference_id");