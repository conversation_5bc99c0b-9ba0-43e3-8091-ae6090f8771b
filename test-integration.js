/**
 * Manual Integration Service Test
 * Test the work order integration service directly
 */

const { workOrderInventoryIntegrationService } = require('./lib/services/work-order-inventory-integration.ts');

async function testIntegration() {
  console.log('🧪 Starting manual integration test...');
  
  try {
    // Test with the completed garment work order
    const workOrderId = 'wo_de0f3e44-f688-490a-8b57-2efe8ed51d32'; // WO-250829-53HC
    const context = {
      companyId: 'comp_fc_china_001', // Replace with actual company ID
      userId: 'test_user'
    };
    
    console.log(`🎯 Testing work order: ${workOrderId}`);
    console.log(`🔐 Context:`, context);
    
    await workOrderInventoryIntegrationService.onWorkOrderCompleted(workOrderId, context);
    
    console.log('✅ Integration test completed successfully!');
    
  } catch (error) {
    console.error('❌ Integration test failed:', error);
    console.error('Stack:', error.stack);
  }
}

testIntegration();
