"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FilePen, Plus, Trash2, Users, Phone, Mail, Eye } from "lucide-react";
import { Toaster } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AddSupplierForm } from "./add-supplier-form";
import { EditSupplierForm } from "./edit-supplier-form";
import { useRouter } from "next/navigation";
import { useI18n } from "@/components/i18n-provider";
import Link from "next/link";
import { useSafeToast } from "@/hooks/use-safe-toast";

// Type definition for Supplier - should match the schema
type Supplier = {
  id: string;
  name: string;
  contact_name: string | null;
  contact_phone: string | null;
  contact_email: string | null;
  address: string | null;
  tax_id: string | null;
  bank: string | null;
  status: string | null;
  created_at: Date | null;
};

export function SuppliersClientPage({ initialSuppliers }: { initialSuppliers: Supplier[] }) {
  const router = useRouter();
  const { t } = useI18n();
  const toast = useSafeToast();
  const [isAddOpen, setAddOpen] = useState(false);
  const [supplierToEdit, setSupplierToEdit] = useState<Supplier | null>(null);
  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);

  // Create stable callback functions to prevent render issues
  const handleCloseAdd = () => setAddOpen(false);
  const handleCloseEdit = () => setSupplierToEdit(null);
  const handleCloseDelete = () => setSupplierToDelete(null);

  const handleDelete = async () => {
    if (!supplierToDelete) return;
    const supplierName = supplierToDelete.name; // Store name before clearing state
    const response = await fetch(`/api/suppliers/${supplierToDelete.id}`, { method: "DELETE" });
    if (response.ok) {
      // Clear state first to prevent setState during render
      handleCloseDelete();
      // Then show toast using safe toast hook
      toast.success(`Supplier "${supplierName}" deleted successfully.`);
      router.refresh();
    } else {
      toast.error("Failed to delete supplier.");
      handleCloseDelete(); // Clear state even on error
    }
  };

  const getStatusColor = (status: string | null) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "default";
      case "inactive":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("suppliers.title")}</h1>
          <p className="text-muted-foreground">{t("suppliers.subtitle")}</p>
        </div>
        <Dialog open={isAddOpen} onOpenChange={setAddOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              {t("suppliers.add")}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("suppliers.add.title")}</DialogTitle>
              <DialogDescription>{t("suppliers.add.description")}</DialogDescription>
            </DialogHeader>
            <AddSupplierForm setOpen={handleCloseAdd} />
          </DialogContent>
        </Dialog>
      </div>
      <Toaster richColors />

      <Card>
        <CardHeader>
          <CardTitle>{t("suppliers.title")}</CardTitle>
          <CardDescription>{t("suppliers.subtitle")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {initialSuppliers.map((supplier) => (
              <Card key={supplier.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CardTitle className="text-lg">{supplier.name}</CardTitle>
                        <Badge variant={getStatusColor(supplier.status)}>
                          {t(`status.${supplier.status}` as keyof typeof t) !== `status.${supplier.status}` ? t(`status.${supplier.status}` as keyof typeof t) : supplier.status}
                        </Badge>
                      </div>
                      <CardDescription>{supplier.address}</CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      <Link href={`/suppliers/view/${supplier.id}`}>
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button variant="ghost" size="icon" onClick={() => setSupplierToEdit(supplier)}>
                        <FilePen className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => setSupplierToDelete(supplier)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-3">
                      <h4 className="font-semibold">{t("field.contact")} {t("common.description")}</h4>
                      <div className="space-y-2">
                        {supplier.contact_name && (
                          <div className="flex items-center gap-2 text-sm">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            {supplier.contact_name}
                          </div>
                        )}
                        {supplier.contact_email && (
                          <div className="flex items-center gap-2 text-sm">
                            <Mail className="h-4 w-4 text-muted-foreground" />
                            {supplier.contact_email}
                          </div>
                        )}
                        {supplier.contact_phone && (
                          <div className="flex items-center gap-2 text-sm">
                            <Phone className="h-4 w-4 text-muted-foreground" />
                            {supplier.contact_phone}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="space-y-3">
                      <h4 className="font-semibold">{t("suppliers.form.bank")}</h4>
                      <div className="space-y-2 text-sm">
                        <p>{supplier.bank || "N/A"}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={!!supplierToEdit} onOpenChange={(open) => !open && handleCloseEdit()}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("suppliers.edit.title")}</DialogTitle>
            <DialogDescription>{t("suppliers.edit.description")}</DialogDescription>
          </DialogHeader>
          {supplierToEdit && <EditSupplierForm setOpen={handleCloseEdit} supplier={supplierToEdit} />}
        </DialogContent>
      </Dialog>

      {/* Delete Alert Dialog */}
      <AlertDialog open={!!supplierToDelete} onOpenChange={(open) => !open && handleCloseDelete()}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("suppliers.delete.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("suppliers.delete.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>{t("common.delete")}</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
