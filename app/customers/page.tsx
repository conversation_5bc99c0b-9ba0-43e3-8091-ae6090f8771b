"use client"

import { useState, useEffect } from "react"
import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Building, Phone, Mail, MapPin, X, Eye, Pencil, Trash2 } from "lucide-react"
import { useI18n } from "@/components/i18n-provider"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Customer {
  id: string
  name: string
  contact_name?: string
  contact_phone?: string
  contact_email?: string
  address?: string
  tax_id?: string
  bank?: string
  incoterm?: string
  payment_term?: string
  status: string
  created_at: string
}

export default function CustomersPage() {
  const { t } = useI18n()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    contact_name: "",
    contact_phone: "",
    contact_email: "",
    address: "",
    tax_id: "",
    bank: "",
    incoterm: "FOB",
    payment_term: "30 days",
    status: "active",
  })

  useEffect(() => {
    fetchCustomers()
  }, [])

  const fetchCustomers = async () => {
    try {
      const response = await fetch("/api/customers")
      if (response.ok) {
        const data = await response.json()
        // Ensure we always have an array
        setCustomers(Array.isArray(data) ? data : [])
      } else {
        console.error("Failed to fetch customers:", response.status)
        setCustomers([])
      }
    } catch (error) {
      console.error("Failed to fetch customers:", error)
      setCustomers([])
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const response = await fetch("/api/customers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const newCustomer = await response.json()
        setCustomers(prev => [...prev, newCustomer])
        setShowAddDialog(false)
        setFormData({
          name: "",
          contact_name: "",
          contact_phone: "",
          contact_email: "",
          address: "",
          tax_id: "",
          bank: "",
          incoterm: "FOB",
          payment_term: "30 days",
          status: "active",
        })
        alert(t("customers.success.created"))
      } else {
        const error = await response.text()
        alert(`${t("customers.error.create")}: ${error}`)
      }
    } catch (error) {
      alert(`Error: ${error.message}`)
    }
  }

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer)
    setFormData({
      name: customer.name,
      contact_name: customer.contact_name || "",
      contact_phone: customer.contact_phone || "",
      contact_email: customer.contact_email || "",
      address: customer.address || "",
      tax_id: customer.tax_id || "",
      bank: customer.bank || "",
      incoterm: customer.incoterm || "FOB",
      payment_term: customer.payment_term || "30 days",
      status: customer.status,
    })
    setShowEditDialog(true)
  }

  const handleUpdateCustomer = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!editingCustomer) return

    try {
      const response = await fetch(`/api/customers/${editingCustomer.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const updatedCustomer = await response.json()
        setCustomers(prev => prev.map(c => c.id === editingCustomer.id ? updatedCustomer : c))
        setShowEditDialog(false)
        setEditingCustomer(null)
        alert("Customer updated successfully!")
      } else {
        const error = await response.text()
        alert(`Failed to update customer: ${error}`)
      }
    } catch (error) {
      alert(`Error: ${error.message}`)
    }
  }

  const handleDeleteCustomer = async () => {
    if (!customerToDelete) return

    try {
      const response = await fetch(`/api/customers/${customerToDelete.id}`, {
        method: "DELETE",
      })

      if (response.ok) {
        setCustomers(prev => prev.filter(c => c.id !== customerToDelete.id))
        setCustomerToDelete(null)
        alert("Customer deleted successfully!")
      } else {
        const error = await response.text()
        alert(`Failed to delete customer: ${error}`)
      }
    } catch (error) {
      alert(`Error: ${error.message}`)
    }
  }

  const filteredCustomers = customers.filter(customer => {
    if (!customer || typeof customer !== 'object') return false

    const name = customer.name || ""
    const contactName = customer.contact_name || ""
    const contactEmail = customer.contact_email || ""
    const search = searchTerm.toLowerCase()

    return name.toLowerCase().includes(search) ||
      contactName.toLowerCase().includes(search) ||
      contactEmail.toLowerCase().includes(search)
  })

  return (
    <AppShell>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t("customers.title")}</h1>
            <p className="text-muted-foreground">
              {t("customers.subtitle")}
            </p>
          </div>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                {t("customers.add")}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>{t("customers.add.title")}</DialogTitle>
                <DialogDescription>
                  {t("customers.add.description")}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">{t("customers.form.company_name")} *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="ABC Electronics Inc."
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="contact_name">{t("customers.form.contact_name")}</Label>
                    <Input
                      id="contact_name"
                      value={formData.contact_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, contact_name: e.target.value }))}
                      placeholder="Sarah Johnson"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="contact_phone">{t("customers.form.contact_phone")}</Label>
                    <Input
                      id="contact_phone"
                      value={formData.contact_phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, contact_phone: e.target.value }))}
                      placeholder="******-0123"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="contact_email">{t("customers.form.contact_email")}</Label>
                    <Input
                      id="contact_email"
                      type="email"
                      value={formData.contact_email}
                      onChange={(e) => setFormData(prev => ({ ...prev, contact_email: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2 col-span-2">
                    <Label htmlFor="address">{t("customers.form.address")}</Label>
                    <Input
                      id="address"
                      value={formData.address}
                      onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                      placeholder="1234 Industrial Blvd, Los Angeles, CA 90210, USA"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="tax_id">{t("customers.form.tax_id")}</Label>
                    <Input
                      id="tax_id"
                      value={formData.tax_id}
                      onChange={(e) => setFormData(prev => ({ ...prev, tax_id: e.target.value }))}
                      placeholder="US-TAX-*********"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bank">{t("customers.form.bank")}</Label>
                    <Input
                      id="bank"
                      value={formData.bank}
                      onChange={(e) => setFormData(prev => ({ ...prev, bank: e.target.value }))}
                      placeholder="Chase Bank - Account: **********"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="incoterm">{t("customers.form.incoterm")}</Label>
                    <Select value={formData.incoterm} onValueChange={(value) => setFormData(prev => ({ ...prev, incoterm: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="FOB">FOB</SelectItem>
                        <SelectItem value="CIF">CIF</SelectItem>
                        <SelectItem value="EXW">EXW</SelectItem>
                        <SelectItem value="DDP">DDP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="payment_term">{t("customers.form.payment_term")}</Label>
                    <Select value={formData.payment_term} onValueChange={(value) => setFormData(prev => ({ ...prev, payment_term: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30 days">30 days</SelectItem>
                        <SelectItem value="60 days">60 days</SelectItem>
                        <SelectItem value="90 days">90 days</SelectItem>
                        <SelectItem value="Cash">Cash</SelectItem>
                        <SelectItem value="T/T">T/T</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex justify-end space-x-2 pt-4">
                  <Button type="button" variant="outline" onClick={() => setShowAddDialog(false)}>
                    {t("common.cancel")}
                  </Button>
                  <Button type="submit">
                    {t("common.create")} {t("field.customer")}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>

          {/* Edit Customer Dialog */}
          <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>{t("customers.edit.title")}</DialogTitle>
                <DialogDescription>
                  {t("customers.edit.description")}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleUpdateCustomer} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-name">Company Name *</Label>
                    <Input
                      id="edit-name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Acme Corporation"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-contact_name">Contact Person</Label>
                    <Input
                      id="edit-contact_name"
                      value={formData.contact_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, contact_name: e.target.value }))}
                      placeholder="John Smith"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-contact_phone">Phone</Label>
                    <Input
                      id="edit-contact_phone"
                      value={formData.contact_phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, contact_phone: e.target.value }))}
                      placeholder="+****************"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-contact_email">Email</Label>
                    <Input
                      id="edit-contact_email"
                      type="email"
                      value={formData.contact_email}
                      onChange={(e) => setFormData(prev => ({ ...prev, contact_email: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-address">Address</Label>
                  <Input
                    id="edit-address"
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    placeholder="123 Business St, City, State 12345"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-incoterm">Incoterm</Label>
                    <Select value={formData.incoterm} onValueChange={(value) => setFormData(prev => ({ ...prev, incoterm: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="FOB">FOB</SelectItem>
                        <SelectItem value="CIF">CIF</SelectItem>
                        <SelectItem value="EXW">EXW</SelectItem>
                        <SelectItem value="DDP">DDP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-payment_term">Payment Terms</Label>
                    <Select value={formData.payment_term} onValueChange={(value) => setFormData(prev => ({ ...prev, payment_term: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30 days">30 days</SelectItem>
                        <SelectItem value="60 days">60 days</SelectItem>
                        <SelectItem value="90 days">90 days</SelectItem>
                        <SelectItem value="Cash">Cash</SelectItem>
                        <SelectItem value="T/T">T/T</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex justify-end space-x-2 pt-4">
                  <Button type="button" variant="outline" onClick={() => setShowEditDialog(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    Update Customer
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        <div className="flex items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        {loading ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-3 bg-muted rounded"></div>
                    <div className="h-3 bg-muted rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("customers.table.company_name")}</TableHead>
                  <TableHead>{t("customers.table.contact_person")}</TableHead>
                  <TableHead>{t("customers.table.phone")}</TableHead>
                  <TableHead>{t("customers.table.email")}</TableHead>
                  <TableHead>{t("customers.table.address")}</TableHead>
                  <TableHead>{t("customers.table.incoterm")}</TableHead>
                  <TableHead>{t("customers.table.payment_terms")}</TableHead>
                  <TableHead>{t("customers.table.status")}</TableHead>
                  <TableHead className="text-right">{t("customers.table.actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCustomers.map((customer) => (
                  <TableRow key={customer.id} className="hover:bg-muted/50">
                    <TableCell className="font-medium">{customer.name}</TableCell>
                    <TableCell>{customer.contact_name || "-"}</TableCell>
                    <TableCell>{customer.contact_phone || "-"}</TableCell>
                    <TableCell>{customer.contact_email || "-"}</TableCell>
                    <TableCell className="max-w-[200px] truncate" title={customer.address || ""}>
                      {customer.address || "-"}
                    </TableCell>
                    <TableCell>{customer.incoterm || "-"}</TableCell>
                    <TableCell>{customer.payment_term || "-"}</TableCell>
                    <TableCell>
                      <Badge variant={customer.status === "active" ? "default" : "secondary"}>
                        {t(`status.${customer.status}` as keyof typeof t) !== `status.${customer.status}` ? t(`status.${customer.status}` as keyof typeof t) : customer.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex gap-2 justify-end">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.location.href = `/crm/${customer.id}`}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          {t("common.view")}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditCustomer(customer)}
                        >
                          <Pencil className="h-4 w-4 mr-1" />
                          {t("common.edit")}
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => setCustomerToDelete(customer)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          {t("common.delete")}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {!loading && filteredCustomers.length === 0 && (
          <div className="border rounded-lg">
            <div className="flex flex-col items-center justify-center py-12">
              <Building className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">{t("customers.empty")}</h3>
              <p className="text-muted-foreground text-center mb-4">
                {searchTerm ? t("customers.empty") : t("customers.empty.description")}
              </p>
              <Button onClick={() => setShowAddDialog(true)}>
                <Plus className="mr-2 h-4 w-4" />
                {t("customers.add")}
              </Button>
            </div>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={!!customerToDelete} onOpenChange={(open) => !open && setCustomerToDelete(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{t("customers.delete.title")}</AlertDialogTitle>
              <AlertDialogDescription>
                {t("customers.delete.description")}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteCustomer} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                {t("common.delete")}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AppShell>
  )
}
