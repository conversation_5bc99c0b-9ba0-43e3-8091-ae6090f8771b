"use client"

import { useState, useEffect, useCallback } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { WorkOrderProductSelect } from "@/components/work-orders/product-select"
import { WorkOrderSalesContractSelect } from "@/components/work-orders/sales-contract-select"
import { ArrowLeft, Plus, AlertTriangle, RefreshCw, Settings, Package, FileText, Calendar } from "lucide-react"
import Link from "next/link"
import { z } from "zod"

// ✅ FORM VALIDATION SCHEMA
const workOrderSchema = z.object({
  number: z.string().min(1, "Work order number is required"),
  product_id: z.string().min(1, "Product selection is required"),
  sales_contract_id: z.string().optional(),
  qty: z.number().min(1, "Quantity must be at least 1").max(999999, "Quantity too large"),
  due_date: z.string().optional(),
  notes: z.string().optional(),
  priority: z.enum(["low", "normal", "high", "urgent"]).default("normal"),
})

type WorkOrderFormData = z.infer<typeof workOrderSchema>

export default function CreateWorkOrderPage() {
  const router = useRouter()
  const { success: toastSuccess, error: toastError } = useSafeToast()
  const { t } = useI18n()

  // ✅ STATE MANAGEMENT
  const [loading, setLoading] = useState(false)
  const [dataLoading, setDataLoading] = useState(true)
  const [products, setProducts] = useState<any[]>([])
  const [salesContracts, setSalesContracts] = useState<any[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})

  const [formData, setFormData] = useState<WorkOrderFormData>({
    number: "",
    product_id: "",
    sales_contract_id: "",
    qty: 1,
    due_date: "",
    notes: "",
    priority: "normal",
  })

  // ✅ ENHANCED DATA LOADING
  const loadData = useCallback(async () => {
    try {
      setDataLoading(true)
      setErrors({})

      const [productsRes, contractsRes] = await Promise.all([
        fetch("/api/products"),
        fetch("/api/contracts/sales"),
      ])

      if (!productsRes.ok || !contractsRes.ok) {
        throw new Error("Failed to fetch data from server")
      }

      const [productsData, contractsData] = await Promise.all([
        productsRes.json(),
        contractsRes.json(),
      ])

      const productsList = Array.isArray(productsData) ? productsData : productsData.data || []
      const contractsList = Array.isArray(contractsData) ? contractsData : contractsData.data || []

      setProducts(productsList)
      setSalesContracts(contractsList)

      // Validate data availability
      if (productsList.length === 0) {
        toastError("No products available. Please create products first.")
      }

    } catch (error) {
      console.error("Error loading data:", error)
      toastError("Failed to load data. Please try again.")
      setErrors({ general: "Failed to load required data" })
    } finally {
      setDataLoading(false)
    }
  }, [toastError])

  useEffect(() => {
    loadData()
  }, [loadData])

  // ✅ GENERATE PROFESSIONAL WORK ORDER NUMBER
  const generateWorkOrderNumber = useCallback(() => {
    const date = new Date()
    const year = date.getFullYear().toString().slice(-2)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const random = Math.random().toString(36).substring(2, 6).toUpperCase()
    return `WO-${year}${month}${day}-${random}`
  }, [])

  useEffect(() => {
    if (!formData.number) {
      setFormData(prev => ({
        ...prev,
        number: generateWorkOrderNumber()
      }))
    }
  }, [formData.number, generateWorkOrderNumber])

  // ✅ FORM VALIDATION
  const validateForm = useCallback(() => {
    try {
      workOrderSchema.parse(formData)
      setErrors({})
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {}
        error.errors.forEach((err) => {
          if (err.path.length > 0) {
            newErrors[err.path[0] as string] = err.message
          }
        })
        setErrors(newErrors)
      }
      return false
    }
  }, [formData])

  // ✅ FORM FIELD HANDLERS
  const handleFieldChange = useCallback((field: keyof WorkOrderFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }, [errors])

  // ✅ ENHANCED FORM SUBMISSION
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!validateForm()) {
      toastError("Please fix the errors below")
      return
    }

    try {
      setLoading(true)
      setErrors({})

      // Prepare submission data
      const submissionData = {
        ...formData,
        qty: Number(formData.qty),
        due_date: formData.due_date || undefined,
        sales_contract_id: formData.sales_contract_id || undefined,
        operations: [
          { name: "Material Preparation" },
          { name: "Production Setup" },
          { name: "Manufacturing" },
          { name: "Quality Inspection" },
          { name: "Packaging" },
          { name: "Final Review" },
        ]
      }

      const response = await fetch("/api/production/work-orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submissionData),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || "Failed to create work order")
      }

      const result = await response.json()
      toastSuccess("Work order created successfully!")

      // Navigate to the detail view
      router.push(`/production/${result.data.id}`)
    } catch (error) {
      console.error("Error creating work order:", error)
      const errorMessage = error instanceof Error ? error.message : "Failed to create work order"
      toastError(errorMessage)
      setErrors({ general: errorMessage })
    } finally {
      setLoading(false)
    }
  }

  // ✅ LOADING STATE
  if (dataLoading) {
    return (
      <AppShell>
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/production">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Work Orders
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Create Work Order</h1>
              <p className="text-muted-foreground">Loading required data...</p>
            </div>
          </div>

          <Card className="max-w-2xl">
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">Loading products and contracts...</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppShell>
    )
  }

  // ✅ ERROR STATE
  if (errors.general && products.length === 0) {
    return (
      <AppShell>
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/production">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Work Orders
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Create Work Order</h1>
              <p className="text-muted-foreground">Unable to load required data</p>
            </div>
          </div>

          <Card className="max-w-2xl">
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center">
                <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
                <p className="text-sm text-muted-foreground mb-4">{errors.general}</p>
                <Button variant="outline" onClick={loadData}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppShell>
    )
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ PROFESSIONAL HEADER */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/production">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Work Orders
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Create Work Order</h1>
            <p className="text-muted-foreground">Create a new production work order</p>
          </div>
        </div>

        {/* ✅ ENHANCED FORM */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Work Order Details
                </CardTitle>
                <CardDescription>
                  Enter the details for the new production work order
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Work Order Number & Quantity */}
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="number">Work Order Number *</Label>
                      <Input
                        id="number"
                        value={formData.number}
                        onChange={(e) => handleFieldChange("number", e.target.value)}
                        placeholder="WO-241225-ABCD"
                        className={errors.number ? "border-destructive" : ""}
                      />
                      {errors.number && (
                        <p className="text-xs text-destructive">{errors.number}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="qty">Quantity *</Label>
                      <Input
                        id="qty"
                        type="number"
                        min="1"
                        max="999999"
                        value={formData.qty}
                        onChange={(e) => handleFieldChange("qty", parseInt(e.target.value) || 1)}
                        className={errors.qty ? "border-destructive" : ""}
                      />
                      {errors.qty && (
                        <p className="text-xs text-destructive">{errors.qty}</p>
                      )}
                    </div>
                  </div>

                  {/* Product Selection */}
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      Product *
                    </Label>
                    <WorkOrderProductSelect
                      products={products}
                      value={formData.product_id}
                      onValueChange={(value) => handleFieldChange("product_id", value)}
                      placeholder="Search and select a product..."
                      className={errors.product_id ? "border-destructive" : ""}
                      required
                    />
                    {errors.product_id && (
                      <p className="text-xs text-destructive">{errors.product_id}</p>
                    )}
                  </div>

                  {/* Sales Contract Selection */}
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Sales Contract (Optional)
                    </Label>
                    <WorkOrderSalesContractSelect
                      salesContracts={salesContracts}
                      value={formData.sales_contract_id}
                      onValueChange={(value) => handleFieldChange("sales_contract_id", value)}
                      placeholder="Search and select a sales contract..."
                    />
                    <p className="text-xs text-muted-foreground">
                      Link this work order to an existing sales contract for better tracking
                    </p>
                  </div>

                  {/* Due Date & Priority */}
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="due_date" className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Due Date (Optional)
                      </Label>
                      <Input
                        id="due_date"
                        type="date"
                        value={formData.due_date}
                        onChange={(e) => handleFieldChange("due_date", e.target.value)}
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="priority">Priority</Label>
                      <select
                        id="priority"
                        name="priority"
                        title="Select work order priority"
                        value={formData.priority}
                        onChange={(e) => handleFieldChange("priority", e.target.value as any)}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="low">Low Priority</option>
                        <option value="normal">Normal Priority</option>
                        <option value="high">High Priority</option>
                        <option value="urgent">Urgent Priority</option>
                      </select>
                    </div>
                  </div>

                  {/* Notes */}
                  <div className="space-y-2">
                    <Label htmlFor="notes">Production Notes (Optional)</Label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => handleFieldChange("notes", e.target.value)}
                      placeholder="Add any special instructions or notes for production..."
                      rows={3}
                    />
                  </div>

                  {/* Form Actions */}
                  <div className="flex items-center gap-2 pt-4">
                    <Button type="submit" disabled={loading || dataLoading}>
                      {loading ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <Plus className="mr-2 h-4 w-4" />
                          Create Work Order
                        </>
                      )}
                    </Button>
                    <Button type="button" variant="outline" asChild disabled={loading}>
                      <Link href="/production">Cancel</Link>
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar Information */}
          <div className="space-y-4">
            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Available Resources</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Products:</span>
                  <span className="font-medium">{products.length}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Sales Contracts:</span>
                  <span className="font-medium">{salesContracts.length}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Active Contracts:</span>
                  <span className="font-medium">
                    {salesContracts.filter(c => c.status === 'approved').length}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Help Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Work Order Tips</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-xs text-muted-foreground">
                <div className="space-y-2">
                  <p>• <strong>Product Selection:</strong> Use the search to quickly find products by SKU or name</p>
                  <p>• <strong>Sales Contract:</strong> Link to existing contracts for better tracking and reporting</p>
                  <p>• <strong>Priority:</strong> Set priority to help production planning</p>
                  <p>• <strong>Due Date:</strong> Optional but recommended for production scheduling</p>
                </div>
              </CardContent>
            </Card>

            {/* Process Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Production Process</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-xs text-muted-foreground">
                <div className="space-y-1">
                  <p>1. Material Preparation</p>
                  <p>2. Production Setup</p>
                  <p>3. Manufacturing</p>
                  <p>4. Quality Inspection</p>
                  <p>5. Packaging</p>
                  <p>6. Final Review</p>
                </div>
                <p className="text-xs text-muted-foreground mt-2 pt-2 border-t">
                  These operations will be automatically created for your work order.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppShell>
  )
}
