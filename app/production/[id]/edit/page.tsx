"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { AppShell } from "@/components/app-shell"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { Alert<PERSON>riangle, RefreshCw, Save, ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"

export default function WorkOrderEditPage() {
  const params = useParams()
  const router = useRouter()
  const { success: toastSuccess, error: toastError } = useSafeToast()
  const { t } = useI18n()

  const [workOrder, setWorkOrder] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Form state
  const [formData, setFormData] = useState({
    qty: "",
    due_date: "",
    priority: "normal",
    notes: "",
    status: "pending"
  })

  const workOrderId = params.id as string

  // ✅ FETCH WORK ORDER FOR EDITING
  const fetchWorkOrder = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!workOrderId) {
        throw new Error('Work order ID is missing')
      }

      const response = await fetch(`/api/production/work-orders/${workOrderId}`)

      if (response.ok) {
        const responseData = await response.json()
        const workOrderData = responseData.data || responseData
        setWorkOrder(workOrderData)
        
        // Initialize form with current data
        setFormData({
          qty: workOrderData.qty || "",
          due_date: workOrderData.due_date ? new Date(workOrderData.due_date).toISOString().split('T')[0] : "",
          priority: workOrderData.priority || "normal",
          notes: workOrderData.notes || "",
          status: workOrderData.status || "pending"
        })
      } else {
        throw new Error(`Failed to fetch work order: ${response.status}`)
      }
    } catch (err) {
      console.error("Error fetching work order:", err)
      setError(err instanceof Error ? err.message : "Failed to load work order")
      toastError("Failed to load work order")
    } finally {
      setLoading(false)
    }
  }

  // ✅ SAVE WORK ORDER CHANGES
  const handleSave = async () => {
    try {
      setSaving(true)
      
      const response = await fetch(`/api/production/work-orders/${workOrderId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          qty: formData.qty,
          due_date: formData.due_date || null,
          priority: formData.priority,
          notes: formData.notes || null,
          status: formData.status
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to update work order")
      }

      toastSuccess("Work order updated successfully")
      router.push(`/production/${workOrderId}`) // Redirect to view page
      
    } catch (error) {
      console.error("Save failed:", error)
      toastError("Failed to save changes")
    } finally {
      setSaving(false)
    }
  }

  useEffect(() => {
    fetchWorkOrder()
  }, [workOrderId])

  if (loading) {
    return (
      <AppShell>
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading work order...</span>
        </div>
      </AppShell>
    )
  }

  if (error) {
    return (
      <AppShell>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">{error}</p>
              <Button variant="outline" onClick={fetchWorkOrder} className="mt-2">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </AppShell>
    )
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ PROFESSIONAL HEADER */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" asChild>
              <Link href={`/production/${workOrderId}`}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to View
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Edit Work Order</h1>
              <p className="text-muted-foreground">
                {workOrder?.number} - {workOrder?.product?.name}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href={`/production/${workOrderId}`}>Cancel</Link>
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              {saving ? (
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Save Changes
            </Button>
          </div>
        </div>

        {/* ✅ WORK ORDER INFO CARD */}
        <Card>
          <CardHeader>
            <CardTitle>Work Order Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Work Order Number</Label>
                <p className="font-medium">{workOrder?.number}</p>
              </div>
              <div>
                <Label>Sales Contract</Label>
                <p className="font-medium">{workOrder?.salesContract?.number || "N/A"}</p>
              </div>
              <div>
                <Label>Product</Label>
                <p className="font-medium">{workOrder?.product?.sku} - {workOrder?.product?.name}</p>
              </div>
              <div>
                <Label>Customer</Label>
                <p className="font-medium">{workOrder?.salesContract?.customer?.name || "N/A"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* ✅ EDITABLE FIELDS CARD */}
        <Card>
          <CardHeader>
            <CardTitle>Edit Work Order Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-6">
              {/* Quantity */}
              <div className="space-y-2">
                <Label htmlFor="qty">Quantity *</Label>
                <Input
                  id="qty"
                  type="number"
                  min="0"
                  step="any"
                  value={formData.qty}
                  onChange={(e) => setFormData(prev => ({ ...prev, qty: e.target.value }))}
                  placeholder="Enter quantity"
                />
              </div>

              {/* Due Date */}
              <div className="space-y-2">
                <Label htmlFor="due_date">Due Date</Label>
                <Input
                  id="due_date"
                  type="date"
                  value={formData.due_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, due_date: e.target.value }))}
                />
              </div>

              {/* Priority */}
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select value={formData.priority} onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="on_hold">On Hold</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Production Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Add production notes, instructions, or comments..."
                rows={4}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
