"use client"

import { AppShell } from "@/components/app-shell"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useI18n } from "@/components/i18n-provider"
import { useUser } from '@auth0/nextjs-auth0/client'
import {
  AlertCircle,
  Building,
  CheckCircle,
  Clock,
  FileText,
  Package,
  TrendingUp,
  Users
} from "lucide-react"
import { useEffect, useState } from 'react'

interface DashboardStats {
  customers: number
  products: number
  suppliers: number
  salesContracts: number
  purchaseContracts: number
  samples: number
}

export default function DashboardPage() {
  const { user, isLoading } = useUser()
  const { t } = useI18n()
  const [stats, setStats] = useState<DashboardStats>({
    customers: 0,
    products: 0,
    suppliers: 0,
    salesContracts: 0,
    purchaseContracts: 0,
    samples: 0
  })
  const [statsLoading, setStatsLoading] = useState(true)
  const [statsError, setStatsError] = useState<string | null>(null)

  // Fetch dashboard statistics from secure API endpoints
  useEffect(() => {
    if (!user) return

    const fetchStats = async () => {
      try {
        setStatsLoading(true)
        setStatsError(null)

        // First, ensure company exists for this user
        console.log('🏢 Ensuring company exists for user:', user.email)
        const ensureCompanyRes = await fetch('/api/companies/ensure', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        if (!ensureCompanyRes.ok) {
          throw new Error('Failed to ensure company exists')
        }

        const companyResult = await ensureCompanyRes.json()
        console.log('🏢 Company status:', companyResult)

        // Now fetch data from all secured endpoints
        const [customersRes, productsRes, suppliersRes, salesContractsRes, purchaseContractsRes, samplesRes] = await Promise.allSettled([
          fetch('/api/customers'),
          fetch('/api/products'),
          fetch('/api/suppliers'),
          fetch('/api/contracts/sales'),
          fetch('/api/contracts/purchase'),
          fetch('/api/samples')
        ])

        const newStats: DashboardStats = {
          customers: 0,
          products: 0,
          suppliers: 0,
          salesContracts: 0,
          purchaseContracts: 0,
          samples: 0
        }

        // Process customers
        if (customersRes.status === 'fulfilled' && customersRes.value.ok) {
          const customers = await customersRes.value.json()
          newStats.customers = Array.isArray(customers) ? customers.length : 0
        }

        // Process products
        if (productsRes.status === 'fulfilled' && productsRes.value.ok) {
          const products = await productsRes.value.json()
          newStats.products = Array.isArray(products) ? products.length : 0
        }

        // Process suppliers
        if (suppliersRes.status === 'fulfilled' && suppliersRes.value.ok) {
          const suppliers = await suppliersRes.value.json()
          newStats.suppliers = Array.isArray(suppliers) ? suppliers.length : 0
        }

        // Process sales contracts
        if (salesContractsRes.status === 'fulfilled' && salesContractsRes.value.ok) {
          const salesContractsResponse = await salesContractsRes.value.json()
          console.log('📊 Sales contracts response:', salesContractsResponse)
          // Handle API response format: {success: true, data: [...]}
          const salesContractsData = salesContractsResponse.data || salesContractsResponse
          newStats.salesContracts = Array.isArray(salesContractsData) ? salesContractsData.length : 0
        } else {
          console.error('❌ Sales contracts fetch failed:', salesContractsRes)
        }

        // Process purchase contracts
        if (purchaseContractsRes.status === 'fulfilled' && purchaseContractsRes.value.ok) {
          const purchaseContractsResponse = await purchaseContractsRes.value.json()
          console.log('📊 Purchase contracts response:', purchaseContractsResponse)
          // Handle API response format: {success: true, data: [...]}
          const purchaseContractsData = purchaseContractsResponse.data || purchaseContractsResponse
          newStats.purchaseContracts = Array.isArray(purchaseContractsData) ? purchaseContractsData.length : 0
        } else {
          console.error('❌ Purchase contracts fetch failed:', purchaseContractsRes)
        }

        // Process samples
        if (samplesRes.status === 'fulfilled' && samplesRes.value.ok) {
          const samples = await samplesRes.value.json()
          newStats.samples = Array.isArray(samples) ? samples.length : 0
        }

        setStats(newStats)
      } catch (error) {
        console.error('Error fetching dashboard stats:', error)
        setStatsError('Failed to load dashboard statistics')
      } finally {
        setStatsLoading(false)
      }
    }

    fetchStats()
  }, [user])

  if (isLoading) {
    return (
      <AppShell>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-muted-foreground">{t("dashboard.loading")}</p>
          </div>
        </div>
      </AppShell>
    )
  }

  if (statsError) {
    return (
      <AppShell>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">{t("dashboard.error.title")}</h2>
            <p className="text-muted-foreground mb-4">{statsError}</p>
            <p className="text-sm text-muted-foreground">
              {t("dashboard.error.description")}
            </p>
            <Button onClick={() => window.location.reload()} className="mt-4">
              {t("dashboard.error.retry")}
            </Button>
          </div>
        </div>
      </AppShell>
    )
  }

  return (
    <AppShell>
      <div className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">
            {t("dashboard.welcome")}{user?.name ? `, ${user.name}` : ''}!
          </h1>
          <p className="text-muted-foreground">
            {t("dashboard.subtitle")}
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("dashboard.stats.customers.title")}</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statsLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
                ) : (
                  stats.customers
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                {t("dashboard.stats.customers.description")}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("dashboard.stats.products.title")}</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statsLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
                ) : (
                  stats.products
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                {t("dashboard.stats.products.description")}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("kpi.suppliers")}</CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statsLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
                ) : (
                  stats.suppliers
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                {t("kpi.suppliers.desc")}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("kpi.contracts")}</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statsLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
                ) : (
                  stats.salesContracts + stats.purchaseContracts
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                {t("kpi.contracts.desc")}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions & Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {t("dashboard.quick_actions.title")}
              </CardTitle>
              <CardDescription>
                {t("dashboard.quick_actions.subtitle")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button asChild className="w-full justify-start">
                <a href="/customers">
                  <Users className="h-4 w-4 mr-2" />
                  {t("dashboard.quick_actions.manage_customers")}
                </a>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <a href="/products">
                  <Package className="h-4 w-4 mr-2" />
                  {t("dashboard.quick_actions.view_products")}
                </a>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <a href="/sales-contracts">
                  <FileText className="h-4 w-4 mr-2" />
                  {t("dashboard.quick_actions.create_contract")}
                </a>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <a href="/company-profile">
                  <Building className="h-4 w-4 mr-2" />
                  {t("dashboard.quick_actions.update_profile")}
                </a>
              </Button>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                {t("dashboard.system_status.title")}
              </CardTitle>
              <CardDescription>
                {t("dashboard.system_status.subtitle")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">{t("dashboard.system_status.company_profile")}</span>
                </div>
                <Badge variant="default">{t("dashboard.system_status.complete")}</Badge>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">{t("dashboard.system_status.customer_database")}</span>
                </div>
                <Badge variant="default">{t("dashboard.system_status.active")}</Badge>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">{t("dashboard.system_status.product_catalog")}</span>
                </div>
                <Badge variant="default">{t("dashboard.system_status.ready")}</Badge>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-orange-500" />
                  <span className="text-sm">{t("dashboard.system_status.first_contract")}</span>
                </div>
                <Badge variant="secondary">{t("dashboard.system_status.pending")}</Badge>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                  <span className="text-sm">{t("dashboard.system_status.inventory_setup")}</span>
                </div>
                <Badge variant="secondary">{t("dashboard.system_status.pending")}</Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Getting Started Guide */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>{t("dashboard.getting_started.title")}</CardTitle>
            <CardDescription>
              {t("dashboard.getting_started.subtitle")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <h3 className="font-semibold">{t("dashboard.getting_started.step1.title")}</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {t("dashboard.getting_started.step1.desc")}
                </p>
                <Badge variant="default">{t("dashboard.system_status.complete")}</Badge>
              </div>

              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-5 w-5 text-orange-500" />
                  <h3 className="font-semibold">{t("dashboard.getting_started.step2.title")}</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {t("dashboard.getting_started.step2.desc")}
                </p>
                <Button asChild size="sm">
                  <a href="/sales-contracts">{t("dashboard.getting_started.step2.action")}</a>
                </Button>
              </div>

              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="h-5 w-5 text-orange-500" />
                  <h3 className="font-semibold">{t("dashboard.getting_started.step3.title")}</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {t("dashboard.getting_started.step3.desc")}
                </p>
                <Button asChild size="sm" variant="outline">
                  <a href="/inventory">{t("dashboard.getting_started.step3.action")}</a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
