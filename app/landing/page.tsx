"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { LanguageSwitcher } from "@/components/language-switcher"
import { useI18n } from "@/components/i18n-provider"
import { FCChinaLogo } from "@/components/fc-china-logo"
import Link from "next/link"
import Image from "next/image"
import {
  Factory,
  Package,
  Ship,
  Users,
  CheckCircle,
  TrendingUp,
  Globe,
  Shield,
  Zap,
  ArrowRight,
  Star,
  Building,
  Truck,
  BarChart3
} from "lucide-react"

export default function LandingPage() {
  const { t } = useI18n()

  return (
    <div className="min-h-screen bg-white relative">
      {/* Global subtle grid overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(15,23,42,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(15,23,42,0.06)_1px,transparent_1px)] bg-[size:32px_32px] pointer-events-none" />

      {/* Header */}
      <header className="relative border-b border-gray-100 bg-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <FCChinaLogo size="md" href="/" />
          <div className="flex items-center gap-4">
            <Button asChild className="bg-slate-900 hover:bg-slate-800 text-white">
              <Link href="/api/auth/login">{t("landing.login")}</Link>
            </Button>
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative container mx-auto px-4 py-20">
        <div className="relative grid lg:grid-cols-2 gap-12 items-center max-w-7xl mx-auto">
          {/* Left side - Content */}
          <div className="space-y-8">
            <Badge variant="secondary" className="bg-slate-100 text-slate-700 border-slate-200">
              🚀 {t("landing.badge")}
            </Badge>
            <div className="space-y-6">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 leading-tight">
                {t("landing.hero.title")}
              </h1>
              <p className="text-xl text-slate-600 leading-relaxed max-w-lg">
                {t("landing.hero.subtitle")}
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" asChild className="bg-slate-900 hover:bg-slate-800 text-white text-lg px-8 py-3">
                <Link href="/api/auth/login?screen_hint=signup">
                  {t("landing.getStarted")} <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild className="text-lg px-8 py-3 border-slate-200 text-slate-900 hover:bg-slate-50">
                <Link href="#features">{t("landing.learnMore")}</Link>
              </Button>
            </div>
            <div className="flex items-center gap-6 text-sm text-slate-500">
              <span>✓ {t("landing.features.noCredit")}</span>
              <span>✓ {t("landing.features.freeTrial")}</span>
              <span>✓ {t("landing.features.quickSetup")}</span>
            </div>
          </div>

          {/* Right side - Dashboard mockup */}
          <div className="relative">
            <div className="bg-white rounded-2xl shadow-2xl border border-slate-200 overflow-hidden">
              <div className="bg-slate-50 px-4 py-3 border-b border-slate-200 flex items-center gap-2">
                <div className="flex gap-1.5">
                  <div className="w-3 h-3 rounded-full bg-red-400"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                  <div className="w-3 h-3 rounded-full bg-green-400"></div>
                </div>
                <div className="flex-1 text-center">
                  <div className="bg-white rounded px-3 py-1 text-xs text-slate-600 inline-block">
                    {t("app.name")}
                  </div>
                </div>
              </div>
              <div className="p-6 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-slate-50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="h-4 w-4 text-slate-600" />
                      <span className="text-sm font-medium text-slate-900">{t("kpi.customers")}</span>
                    </div>
                    <div className="text-2xl font-bold text-slate-900">1,247</div>
                  </div>
                  <div className="bg-slate-50 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Package className="h-4 w-4 text-slate-600" />
                      <span className="text-sm font-medium text-slate-900">{t("kpi.products")}</span>
                    </div>
                    <div className="text-2xl font-bold text-slate-900">856</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                    <span className="text-sm text-slate-700">{t("nav.item.sales-contracts")}</span>
                    <Badge variant="secondary" className="bg-green-100 text-green-700">24</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                    <span className="text-sm text-slate-700">{t("nav.item.quality-control")}</span>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-700">12</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                    <span className="text-sm text-slate-700">{t("nav.item.inventory")}</span>
                    <Badge variant="secondary" className="bg-purple-100 text-purple-700">8</Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="relative bg-slate-50 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              {t("landing.features.title")}
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              {t("landing.features.subtitle")}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            <FeatureCard
              icon={<Users className="h-8 w-8 text-slate-700" />}
              title={t("landing.features.crm.title")}
              description={t("landing.features.crm.description")}
            />
            <FeatureCard
              icon={<Package className="h-8 w-8 text-slate-700" />}
              title={t("landing.features.inventory.title")}
              description={t("landing.features.inventory.description")}
            />
            <FeatureCard
              icon={<Factory className="h-8 w-8 text-slate-700" />}
              title={t("landing.features.production.title")}
              description={t("landing.features.production.description")}
            />
            <FeatureCard
              icon={<CheckCircle className="h-8 w-8 text-slate-700" />}
              title={t("landing.features.quality.title")}
              description={t("landing.features.quality.description")}
            />
            <FeatureCard
              icon={<Ship className="h-8 w-8 text-slate-700" />}
              title={t("landing.features.export.title")}
              description={t("landing.features.export.description")}
            />
            <FeatureCard
              icon={<BarChart3 className="h-8 w-8 text-slate-700" />}
              title={t("landing.features.analytics.title")}
              description={t("landing.features.analytics.description")}
            />
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="relative bg-white py-20">
        {/* Subtle grid overlay */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(15,23,42,0.06)_1px,transparent_1px),linear-gradient(90deg,rgba(15,23,42,0.06)_1px,transparent_1px)] bg-[size:32px_32px] pointer-events-none" />
        <div className="relative container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              {t("landing.benefits.title")}
            </h2>
            <p className="text-xl text-slate-600">
              {t("landing.benefits.subtitle")}
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <BenefitCard
              icon={<Zap className="h-12 w-12 text-slate-700" />}
              title={t("landing.benefits.speed.title")}
              description={t("landing.benefits.speed.description")}
            />
            <BenefitCard
              icon={<Shield className="h-12 w-12 text-slate-700" />}
              title={t("landing.benefits.compliance.title")}
              description={t("landing.benefits.compliance.description")}
            />
            <BenefitCard
              icon={<Globe className="h-12 w-12 text-slate-700" />}
              title={t("landing.benefits.global.title")}
              description={t("landing.benefits.global.description")}
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative bg-slate-900 py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              {t("landing.cta.title")}
            </h2>
            <p className="text-xl text-slate-300 mb-8">
              {t("landing.cta.subtitle")}
            </p>
            <Button size="lg" asChild className="bg-white hover:bg-slate-100 text-slate-900 text-lg px-8 py-3">
              <Link href="/api/auth/login?screen_hint=signup">
                {t("landing.cta.button")} <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <p className="text-sm text-slate-400 mt-6">
              {t("landing.cta.features")}
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative border-t border-slate-200 bg-slate-50 py-12">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <FCChinaLogo size="sm" href="/" />
            </div>
            <p className="text-sm text-slate-600">
              {t("landing.footer.copyright")}
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

function FeatureCard({ icon, title, description }: {
  icon: React.ReactNode
  title: string
  description: string
}) {
  return (
    <Card className="border border-slate-200 bg-white hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
      <CardHeader className="pb-4">
        <div className="mb-3">{icon}</div>
        <CardTitle className="text-xl font-semibold text-slate-900">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <CardDescription className="text-base text-slate-600 leading-relaxed">{description}</CardDescription>
      </CardContent>
    </Card>
  )
}

function BenefitCard({ icon, title, description }: {
  icon: React.ReactNode
  title: string
  description: string
}) {
  return (
    <div className="text-center group">
      <div className="flex justify-center mb-6 group-hover:scale-110 transition-transform duration-200">{icon}</div>
      <h3 className="text-xl font-semibold text-slate-900 mb-3">{title}</h3>
      <p className="text-slate-600 leading-relaxed">{description}</p>
    </div>
  )
}
