"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { AppShell } from "@/components/app-shell"
import { BreadcrumbNavigation, createContractBreadcrumbs } from "@/components/breadcrumb-navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Download, FileText, Calendar, DollarSign, Building } from "lucide-react"
import { ContractDocumentViewer } from "@/components/contract-document-viewer"
import { useI18n } from "@/components/i18n-provider"

interface PurchaseContractViewClientProps {
  contract: any
  companyId: string
}

export function PurchaseContractViewClient({ contract, companyId }: PurchaseContractViewClientProps) {
  const router = useRouter()
  const { t } = useI18n()
  const [isLoading, setIsLoading] = useState(false)
  const [documentContent, setDocumentContent] = useState<string>("")

  // Generate breadcrumb navigation
  const breadcrumbs = createContractBreadcrumbs('purchase', contract.number, 'view')

  // Load contract document content
  useEffect(() => {
    async function loadContractDocument() {
      if (!contract.template_id) return

      setIsLoading(true)
      try {
        const response = await fetch(`/api/contracts/templates/${contract.template_id}/preview`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            number: contract.number,
            supplier_id: contract.supplier_id,
            date: contract.date,
            status: contract.status,
            currency: contract.currency,
            items: contract.items?.map((item: any) => ({
              product_id: item.product_id,
              qty: item.qty.toString(),
              price: item.price.toString(),
              product: item.product
            })) || []
          })
        })

        if (response.ok) {
          const data = await response.json()
          setDocumentContent(data.data?.template?.processed_content || "")
        } else {
          console.error('Failed to load contract document')
        }
      } catch (error) {
        console.error('Error loading contract document:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadContractDocument()
  }, [contract])

  // Calculate total amount
  const totalAmount = contract.items?.reduce((sum: number, item: any) => {
    return sum + (parseFloat(item.qty) * parseFloat(item.price))
  }, 0) || 0

  // Format status badge
  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      draft: "outline",
      active: "default",
      completed: "secondary",
      cancelled: "destructive"
    }
    const statusKey = `status.${status.toLowerCase()}` as keyof typeof t
    const statusText = t(statusKey) !== statusKey ? t(statusKey) : status.toUpperCase()
    return <Badge variant={variants[status] || "outline"}>{statusText}</Badge>
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* Header with Breadcrumbs */}
        <div className="space-y-4">
          <BreadcrumbNavigation items={breadcrumbs} />

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/purchase-contracts')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                {t("contracts.view.back_purchase")}
              </Button>

              <div>
                <h1 className="text-3xl font-bold tracking-tight">Purchase Contract {contract.number}</h1>
                <p className="text-muted-foreground">
                  Contract with {contract.supplier?.name || 'Unknown Supplier'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {getStatusBadge(contract.status)}
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/purchase-contracts/edit/${contract.id}`)}
              >
                {t("contracts.view.edit_contract")}
              </Button>
            </div>
          </div>
        </div>

        {/* Contract Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("contracts.view.supplier")}</CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{contract.supplier?.name || 'N/A'}</div>
              <p className="text-xs text-muted-foreground">
                {contract.supplier?.email || t("contracts.view.no_email")}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("contracts.view.contract_date")}</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Date(contract.date).toLocaleDateString()}
              </div>
              <p className="text-xs text-muted-foreground">
                Created {new Date(contract.created_at).toLocaleDateString()}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("contracts.view.total_value")}</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {contract.currency} {totalAmount.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {t("contracts.view.items_count", { count: contract.items?.length || 0 })}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t("contracts.view.template")}</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {contract.template?.name || 'N/A'}
              </div>
              <p className="text-xs text-muted-foreground">
                {t("contracts.view.purchase_template")}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Contract Document Viewer */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {t("contracts.view.contract_document")}
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                disabled={!documentContent || isLoading}
                onClick={async () => {
                  if (!documentContent) return
                  try {
                    const { generateContractPDF } = await import('@/components/pdf-contract-document')
                    await generateContractPDF(contract.number, documentContent, 'purchase')
                  } catch (error) {
                    console.error('PDF export error:', error)
                  }
                }}
              >
                <Download className="h-4 w-4" />
                {t("contracts.view.export_pdf")}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">{t("contracts.view.loading")}</p>
                </div>
              </div>
            ) : documentContent ? (
              <ContractDocumentViewer
                content={documentContent}
                contractNumber={contract.number}
                fullPage={true}
              />
            ) : (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">{t("contracts.view.no_document")}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppShell>
  )
}
