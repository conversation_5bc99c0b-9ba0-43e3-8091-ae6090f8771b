"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SupplierSelect } from "@/components/forms/supplier-select"
import { ProductSelect } from "@/components/forms/product-select"
import { TemplateSelect } from "@/components/forms/template-select"

import { zodResolver } from "@hookform/resolvers/zod"
import { ArrowLeft, Building, CreditCard, FileText, Package, Plus, Save, Trash2, X } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { useFieldArray, useForm } from "react-hook-form"
import { z } from "zod"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"

// Types
interface Supplier {
  id: string
  name: string
  contact_email?: string
  address?: string
}

interface Product {
  id: string
  name: string
  sku: string
  price?: string
  unit: string
  description?: string
}

interface ContractTemplate {
  id: string
  name: string
  type: string
  description?: string
}

interface PurchaseContract {
  id: string
  number: string
  supplier_id: string
  template_id?: string
  date: string
  currency?: string
  status: string
  created_at: Date | null
  supplier: {
    id: string
    name: string
    contact_name: string | null
    contact_phone: string | null
    contact_email: string | null
    address: string | null
    tax_id: string | null
    bank: string | null
    status: string | null
    created_at: Date | null
  }
  items: {
    id: string
    contract_id: string
    product_id: string
    qty: string
    price: string
    product: {
      id: string
      sku: string
      name: string
      unit: string
      hs_code: string | null
      origin: string | null
      package: string | null
      image: string | null
      created_at: Date | null
    }
  }[]
}

// Form validation schema
const formSchema = z.object({
  number: z.string().min(1, "Contract number is required"),
  supplier_id: z.string().min(1, "Supplier is required"),
  template_id: z.string().optional(),
  currency: z.string().min(1, "Currency is required"),
  items: z.array(
    z.object({
      product_id: z.string().min(1, "Product is required"),
      qty: z.coerce.number().min(0.01, "Quantity must be greater than 0"),
      price: z.coerce.number().min(0.01, "Price must be greater than 0"),
    })
  ).min(1, "At least one item is required"),
})

interface EditPurchaseContractPageProps {
  contract: PurchaseContract
  suppliers: Supplier[]
  products: Product[]
  templates: ContractTemplate[]
}

export function EditPurchaseContractPage({
  contract,
  suppliers,
  products,
  templates,
}: EditPurchaseContractPageProps) {
  const router = useRouter()
  const { t } = useI18n()
  const toast = useSafeToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      number: contract.number,
      supplier_id: contract.supplier_id,
      template_id: contract.template_id || "",
      currency: contract.currency || "USD",
      items: contract.items.map(item => ({
        product_id: item.product_id,
        qty: parseFloat(item.qty),
        price: parseFloat(item.price),
      })),
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)
    try {
      const response = await fetch(`/api/contracts/purchase/${contract.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      })

      if (response.ok) {
        toast.success("Purchase contract updated successfully!")
        router.push("/purchase-contracts")
        router.refresh()
      } else {
        const errorData = await response.json()
        toast.error("Failed to update purchase contract", errorData.message)
      }
    } catch (error) {
      console.error("Error updating contract:", error)
      toast.error("Failed to update purchase contract")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto max-w-4xl py-8 space-y-8">
      {/* Header with Breadcrumb */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Link href="/purchase-contracts" className="hover:text-foreground">
              {t("nav.purchase_contracts")}
            </Link>
            <span>/</span>
            <span>{t("contracts.edit.title_purchase")}</span>
          </div>
          <h1 className="text-3xl font-bold tracking-tight">{t("contracts.edit.title_purchase")}</h1>
          <p className="text-muted-foreground">
            {t("contracts.edit.subtitle", { number: contract.number })}
          </p>
        </div>
        <Link href="/purchase-contracts">
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("contracts.edit.back")}
          </Button>
        </Link>
      </div>

      {/* Edit Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Contract Details
          </CardTitle>
          <CardDescription>
            Update the contract information and items below.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Contract Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Contract Number
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="e.g., PC-2025-001" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="supplier_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Building className="h-4 w-4" />
                        Supplier
                      </FormLabel>
                      <SupplierSelect
                        suppliers={suppliers}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select supplier..."
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <CreditCard className="h-4 w-4" />
                        Currency
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="e.g., USD" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Template Selection */}
              <FormField
                control={form.control}
                name="template_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("contracts.edit.template_optional")}</FormLabel>
                    <TemplateSelect
                      templates={templates}
                      templateType="purchase"
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder="Select template..."
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Contract Items */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <FormLabel className="text-base font-semibold flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    {t("contracts.edit.items_title")}
                  </FormLabel>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => append({ product_id: "", qty: 1, price: 0 })}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    {t("contracts.edit.add_item")}
                  </Button>
                </div>

                <div className="space-y-4 border rounded-lg p-4">
                  {fields.map((field, index) => (
                    <div key={field.id} className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end p-4 border rounded-lg">
                      <FormField
                        control={form.control}
                        name={`items.${index}.product_id`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("contracts.edit.product")}</FormLabel>
                            <ProductSelect
                              products={products}
                              value={field.value}
                              onValueChange={field.onChange}
                              placeholder="Select product..."
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.qty`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("contracts.edit.quantity")}</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0.01"
                                {...field}
                                placeholder="0"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.price`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t("contracts.edit.unit_price")}</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                min="0.01"
                                {...field}
                                placeholder="0.00"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={() => remove(index)}
                        disabled={fields.length === 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}

                  {fields.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No items added yet. Click "Add Item" to get started.</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-4 pt-6 border-t">
                <Link href="/purchase-contracts">
                  <Button type="button" variant="outline">
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                </Link>
                <Button type="submit" disabled={isSubmitting}>
                  <Save className="mr-2 h-4 w-4" />
                  {isSubmitting ? "Updating..." : "Update Contract"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
