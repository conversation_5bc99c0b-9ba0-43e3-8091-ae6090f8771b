"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText, Plus, Trash2, Eye, FilePen } from "lucide-react";


import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { toast } from "sonner";
import { useI18n } from "@/components/i18n-provider";


// Type definitions matching the schema
type PurchaseContract = {
  id: string;
  number: string;
  supplier_id: string;
  template_id?: string;
  date: string;
  currency?: string;
  status: string;
  created_at: Date | null;
  supplier: {
    id: string;
    name: string;
    contact_name: string | null;
    contact_phone: string | null;
    contact_email: string | null;
    address: string | null;
    tax_id: string | null;
    bank: string | null;
    status: string | null;
    created_at: Date | null;
  };
  items: {
    id: string;
    contract_id: string;
    product_id: string;
    qty: string;
    price: string;
    product: {
      id: string;
      sku: string;
      name: string;
      unit: string;
      hs_code: string | null;
      origin: string | null;
      package: string | null;
      image: string | null;
      created_at: Date | null;
    };
  }[];
};

type Supplier = {
  id: string;
  name: string;
  contact_name: string | null;
  contact_phone: string | null;
  contact_email: string | null;
  address: string | null;
  tax_id: string | null;
  bank: string | null;
  status: string | null;
  created_at: Date | null;
};

type Product = {
  id: string;
  sku: string;
  name: string;
  unit: string;
  hs_code: string | null;
  origin: string | null;
  package: string | null;
  image: string | null;
  created_at: Date | null;
};

type ContractTemplate = {
  id: string;
  name: string;
  type: string;
  content: string;
  currency?: string;
  payment_terms?: string;
  delivery_terms?: string;
  language: string;
  version: number;
  is_active: boolean;
  created_at: Date | null;
};





export function PurchaseContractsClientPage({
  initialContracts,
  suppliers,
  products,
}: {
  initialContracts: PurchaseContract[];
  suppliers: Supplier[];
  products: Product[];
}) {
  const router = useRouter();
  const { t } = useI18n();
  const [contractToDelete, setContractToDelete] = useState<PurchaseContract | null>(null);
  const [templates, setTemplates] = useState<ContractTemplate[]>([]);

  // Load templates on component mount
  useEffect(() => {
    async function loadTemplates() {
      try {
        const response = await fetch("/api/contracts/templates?type=purchase");
        if (response.ok) {
          const data = await response.json();
          setTemplates(data.data?.templates || []);
        }
      } catch (error) {
        console.error("Failed to load templates:", error);
      }
    }
    loadTemplates();
  }, []);

  const handleViewDocument = (contract: PurchaseContract) => {
    // Navigate to the dedicated contract view page
    router.push(`/purchase-contracts/view/${contract.id}`);
  };

  const handleDelete = async () => {
    if (!contractToDelete) return;
    const response = await fetch(`/api/contracts/purchase/${contractToDelete.id}`, { method: "DELETE" });
    if (response.ok) {
      toast.success(t("purchase_contracts.success.deleted"));
      setContractToDelete(null);
      router.refresh();
    } else {
      toast.error(t("purchase_contracts.error.delete"));
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("purchase_contracts.title")}</h1>
          <p className="text-muted-foreground">{t("purchase_contracts.subtitle")}</p>
        </div>
        <Link href="/purchase-contracts/add">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            {t("purchase_contracts.add")}
          </Button>
        </Link>


      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("purchase_contracts.title")}</CardTitle>
          <CardDescription>{t("purchase_contracts.subtitle")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {initialContracts.map((contract) => (
              <Card key={contract.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <FileText className="h-6 w-6" />
                        <CardTitle className="text-lg">{contract.number}</CardTitle>
                        <Badge>{contract.status}</Badge>
                      </div>
                      <CardDescription>{t("field.supplier")}: {contract.supplier.name}</CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      {contract.template_id && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleViewDocument(contract)}
                          title="View Contract Document"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                      <Link href={`/purchase-contracts/edit/${contract.id}`}>
                        <Button
                          variant="ghost"
                          size="icon"
                          title="Edit Contract"
                        >
                          <FilePen className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button variant="ghost" size="icon" onClick={() => setContractToDelete(contract)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <h4 className="font-semibold">{t("purchase_contracts.form.items")} ({contract.items.length})</h4>
                  <ul className="list-disc pl-5 mt-2 text-sm">
                    {contract.items.map((item) => (
                      <li key={item.id}>
                        {item.product.name} - {item.qty} {item.product.unit} @ {item.price} {contract.currency}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>



      {/* Delete Alert Dialog */}
      <AlertDialog open={!!contractToDelete} onOpenChange={(open) => !open && setContractToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("purchase_contracts.delete.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("purchase_contracts.delete.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>{t("common.delete")}</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
