"use client"

import { AppShell } from "@/components/app-shell"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useEffect, useState } from "react"
import { safeJson } from "@/lib/safe-fetch"
import { useI18n } from "@/components/i18n-provider"
import { Clock, CheckCircle, XCircle, AlertTriangle } from "lucide-react"

export default function InventoryPage() {
  return (
    <AppShell>
      <div className="grid gap-6">
        <InboundCard />
        <OutboundCard />
        <StockCard />
        <TransactionsCard />
      </div>
    </AppShell>
  )
}

function InboundCard() {
  const { t } = useI18n()
  const [products, setProducts] = useState<any[]>([])
  const [form, setForm] = useState({ product_id: "", qty: 100, location: "Main", note: "" })

  useEffect(() => {
    safeJson("/api/products", { data: [] }).then((response) => {
      setProducts(response?.data || [])
    })
  }, [])

  async function add() {
    if (!form.product_id || !form.location || !form.qty) return
    await fetch("/api/inventory/inbound", { method: "POST", body: JSON.stringify(form) })
    setForm({ product_id: "", qty: 100, location: "Main", note: "" })
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    await (window as any).refreshInventory?.()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("inventory.inbound.title")}</CardTitle>
        <CardDescription>{t("inventory.inbound.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="grid gap-3 md:grid-cols-5">
        <div className="grid gap-1">
          <Label>{t("field.product")}</Label>
          <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v })}>
            <SelectTrigger>
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {products.map((p) => (
                <SelectItem key={p.id} value={p.id}>
                  {p.sku} - {p.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="grid gap-1">
          <Label>{t("field.qty")}</Label>
          <Input type="number" value={form.qty} onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })} />
        </div>
        <div className="grid gap-1">
          <Label>{t("field.location")}</Label>
          <Input value={form.location} onChange={(e) => setForm({ ...form, location: e.target.value })} />
        </div>
        <div className="grid gap-1 md:col-span-2">
          <Label>{t("field.note")}</Label>
          <Input value={form.note} onChange={(e) => setForm({ ...form, note: e.target.value })} />
        </div>
        <div className="md:col-span-5">
          <Button onClick={add}>{t("action.addInbound")}</Button>
        </div>
      </CardContent>
    </Card>
  )
}

function OutboundCard() {
  const { t } = useI18n()
  const [products, setProducts] = useState<any[]>([])
  const [form, setForm] = useState({ product_id: "", qty: 10, location: "Main", ref: "" })

  useEffect(() => {
    safeJson("/api/products", { data: [] }).then((response) => {
      setProducts(response?.data || [])
    })
  }, [])

  async function ship() {
    if (!form.product_id || !form.qty || !form.location) return
    const res = await fetch("/api/inventory/outbound", { method: "POST", body: JSON.stringify(form) })
    if (!res.ok) {
      alert(await res.text())
      return
    }
    setForm({ product_id: "", qty: 10, location: "Main", ref: "" })
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    await (window as any).refreshInventory?.()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("inventory.outbound.title")}</CardTitle>
        <CardDescription>{t("inventory.outbound.desc")}</CardDescription>
      </CardHeader>
      <CardContent className="grid gap-3 md:grid-cols-5">
        <div className="grid gap-1">
          <Label>{t("field.product")}</Label>
          <Select value={form.product_id} onValueChange={(v) => setForm({ ...form, product_id: v })}>
            <SelectTrigger>
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {products.map((p) => (
                <SelectItem key={p.id} value={p.id}>
                  {p.sku} - {p.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="grid gap-1">
          <Label>{t("field.qty")}</Label>
          <Input type="number" value={form.qty} onChange={(e) => setForm({ ...form, qty: Number(e.target.value) })} />
        </div>
        <div className="grid gap-1">
          <Label>{t("field.location")}</Label>
          <Input value={form.location} onChange={(e) => setForm({ ...form, location: e.target.value })} />
        </div>
        <div className="grid gap-1 md:col-span-2">
          <Label>{t("field.reference")}</Label>
          <Input
            value={form.ref}
            onChange={(e) => setForm({ ...form, ref: e.target.value })}
            placeholder="SO/Shipment"
          />
        </div>
        <div className="md:col-span-5">
          <Button onClick={ship}>{t("action.addOutbound")}</Button>
        </div>
      </CardContent>
    </Card>
  )
}

function StockCard() {
  const { t } = useI18n()
  const [lots, setLots] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [qualityFilter, setQualityFilter] = useState<string>("all")

  async function refresh() {
    try {
      setLoading(true)
      setError(null)

      // Build API URL with quality status filter
      const lotsUrl = qualityFilter === "all"
        ? "/api/inventory/lots"
        : `/api/inventory/lots?quality_status=${qualityFilter}`

      const [lotsResponse, productsResponse] = await Promise.all([safeJson(lotsUrl, { data: [] }), safeJson("/api/products", { data: [] })])

      // Extract data from API response structure
      const l = lotsResponse?.data || []
      const p = productsResponse?.data || []

      // Ensure we always have arrays
      setLots(Array.isArray(l) ? l : [])
      setProducts(Array.isArray(p) ? p : [])
    } catch (err) {
      console.error('Error refreshing inventory:', err)
      setError('Failed to load inventory data')
      setLots([])
      setProducts([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    ; (window as any).refreshInventory = refresh
    refresh()
    return () => {
      ; (window as any).refreshInventory = undefined
    }
  }, [qualityFilter]) // Refresh when quality filter changes

  // Quality status badge component - Professional 4-status system
  const QualityStatusBadge = ({ status }: { status: string }) => {
    const statusConfig = {
      pending: { label: t("quality.status.pending"), icon: Clock, variant: "outline" as const },
      approved: { label: t("quality.status.approved"), icon: CheckCircle, variant: "default" as const },
      quarantined: { label: t("quality.status.quarantined"), icon: AlertTriangle, variant: "secondary" as const },
      rejected: { label: t("quality.status.rejected"), icon: XCircle, variant: "destructive" as const },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.approved
    const Icon = config.icon

    return (
      <Badge variant={config.variant}>
        <Icon className="mr-1 h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("inventory.stock.title")}</CardTitle>
        <CardDescription>{t("inventory.stock.desc")}</CardDescription>
      </CardHeader>
      <CardContent>
        {/* Quality Status Filter - Aligned with Stock Lot Quality Statuses */}
        <div className="mb-4">
          <Label>Quality Status Filter</Label>
          <Select value={qualityFilter} onValueChange={setQualityFilter}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t("common.filter")} - {t("common.status")}</SelectItem>
              <SelectItem value="pending">{t("quality.status.pending")}</SelectItem>
              <SelectItem value="approved">{t("quality.status.approved")}</SelectItem>
              <SelectItem value="quarantined">{t("quality.status.quarantined")}</SelectItem>
              <SelectItem value="rejected">{t("quality.status.rejected")}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2">{t("loading.inventory")}</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-600 mb-2">{error}</p>
            <button
              type="button"
              onClick={refresh}
              className="text-blue-600 hover:underline"
            >
              {t("loading.try_again")}
            </button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("field.product")}</TableHead>
                <TableHead>{t("header.lot")}</TableHead>
                <TableHead>{t("field.qty")}</TableHead>
                <TableHead>{t("field.location")}</TableHead>
                <TableHead>{t("quality.status")}</TableHead>
                <TableHead>{t("workOrder.title")}</TableHead>
                <TableHead>{t("field.note")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.isArray(lots) && lots.map((l) => (
                <TableRow key={l.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{l.product?.name || "N/A"}</div>
                      <div className="text-sm text-muted-foreground">{l.product?.sku || l.product_id}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-mono text-sm">{l.lot_number || l.id.slice(-8)}</div>
                      {l.expiry_date && <div className="text-xs text-muted-foreground">Exp: {l.expiry_date}</div>}
                    </div>
                  </TableCell>
                  <TableCell>{l.qty} {products.find((p) => p.id === l.product_id)?.unit || ""}</TableCell>
                  <TableCell>{l.location}</TableCell>
                  <TableCell>
                    <QualityStatusBadge status={l.quality_status || "pending"} />
                  </TableCell>
                  <TableCell>
                    {l.workOrder ? (
                      <div className="text-sm">
                        <div className="font-medium">{l.workOrder.number}</div>
                        <div className="text-xs text-muted-foreground">
                          {l.workOrder.status === 'completed' ? (
                            <span className="text-green-600">✅ {t("workOrder.status.completed")}</span>
                          ) : l.workOrder.status === 'in-progress' ? (
                            <span className="text-blue-600">🔄 {t("workOrder.status.in-progress")}</span>
                          ) : (
                            <span className="text-gray-600">⏳ {t("workOrder.status.pending")}</span>
                          )}
                        </div>
                      </div>
                    ) : l.work_order_id ? (
                      <div className="text-sm">
                        <div className="font-mono text-muted-foreground">{l.work_order_id.slice(-8)}</div>
                        <div className="text-xs text-muted-foreground">{t("common.loading")}...</div>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>{l.quality_notes || l.note || ""}</TableCell>
                </TableRow>
              ))}
              {(!Array.isArray(lots) || lots.length === 0) && (
                <TableRow>
                  <TableCell colSpan={7} className="text-muted-foreground">
                    {qualityFilter === "all" ? t("inventory.stock.empty") : `No stock with ${qualityFilter} quality status`}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}

function TransactionsCard() {
  const { t } = useI18n()
  const [txns, setTxns] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])

  async function refresh() {
    try {
      const [txnsResponse, productsResponse] = await Promise.all([safeJson("/api/inventory/txns", { data: [] }), safeJson("/api/products", { data: [] })])

      const tRows = txnsResponse?.data || []
      const p = productsResponse?.data || []

      setTxns(Array.isArray(tRows) ? tRows : [])
      setProducts(Array.isArray(p) ? p : [])
    } catch (err) {
      console.error('Error refreshing transactions:', err)
      setTxns([])
      setProducts([])
    }
  }

  useEffect(() => {
    refresh()
  }, [])

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("inventory.txns.title")}</CardTitle>
        <CardDescription>{t("inventory.txns.desc")}</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("header.type")}</TableHead>
              <TableHead>{t("field.product")}</TableHead>
              <TableHead>{t("field.qty")}</TableHead>
              <TableHead>{t("field.location")}</TableHead>
              <TableHead>Workflow Trigger</TableHead>
              <TableHead>{t("field.reference")}</TableHead>
              <TableHead>{t("header.time")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.isArray(txns) && txns.map((tRow) => (
              <TableRow key={tRow.id}>
                <TableCell>
                  <Badge variant={tRow.type === "inbound" ? "default" : "secondary"}>
                    {tRow.type}
                  </Badge>
                </TableCell>
                <TableCell>{tRow.product?.name || tRow.product_id}</TableCell>
                <TableCell>{tRow.qty}</TableCell>
                <TableCell>{tRow.location || "N/A"}</TableCell>
                <TableCell>
                  {tRow.workflow_trigger ? (
                    <Badge variant="outline">
                      {tRow.workflow_trigger.replace(/_/g, " ")}
                    </Badge>
                  ) : (
                    <span className="text-muted-foreground">Manual</span>
                  )}
                </TableCell>
                <TableCell>{tRow.reference || tRow.ref || ""}</TableCell>
                <TableCell>{new Date(tRow.created_at).toLocaleString()}</TableCell>
              </TableRow>
            ))}
            {(!Array.isArray(txns) || txns.length === 0) && (
              <TableRow>
                <TableCell colSpan={7} className="text-muted-foreground">
                  {t("inventory.txns.empty")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
