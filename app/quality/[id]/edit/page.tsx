import { Suspense } from "react"
import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect, notFound } from "next/navigation"
import { db } from "@/lib/db"
import { qualityInspections, workOrders } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { QualityInspectionEdit } from "./quality-inspection-edit"

async function getQualityInspection(id: string, companyId: string) {
  try {
    const inspection = await db.query.qualityInspections.findFirst({
      where: and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, companyId)
      ),
      with: {
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true
              }
            }
          }
        }
      }
    })

    return inspection
  } catch (error) {
    console.error("Error fetching quality inspection:", error)
    return null
  }
}

async function getWorkOrders(companyId: string) {
  try {
    const workOrdersList = await db.query.workOrders.findMany({
      where: eq(workOrders.company_id, companyId),
      with: {
        product: true,
        salesContract: {
          with: {
            customer: true
          }
        }
      }
    })

    return workOrdersList
  } catch (error) {
    console.error("Error fetching work orders:", error)
    return []
  }
}

interface QualityInspectionEditPageProps {
  params: Promise<{ id: string }>
}

export default async function QualityInspectionEditPage({ params }: QualityInspectionEditPageProps) {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const { id } = await params
  const [inspection, workOrdersList] = await Promise.all([
    getQualityInspection(id, context.companyId),
    getWorkOrders(context.companyId)
  ])

  if (!inspection) {
    notFound()
  }

  return (
    <AppShell>
      <Suspense fallback={<div>Loading quality inspection editor...</div>}>
        <QualityInspectionEdit 
          inspection={inspection} 
          workOrders={workOrdersList}
        />
      </Suspense>
    </AppShell>
  )
}
