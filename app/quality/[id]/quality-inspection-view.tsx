"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { AttachmentManager } from "@/components/quality/attachment-manager"
import { UnarchiveInspectionDialog } from "@/components/quality/unarchive-inspection-dialog"
import {
  ArrowLeft,
  Edit,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  FileText,
  User,
  Calendar,
  Package,
  Building2,
  RefreshCw,
  Archive,
  RotateCcw
} from "lucide-react"
import Link from "next/link"

interface QualityInspectionViewProps {
  inspection: any
}

export function QualityInspectionView({ inspection }: QualityInspectionViewProps) {
  const { t } = useI18n()
  const { success: toastSuccess, error: toastError } = useSafeToast()
  const [loading, setLoading] = useState(false)
  const [showUnarchiveDialog, setShowUnarchiveDialog] = useState(false)

  // ✅ PROFESSIONAL ERP: STATUS HELPERS
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'pending': { variant: 'outline' as const, icon: Clock, color: 'text-yellow-600' },
      'in_progress': { variant: 'secondary' as const, icon: AlertTriangle, color: 'text-blue-600' },
      'passed': { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      'failed': { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    const Icon = config.icon

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status?.replace('_', ' ') || 'Unknown'}
      </Badge>
    )
  }

  // ✅ PROFESSIONAL ERP: UPDATE STATUS
  const handleStatusUpdate = async (newStatus: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/quality/inspections/${inspection.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      })

      if (response.ok) {
        toastSuccess(`Inspection status updated to ${newStatus}`)
        // Refresh the page to show updated data
        window.location.reload()
      } else {
        toastError('Failed to update inspection status')
      }
    } catch (error) {
      console.error('Status update error:', error)
      toastError('Failed to update inspection status')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* ✅ PROFESSIONAL HEADER WITH BREADCRUMB */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/quality">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Quality Control
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Quality Inspection</h1>
            <p className="text-muted-foreground">
              Inspection details and results
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href={`/quality/${inspection.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* ✅ INSPECTION DETAILS */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Inspection Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Status</span>
                <div className="flex items-center gap-2">
                  {getStatusBadge(inspection.status)}
                  {inspection.status === 'pending' && (
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleStatusUpdate('passed')}
                        disabled={loading}
                      >
                        {loading ? <RefreshCw className="h-3 w-3 animate-spin" /> : <CheckCircle className="h-3 w-3" />}
                        Pass
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleStatusUpdate('failed')}
                        disabled={loading}
                      >
                        {loading ? <RefreshCw className="h-3 w-3 animate-spin" /> : <XCircle className="h-3 w-3" />}
                        Fail
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Inspector</span>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{inspection.inspector}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Type</span>
                <span className="capitalize font-medium">
                  {inspection.inspection_type?.replace('_', ' ')}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Scheduled Date</span>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">
                    {inspection.scheduled_date ?
                      new Date(inspection.scheduled_date).toLocaleDateString() :
                      'Not scheduled'
                    }
                  </span>
                </div>
              </div>

              {inspection.completed_date && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-muted-foreground">Completed Date</span>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">
                      {new Date(inspection.completed_date).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              )}

              {inspection.notes && (
                <>
                  <Separator />
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Notes</span>
                    <p className="mt-1 text-sm">{inspection.notes}</p>
                  </div>
                </>
              )}

              {/* ✅ ARCHIVE INFORMATION */}
              {inspection.archived && (
                <>
                  <Separator />
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Archive className="h-4 w-4 text-orange-600" />
                        <span className="text-sm font-medium text-orange-800">Archived Inspection</span>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowUnarchiveDialog(true)}
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        <RotateCcw className="h-3 w-3 mr-1" />
                        Unarchive
                      </Button>
                    </div>
                    <div className="space-y-1 text-sm text-orange-700">
                      <div><strong>Reason:</strong> {inspection.archive_reason?.replace('_', ' ') || 'Not specified'}</div>
                      {inspection.archived_at && (
                        <div><strong>Archived:</strong> {new Date(inspection.archived_at).toLocaleDateString()}</div>
                      )}
                      {inspection.archived_by && (
                        <div><strong>Archived by:</strong> {inspection.archived_by}</div>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* ✅ WORK ORDER DETAILS */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Work Order Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Work Order</span>
                <Link
                  href={`/production/${inspection.work_order_id}`}
                  className="font-medium text-blue-600 hover:underline"
                >
                  {inspection.workOrder?.number || 'N/A'}
                </Link>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Product</span>
                <div className="text-right">
                  <div className="font-medium">{inspection.workOrder?.product?.sku || 'N/A'}</div>
                  <div className="text-sm text-muted-foreground">
                    {inspection.workOrder?.product?.name || 'N/A'}
                  </div>
                </div>
              </div>

              {inspection.workOrder?.salesContract?.customer && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-muted-foreground">Customer</span>
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <Link
                      href={`/customers/${inspection.workOrder.salesContract.customer.id}`}
                      className="font-medium text-blue-600 hover:underline"
                    >
                      {inspection.workOrder.salesContract.customer.name}
                    </Link>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Quantity</span>
                <span className="font-medium">{inspection.workOrder?.qty || 'N/A'}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Work Order Status</span>
                <Badge variant="outline">
                  {inspection.workOrder?.status?.replace('_', ' ') || 'Unknown'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ✅ INSPECTION RESULTS (if any) */}
      {inspection.results && inspection.results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Inspection Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {inspection.results.map((result: any) => (
                <div key={result.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{result.standard?.name || 'Test'}</span>
                    <Badge variant={result.result === 'pass' ? 'default' : 'destructive'}>
                      {result.result}
                    </Badge>
                  </div>
                  {result.measured_value && (
                    <div className="text-sm text-muted-foreground">
                      Measured: {result.measured_value} {result.unit}
                    </div>
                  )}
                  {result.notes && (
                    <div className="text-sm mt-2">{result.notes}</div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ DEFECTS (if any) */}
      {inspection.defects && inspection.defects.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              Quality Defects
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {inspection.defects.map((defect: any) => (
                <div key={defect.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{defect.defect_type?.replace('_', ' ')}</span>
                    <Badge variant="destructive">
                      {defect.severity}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mb-2">
                    Affected Quantity: {defect.quantity}
                  </div>
                  {defect.description && (
                    <div className="text-sm mb-2">{defect.description}</div>
                  )}
                  {defect.corrective_action && (
                    <div className="text-sm">
                      <span className="font-medium">Corrective Action: </span>
                      {defect.corrective_action}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ CERTIFICATES (if any) */}
      {inspection.certificates && inspection.certificates.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Quality Certificates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {inspection.certificates.map((cert: any) => (
                <div key={cert.id} className="flex items-center justify-between border rounded-lg p-4">
                  <div>
                    <div className="font-medium">{cert.certificate_type}</div>
                    <div className="text-sm text-muted-foreground">
                      Issued: {new Date(cert.issue_date).toLocaleDateString()}
                    </div>
                    {cert.expiry_date && (
                      <div className="text-sm text-muted-foreground">
                        Expires: {new Date(cert.expiry_date).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                  <Button variant="outline" size="sm">
                    <FileText className="h-4 w-4 mr-2" />
                    View Certificate
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ ATTACHMENT DISPLAY (READ-ONLY) */}
      <AttachmentManager
        inspectionId={inspection.id}
        attachments={inspection.attachments ? JSON.parse(inspection.attachments) : []}
        photos={inspection.photos ? JSON.parse(inspection.photos) : []}
        readOnly={true}
      />

      {/* ✅ ERP STANDARD: Unarchive Dialog */}
      {inspection.archived && (
        <UnarchiveInspectionDialog
          isOpen={showUnarchiveDialog}
          onClose={() => setShowUnarchiveDialog(false)}
          onUnarchived={() => {
            // Refresh the page to show updated state
            window.location.reload()
          }}
          inspectionId={inspection.id}
          archiveInfo={{
            reason: inspection.archive_reason || 'Not specified',
            archivedAt: inspection.archived_at ? new Date(inspection.archived_at).toLocaleDateString() : 'Unknown',
            archivedBy: inspection.archived_by || 'Unknown'
          }}
        />
      )}
    </div>
  )
}
