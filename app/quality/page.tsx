import { Suspense } from "react"
import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"
import { db } from "@/lib/db"
import { qualityInspections, workOrders } from "@/lib/schema-postgres"
import { eq, and, desc, not } from "drizzle-orm"
import { QualityInspectionsContent } from "./quality-inspections-content"

async function getQualityInspections(companyId: string) {
  try {
    const inspections = await db.query.qualityInspections.findMany({
      where: eq(qualityInspections.company_id, companyId),
      with: {
        workOrder: {
          with: {
            product: true,
            salesContract: {
              with: {
                customer: true
              }
            }
          }
        }
      },
      orderBy: [desc(qualityInspections.created_at)]
    })

    return inspections
  } catch (error) {
    console.error("Error fetching quality inspections:", error)
    return []
  }
}

async function getWorkOrders(companyId: string) {
  try {
    // Get work orders that are completed or in progress (ready for quality inspection)
    const workOrdersList = await db.query.workOrders.findMany({
      where: and(
        eq(workOrders.company_id, companyId),
        // Only show work orders that could need quality inspection
      ),
      with: {
        product: true,
        salesContract: {
          with: {
            customer: true
          }
        }
      },
      orderBy: [desc(workOrders.created_at)]
    })

    return workOrdersList
  } catch (error) {
    console.error("Error fetching work orders:", error)
    return []
  }
}

export default async function QualityPage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }

  const [inspections, workOrdersList] = await Promise.all([
    getQualityInspections(context.companyId),
    getWorkOrders(context.companyId)
  ])

  return (
    <AppShell>
      <Suspense fallback={<div>Loading quality inspections...</div>}>
        <QualityInspectionsContent
          initialInspections={inspections}
          workOrders={workOrdersList}
        />
      </Suspense>
    </AppShell>
  )
}