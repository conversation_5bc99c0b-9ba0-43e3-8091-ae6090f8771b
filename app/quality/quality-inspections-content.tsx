"use client"

import { useState, use<PERSON><PERSON><PERSON>, use<PERSON>ffect, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { ArchiveInspectionDialog } from "@/components/quality/archive-inspection-dialog"
import { UnarchiveInspectionDialog } from "@/components/quality/unarchive-inspection-dialog"
import {
  Plus,
  Search,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Eye,
  Edit,
  Archive,
  FileText,
  <PERSON><PERSON>,
  Ch<PERSON>ronDown,
  ChevronRight,
  Building2,
  Package,
  Settings,
  PlayCircle,
  FolderArchive,
  RotateCcw
} from "lucide-react"
import Link from "next/link"
import { InlineStatusEditor } from "@/components/quality/inline-status-editor"

interface QualityInspectionsContentProps {
  initialInspections: any[]
  workOrders: any[]
}

// ✅ PROFESSIONAL ERP: EXACT SAME PATTERN AS WORK ORDERS
function renderGroupedInspections(
  inspections: any[],
  expandedContracts: Set<string>,
  toggleContract: (contractId: string) => void,
  handleArchive: (id: string) => void,
  handleUnarchive: (id: string) => void,
  handleStatusChange: (inspectionId: string, newStatus: string) => void
) {
  // Group inspections by sales contract (via work order) - EXACTLY like work orders
  const groupedByContract = inspections.reduce((groups, inspection) => {
    const contractId = inspection.workOrder?.sales_contract_id || 'no-contract'
    if (!groups[contractId]) {
      groups[contractId] = []
    }
    groups[contractId].push(inspection)
    return groups
  }, {} as Record<string, any[]>)

  const rows: JSX.Element[] = []

  Object.entries(groupedByContract).forEach(([contractId, contractInspections]) => {
    const contract = contractInspections[0]?.workOrder?.salesContract
    const isMultiInspection = contractInspections.length > 1
    const isExpanded = expandedContracts.has(contractId)

    // ✅ CONTRACT HEADER ROW - EXACTLY LIKE WORK ORDERS
    if (contract) {
      // Calculate progress like work orders
      const totalInspections = contractInspections.length
      const completedInspections = contractInspections.filter(i => i.status === 'passed').length
      const progressPercent = totalInspections > 0 ? Math.round((completedInspections / totalInspections) * 100) : 0

      rows.push(
        <TableRow
          key={`contract-${contractId}`}
          className="bg-slate-50 hover:bg-slate-100 cursor-pointer border-b-2"
          onClick={() => toggleContract(contractId)}
        >
          <TableCell className="font-semibold">
            <div className="flex items-center gap-2">
              {isMultiInspection ? (
                isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
              ) : null}
              <Link
                href={`/sales-contracts/${contract.id}`}
                className="text-blue-600 hover:underline"
                onClick={(e) => e.stopPropagation()}
              >
                {contract.number}
              </Link>
            </div>
          </TableCell>
          <TableCell className="text-muted-foreground">
            {totalInspections} inspection{totalInspections !== 1 ? 's' : ''}
          </TableCell>
          <TableCell>
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">{contract.customer?.name || 'N/A'}</span>
            </div>
          </TableCell>
          <TableCell className="text-muted-foreground">
            Progress: {completedInspections}/{totalInspections} ({progressPercent}%)
          </TableCell>
          <TableCell></TableCell>
          <TableCell></TableCell>
          <TableCell></TableCell>
        </TableRow>
      )
    }

    // ✅ INSPECTION ROWS - EXACTLY LIKE WORK ORDER SUB-ROWS
    if (!isMultiInspection || isExpanded) {
      contractInspections.forEach((inspection) => {
        const product = inspection.workOrder?.product

        rows.push(
          <TableRow key={inspection.id} className="border-l-4 border-l-blue-200 bg-blue-50/30">
            <TableCell className="pl-8">
              <div className="flex items-center gap-2">
                <Link
                  href={`/quality/${inspection.id}`}
                  className="text-blue-600 hover:underline font-medium"
                >
                  QI-{inspection.id.slice(-6).toUpperCase()}
                </Link>
                {inspection.archived && (
                  <Badge variant="outline" className="text-orange-600 border-orange-300">
                    <Archive className="h-3 w-3 mr-1" />
                    Archived
                  </Badge>
                )}
              </div>
            </TableCell>
            <TableCell className="text-muted-foreground text-sm">
              {/* Clean visual hierarchy - no redundant text needed */}
            </TableCell>
            <TableCell>
              <Link
                href={`/production/${inspection.work_order_id}`}
                className="text-blue-600 hover:underline text-sm"
              >
                {inspection.workOrder?.number || 'N/A'}
              </Link>
            </TableCell>
            <TableCell>
              <div className="space-y-1">
                <div className="font-medium">{product?.sku || "N/A"}</div>
                <div className="text-sm text-muted-foreground">{product?.name || "N/A"}</div>
              </div>
            </TableCell>
            <TableCell>
              <span className="font-medium">{inspection.inspector}</span>
            </TableCell>
            <TableCell>
              <InlineStatusEditor
                inspectionId={inspection.id}
                currentStatus={inspection.status || "pending"}
                onStatusChange={(newStatus) => handleStatusChange(inspection.id, newStatus)}
              />
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/quality/${inspection.id}`}>
                    <Eye className="h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/quality/${inspection.id}/edit`}>
                    <Edit className="h-4 w-4" />
                  </Link>
                </Button>
                {inspection.archived ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleUnarchive(inspection.id)}
                    className="text-blue-600 hover:text-blue-700"
                    title="Unarchive inspection"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleArchive(inspection.id)}
                    className="text-orange-600 hover:text-orange-700"
                    title="Archive inspection (compliance-friendly)"
                  >
                    <Archive className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </TableCell>
          </TableRow>
        )
      })
    }
  })

  return rows
}

// ✅ REMOVED: getStatusBadge function - now using InlineStatusEditor

export function QualityInspectionsContent({
  initialInspections,
  workOrders
}: QualityInspectionsContentProps) {
  const { t } = useI18n()
  const { success: toastSuccess, error: toastError } = useSafeToast()

  const [inspections, setInspections] = useState(initialInspections)
  const [loading, setLoading] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [archiveDialogOpen, setArchiveDialogOpen] = useState(false)
  const [inspectionToArchive, setInspectionToArchive] = useState<string | null>(null)
  const [unarchiveDialogOpen, setUnarchiveDialogOpen] = useState(false)
  const [inspectionToUnarchive, setInspectionToUnarchive] = useState<string | null>(null)

  // ✅ COLLAPSIBLE CONTRACT STATE - START EXPANDED FOR PROFESSIONAL UX
  const [expandedContracts, setExpandedContracts] = useState<Set<string>>(new Set())

  // ✅ AUTO-EXPAND ALL CONTRACTS ON LOAD FOR PROFESSIONAL ERP UX
  useEffect(() => {
    if (inspections.length > 0) {
      const contractIds = [...new Set(inspections.map(i => i.workOrder?.sales_contract_id).filter(Boolean))]
      setExpandedContracts(new Set(contractIds))
    }
  }, [inspections])

  const toggleContract = (contractId: string) => {
    setExpandedContracts(prev => {
      const newSet = new Set(prev)
      if (newSet.has(contractId)) {
        newSet.delete(contractId)
      } else {
        newSet.add(contractId)
      }
      return newSet
    })
  }

  // ✅ MANUFACTURING ERP: Inline status editing handler
  const handleStatusChange = useCallback((inspectionId: string, newStatus: string) => {
    setInspections(prev => prev.map(inspection =>
      inspection.id === inspectionId
        ? { ...inspection, status: newStatus }
        : inspection
    ))
  }, [])

  // ✅ FILTERS STATE (Following established patterns)
  const [filters, setFilters] = useState({
    search: "",
    status: "all",
    work_order_id: "",
  })

  // ✅ CREATE FORM STATE
  const [createForm, setCreateForm] = useState({
    work_order_id: "",
    inspector: "",
    inspection_type: "final",
    scheduled_date: new Date().toISOString().split('T')[0],
    notes: ""
  })

  // ✅ PROFESSIONAL ERP: LOAD DATA - IMPROVED ERROR HANDLING
  const loadInspections = useCallback(async () => {
    try {
      console.log('🔄 Loading inspections...')
      setLoading(true)

      const response = await fetch('/api/quality/inspections')
      console.log('📡 Load Response:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      })

      if (response.ok) {
        const data = await response.json()
        const inspectionsData = data.data || data
        console.log('✅ Loaded inspections:', {
          count: inspectionsData.length,
          sample: inspectionsData.slice(0, 2).map(i => ({ id: i.id, inspector: i.inspector }))
        })
        setInspections(inspectionsData)
      } else {
        const errorText = await response.text()
        console.error('❌ Failed to load inspections:', {
          status: response.status,
          error: errorText
        })
        toastError(`Failed to load inspections: ${errorText}`)
      }
    } catch (error) {
      console.error('❌ Network error loading inspections:', error)
      toastError('Network error: Failed to load quality inspections')
    } finally {
      setLoading(false)
    }
  }, [toastError])

  // ✅ PROFESSIONAL ERP: CREATE INSPECTION - IMPROVED ERROR HANDLING
  const handleCreate = async () => {
    if (!createForm.work_order_id || !createForm.inspector) {
      toastError('Please select a work order and enter inspector name')
      return
    }

    try {
      setLoading(true)
      console.log('🔍 Creating inspection:', createForm)

      const response = await fetch('/api/quality/inspections', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(createForm)
      })

      console.log('📡 API Response:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      })

      if (response.ok) {
        const result = await response.json()
        console.log('✅ Inspection created:', result)

        toastSuccess('Quality inspection created successfully')

        // Reset form
        setCreateForm({
          work_order_id: "",
          inspector: "",
          inspection_type: "final",
          scheduled_date: new Date().toISOString().split('T')[0],
          notes: ""
        })

        // Close form
        setShowCreateForm(false)

        // ✅ APPLY WORKING PATTERN: Use same refresh mechanism as archive/unarchive
        console.log('🔄 Refreshing inspection table using proven pattern...')
        await loadInspections()

        // Force page refresh to show updated data (same pattern as archive/unarchive)
        setTimeout(() => {
          window.location.reload()
        }, 1000)

      } else {
        // Handle API errors
        let errorMessage = 'Unknown error occurred'
        try {
          const errorData = await response.json()
          errorMessage = errorData.error || errorData.message || errorMessage
        } catch {
          // If JSON parsing fails, try text
          try {
            errorMessage = await response.text()
          } catch {
            errorMessage = `HTTP ${response.status}: ${response.statusText}`
          }
        }

        console.error('❌ API Error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorMessage,
          formData: createForm
        })

        toastError(`Failed to create inspection: ${errorMessage}`)
      }
    } catch (error) {
      console.error('❌ Network/Client Error:', error)
      toastError('Network error: Failed to create quality inspection')
    } finally {
      setLoading(false)
    }
  }

  // ✅ COMPLIANCE: ARCHIVE INSPECTION (instead of delete)
  const handleArchive = (id: string) => {
    setInspectionToArchive(id)
    setArchiveDialogOpen(true)
  }

  const handleArchiveComplete = async () => {
    await loadInspections()
    setArchiveDialogOpen(false)
    setInspectionToArchive(null)
  }

  const handleUnarchive = (id: string) => {
    setInspectionToUnarchive(id)
    setUnarchiveDialogOpen(true)
  }

  const handleUnarchiveComplete = async () => {
    await loadInspections()
    setUnarchiveDialogOpen(false)
    setInspectionToUnarchive(null)
  }

  // ✅ PROFESSIONAL ERP: FILTER INSPECTIONS
  const filteredInspections = inspections.filter(inspection => {
    const matchesSearch = !filters.search ||
      inspection.inspector?.toLowerCase().includes(filters.search.toLowerCase()) ||
      inspection.workOrder?.number?.toLowerCase().includes(filters.search.toLowerCase()) ||
      inspection.workOrder?.salesContract?.number?.toLowerCase().includes(filters.search.toLowerCase()) ||
      inspection.workOrder?.salesContract?.customer?.name?.toLowerCase().includes(filters.search.toLowerCase())

    const matchesStatus = filters.status === "all" ||
      (filters.status === "archived" ? inspection.archived === true : inspection.status === filters.status)
    const matchesWorkOrder = !filters.work_order_id || inspection.work_order_id === filters.work_order_id

    return matchesSearch && matchesStatus && matchesWorkOrder
  })

  // ✅ PROFESSIONAL ERP: CONTRACT-LEVEL STATS FOR CONSISTENCY WITH WORK ORDERS
  const qualityStats = useMemo(() => {
    // ✅ MANUFACTURING ERP: Calculate both inspection-level and contract-level metrics

    // Individual inspection counts (primary metrics)
    const totalInspections = inspections.length
    const pendingInspections = inspections.filter(i => i.status === 'pending').length
    const inProgressInspections = inspections.filter(i => i.status === 'in-progress').length
    const passedInspections = inspections.filter(i => i.status === 'passed').length
    const failedInspections = inspections.filter(i => i.status === 'failed').length
    const archivedInspections = inspections.filter(i => i.archived === true).length

    // Group inspections by contract for secondary metrics
    const contractGroups = inspections.reduce((acc, inspection) => {
      const contractId = inspection.workOrder?.sales_contract_id
      if (!contractId) return acc

      if (!acc[contractId]) {
        acc[contractId] = {
          total: 0,
          pending: 0,
          inProgress: 0,
          passed: 0,
          failed: 0,
        }
      }

      acc[contractId].total++
      if (inspection.status === 'pending') acc[contractId].pending++
      if (inspection.status === 'in-progress') acc[contractId].inProgress++
      if (inspection.status === 'passed') acc[contractId].passed++
      if (inspection.status === 'failed') acc[contractId].failed++

      return acc
    }, {} as Record<string, { total: number; pending: number; inProgress: number; passed: number; failed: number }>)

    // Contract-level metrics (secondary)
    const totalContracts = Object.keys(contractGroups).length
    const contractsWithPending = Object.values(contractGroups).filter(c => c.pending > 0).length
    const contractsInProgress = Object.values(contractGroups).filter(c => c.inProgress > 0).length
    const contractsFullyPassed = Object.values(contractGroups).filter(c => c.total > 0 && c.passed === c.total).length
    const contractsWithFailed = Object.values(contractGroups).filter(c => c.failed > 0).length

    return {
      // Primary metrics: Individual inspections
      totalInspections,
      pendingInspections,
      inProgressInspections,
      passedInspections,
      failedInspections,
      archivedInspections,
      // Secondary metrics: Contract-level aggregations
      totalContracts,
      contractsWithPending,
      contractsInProgress,
      contractsFullyPassed,
      contractsWithFailed,
    }
  }, [inspections])

  return (
    <div className="space-y-6">
      {/* ✅ PROFESSIONAL HEADER */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("quality.title")}</h1>
          <p className="text-muted-foreground">
            {t("quality.subtitle")}
          </p>
          <div className="mt-2 text-sm text-muted-foreground">
            <p>Track quality inspections from work orders through completion. Click status badges to update inspection progress.</p>
          </div>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          New Inspection
        </Button>
      </div>

      {/* ✅ QUALITY CONTROL DASHBOARD - CLICKABLE FILTER CARDS */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card
          className={`cursor-pointer transition-colors hover:bg-muted/50 ${filters.status === "all" ? "ring-2 ring-primary" : ""}`}
          onClick={() => setFilters(prev => ({ ...prev, status: "all" }))}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Inspections</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{qualityStats.totalInspections}</div>
            <p className="text-xs text-muted-foreground">across {qualityStats.totalContracts} contracts</p>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer transition-colors hover:bg-yellow-50 ${filters.status === "pending" ? "ring-2 ring-yellow-500" : ""}`}
          onClick={() => setFilters(prev => ({ ...prev, status: "pending" }))}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{qualityStats.pendingInspections}</div>
            <p className="text-xs text-muted-foreground">awaiting inspection start</p>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer transition-colors hover:bg-blue-50 ${filters.status === "in-progress" ? "ring-2 ring-blue-500" : ""}`}
          onClick={() => setFilters(prev => ({ ...prev, status: "in-progress" }))}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <PlayCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{qualityStats.inProgressInspections}</div>
            <p className="text-xs text-muted-foreground">currently being inspected</p>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer transition-colors hover:bg-green-50 ${filters.status === "passed" ? "ring-2 ring-green-500" : ""}`}
          onClick={() => setFilters(prev => ({ ...prev, status: "passed" }))}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Passed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{qualityStats.passedInspections}</div>
            <p className="text-xs text-muted-foreground">passed quality standards</p>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer transition-colors hover:bg-red-50 ${filters.status === "failed" ? "ring-2 ring-red-500" : ""}`}
          onClick={() => setFilters(prev => ({ ...prev, status: "failed" }))}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{qualityStats.failedInspections}</div>
            <p className="text-xs text-muted-foreground">require corrective action</p>
          </CardContent>
        </Card>

        <Card
          className={`cursor-pointer transition-colors hover:bg-orange-50 ${filters.status === "archived" ? "ring-2 ring-orange-500" : ""}`}
          onClick={() => setFilters(prev => ({ ...prev, status: "archived" }))}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Archived</CardTitle>
            <FolderArchive className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{qualityStats.archivedInspections}</div>
            <p className="text-xs text-muted-foreground">archived inspections</p>
          </CardContent>
        </Card>
      </div>

      {/* ✅ QUALITY WORKFLOW LEGEND */}
      <Card className="bg-muted/50">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="text-sm font-medium text-muted-foreground">Quality Workflow:</div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3 text-yellow-600" />
                  <span className="text-xs">Pending Review</span>
                </div>
                <span className="text-muted-foreground">→</span>
                <div className="flex items-center gap-1">
                  <PlayCircle className="h-3 w-3 text-blue-600" />
                  <span className="text-xs">In Progress</span>
                </div>
                <span className="text-muted-foreground">→</span>
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  <span className="text-xs">Passed</span>
                </div>
                <span className="text-muted-foreground">or</span>
                <div className="flex items-center gap-1">
                  <XCircle className="h-3 w-3 text-red-600" />
                  <span className="text-xs">Failed</span>
                </div>
              </div>
            </div>
            <div className="text-xs text-muted-foreground">
              Click dashboard cards to filter • Click status badges to update progress
            </div>
          </div>
        </CardContent>
      </Card>

      {/* ✅ CREATE FORM (Following established patterns) */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>Create Quality Inspection</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="work_order">Work Order *</Label>
                <Select
                  value={createForm.work_order_id}
                  onValueChange={(value) => setCreateForm(prev => ({ ...prev, work_order_id: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select work order" />
                  </SelectTrigger>
                  <SelectContent>
                    {workOrders.map((wo) => (
                      <SelectItem key={wo.id} value={wo.id}>
                        {wo.number} - {wo.product?.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="inspector">Inspector *</Label>
                <Input
                  id="inspector"
                  value={createForm.inspector}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, inspector: e.target.value }))}
                  placeholder="Inspector name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="inspection_type">Inspection Type</Label>
                <Select
                  value={createForm.inspection_type}
                  onValueChange={(value) => setCreateForm(prev => ({ ...prev, inspection_type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="incoming">Incoming Material</SelectItem>
                    <SelectItem value="in_process">In-Process</SelectItem>
                    <SelectItem value="final">Final Inspection</SelectItem>
                    <SelectItem value="pre_shipment">Pre-Shipment</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="scheduled_date">Scheduled Date</Label>
                <Input
                  id="scheduled_date"
                  type="date"
                  value={createForm.scheduled_date}
                  onChange={(e) => setCreateForm(prev => ({ ...prev, scheduled_date: e.target.value }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Input
                id="notes"
                value={createForm.notes}
                onChange={(e) => setCreateForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Inspection notes or requirements"
              />
            </div>

            <div className="flex items-center gap-2">
              <Button onClick={handleCreate} disabled={loading}>
                {loading ? <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> : <Plus className="mr-2 h-4 w-4" />}
                Create Inspection
              </Button>
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ FILTERS (Following established patterns) */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search inspections..."
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            className="pl-10"
          />
        </div>

        <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="passed">Passed</SelectItem>
            <SelectItem value="failed">Failed</SelectItem>
            <SelectItem value="archived">Archived</SelectItem>
          </SelectContent>
        </Select>



        <Button variant="outline" onClick={loadInspections} disabled={loading}>
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* ✅ PROFESSIONAL CONTRACT-GROUPED INSPECTIONS TABLE */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">Quality Inspections</h2>
            <p className="text-sm text-muted-foreground">
              {loading ? "Loading..." : `Found ${filteredInspections.length} inspections`}
            </p>
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Contract / Inspection</TableHead>
                <TableHead>Count / Progress</TableHead>
                <TableHead>Customer / Work Order</TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Inspector</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">Loading quality inspections...</p>
                  </TableCell>
                </TableRow>
              ) : filteredInspections.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <Settings className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No quality inspections found</p>
                  </TableCell>
                </TableRow>
              ) : (
                renderGroupedInspections(
                  filteredInspections,
                  expandedContracts,
                  toggleContract,
                  handleArchive,
                  handleUnarchive,
                  handleStatusChange
                )
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* ✅ COMPLIANCE: Archive Dialog */}
      <ArchiveInspectionDialog
        inspectionId={inspectionToArchive || ""}
        isOpen={archiveDialogOpen}
        onClose={() => setArchiveDialogOpen(false)}
        onArchived={handleArchiveComplete}
      />

      {/* ✅ COMPLIANCE: Unarchive Dialog */}
      {inspectionToUnarchive && (() => {
        const inspection = inspections.find(i => i.id === inspectionToUnarchive)
        return (
          <UnarchiveInspectionDialog
            isOpen={unarchiveDialogOpen}
            onClose={() => setUnarchiveDialogOpen(false)}
            onUnarchived={handleUnarchiveComplete}
            inspectionId={inspectionToUnarchive}
            archiveInfo={{
              reason: inspection?.archive_reason?.replace('_', ' ') || 'Not specified',
              archivedAt: inspection?.archived_at ? new Date(inspection.archived_at).toLocaleDateString() : 'Unknown',
              archivedBy: inspection?.archived_by || 'Unknown'
            }}
          />
        )
      })()}
    </div>
  )
}
