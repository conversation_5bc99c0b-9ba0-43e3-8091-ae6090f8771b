"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { AppShell } from "@/components/app-shell"
import { SampleDetail } from "@/components/samples/sample-detail"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { AlertTriangle, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function SampleDetailPage() {
  const params = useParams()
  const { error: toastError } = useSafeToast()
  const { t } = useI18n()

  const [sample, setSample] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const sampleId = params.id as string

  // ✅ FETCH SAMPLE DETAIL WITH ENHANCED API
  const fetchSample = async () => {
    try {
      setLoading(true)
      setError(null)

      // Use enhanced detail API with all options
      const queryParams = new URLSearchParams({
        format: 'detailed',
        include_customer_details: 'true',
        include_product_details: 'true',
        include_supplier_details: 'true',
        include_approval_history: 'true',
        include_workflow_actions: 'true',
        include_metadata: 'true',
      })

      const response = await fetch(`/api/samples/${sampleId}?${queryParams.toString()}`)

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Sample not found')
        }
        if (response.status === 403) {
          throw new Error('Access denied')
        }
        throw new Error('Failed to load sample details')
      }

      const data = await response.json()

      // ✅ DEBUG: Log the API response
      console.log('Sample detail API response:', data)

      // Handle enhanced API response format
      if (data.data) {
        console.log('Using data.data, workflow:', data.data.workflow)
        setSample(data.data)
      } else {
        console.log('Using data directly, workflow:', data.workflow)
        setSample(data)
      }

    } catch (err) {
      console.error('Error fetching sample:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load sample details'
      setError(errorMessage)
      toastError("Error Loading Sample", errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // ✅ INITIAL DATA LOADING
  useEffect(() => {
    if (sampleId) {
      fetchSample()
    }
  }, [sampleId])

  // ✅ HANDLE SAMPLE UPDATE
  const handleSampleUpdate = () => {
    fetchSample()
    toast({
      title: "Sample Updated",
      description: "Sample details have been refreshed.",
    })
  }

  // Show loading state
  if (loading) {
    return (
      <AppShell>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading sample details...</p>
          </div>
        </div>
      </AppShell>
    )
  }

  // Show error state
  if (error || !sample) {
    return (
      <AppShell>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {error === 'Sample not found' ? 'Sample Not Found' : 'Error Loading Sample'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {error || 'Failed to load sample details. Please try again.'}
            </p>
            <div className="flex gap-2 justify-center">
              <Button onClick={fetchSample}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button variant="outline" onClick={() => window.history.back()}>
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </AppShell>
    )
  }

  return (
    <AppShell>
      <SampleDetail
        sample={sample}
        onUpdate={handleSampleUpdate}
        loading={loading}
      />
    </AppShell>
  )
}
