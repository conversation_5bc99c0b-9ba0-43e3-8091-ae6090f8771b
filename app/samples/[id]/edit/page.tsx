"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { AppShell } from "@/components/app-shell"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Save, Package, Loader2 } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { CustomerSelect } from "@/components/forms/customer-select"
import { ProductSelect } from "@/components/forms/product-select"
import { SupplierSelect } from "@/components/forms/supplier-select"
import Link from "next/link"

interface SampleData {
  id: string
  code: string
  name: string
  date: string
  sample_type: string
  priority: string
  status: string
  sample_direction: string
  sample_purpose?: string
  customer_id?: string
  product_id?: string
  supplier_id?: string
  notes?: string
  quantity?: string
  unit?: string
  specifications?: string
  quality_requirements?: string
  delivery_date?: string
  cost?: string
  currency?: string
}

export default function EditSamplePage() {
  const router = useRouter()
  const params = useParams()
  const sampleId = params.id as string
  const { success: toastSuccess, error: toastError } = useSafeToast()
  const { t } = useI18n()

  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [customers, setCustomers] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [suppliers, setSuppliers] = useState<any[]>([])
  const [formData, setFormData] = useState<SampleData>({
    id: "",
    code: "",
    name: "",
    date: "",
    sample_type: "development",
    priority: "normal",
    status: "active",
    sample_direction: "outbound",
    sample_purpose: "",
    customer_id: "",
    product_id: "",
    supplier_id: "",
    notes: "",
    quantity: "",
    unit: "",
    specifications: "",
    quality_requirements: "",
    delivery_date: "",
    cost: "",
    currency: "USD",
  })

  // ✅ FETCH SAMPLE DATA
  useEffect(() => {
    const fetchSampleData = async () => {
      try {
        setInitialLoading(true)

        // ✅ USE ENHANCED API LIKE VIEW PAGE
        const queryParams = new URLSearchParams({
          format: 'detailed',
          include_customer_details: 'true',
          include_product_details: 'true',
          include_supplier_details: 'true',
        })

        const response = await fetch(`/api/samples/${sampleId}?${queryParams.toString()}`)

        if (!response.ok) {
          throw new Error('Failed to fetch sample data')
        }

        const response_data = await response.json()

        // ✅ HANDLE WRAPPED RESPONSE FORMAT
        const sample = response_data.data || response_data

        // ✅ DEBUG: Log the fetched sample data
        console.log('Fetched sample data for edit:', sample)

        // ✅ POPULATE FORM WITH EXISTING DATA
        setFormData({
          id: sample.id,
          code: sample.code || "",
          name: sample.name || "",
          date: sample.date || "",
          sample_type: sample.sample_type || "development",
          priority: sample.priority || "normal",
          status: sample.status || "active",
          sample_direction: sample.sample_direction || "outbound",
          sample_purpose: sample.sample_purpose || "",
          customer_id: sample.customer_id || "",
          product_id: sample.product_id || "",
          supplier_id: sample.supplier_id || "",
          notes: sample.notes || "",
          quantity: sample.quantity || "",
          unit: sample.unit || "",
          specifications: sample.specifications || "",
          quality_requirements: sample.quality_requirements || "",
          delivery_date: sample.delivery_date || "",
          cost: sample.cost || "",
          currency: sample.currency || "USD",
        })

        // ✅ DEBUG: Log the populated form data
        console.log('Form data populated:', {
          id: sample.id,
          name: sample.name,
          customer_id: sample.customer_id,
          product_id: sample.product_id,
          supplier_id: sample.supplier_id,
        })
      } catch (error) {
        console.error('Error fetching sample:', error)
        toastError(t("samples.edit.error.title"), t("samples.edit.error.load"))
        router.push('/samples')
      } finally {
        setInitialLoading(false)
      }
    }

    if (sampleId) {
      fetchSampleData()
    }
  }, [sampleId, toastError, router])

  // ✅ FETCH RELATIONSHIP DATA
  useEffect(() => {
    const fetchRelationshipData = async () => {
      try {
        const [customersRes, productsRes, suppliersRes] = await Promise.all([
          fetch('/api/customers'),
          fetch('/api/products'),
          fetch('/api/suppliers')
        ])

        if (customersRes.ok) {
          const customersData = await customersRes.json()
          setCustomers(Array.isArray(customersData) ? customersData : [])
        }

        if (productsRes.ok) {
          const productsData = await productsRes.json()
          setProducts(Array.isArray(productsData) ? productsData : [])
        }

        if (suppliersRes.ok) {
          const suppliersData = await suppliersRes.json()
          setSuppliers(Array.isArray(suppliersData) ? suppliersData : [])
        }
      } catch (error) {
        console.error('Error fetching relationship data:', error)
      }
    }

    fetchRelationshipData()
  }, [])

  // ✅ HANDLE FORM SUBMISSION
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim()) {
      toastError(t("samples.edit.error.title"), t("samples.edit.validation.name"))
      return
    }

    setLoading(true)

    try {
      // ✅ PREPARE UPDATE DATA (Filter out empty values)
      const updateData: any = {
        name: formData.name,
        sample_type: formData.sample_type,
        priority: formData.priority,
        status: formData.status,
      }

      // ✅ ONLY ADD NON-EMPTY OPTIONAL FIELDS
      if (formData.customer_id && formData.customer_id.trim()) {
        updateData.customer_id = formData.customer_id
      }
      if (formData.product_id && formData.product_id.trim()) {
        updateData.product_id = formData.product_id
      }
      if (formData.supplier_id && formData.supplier_id.trim()) {
        updateData.supplier_id = formData.supplier_id
      }
      if (formData.notes && formData.notes.trim()) {
        updateData.notes = formData.notes
      }
      if (formData.quantity && formData.quantity.trim()) {
        updateData.quantity = formData.quantity
      }
      if (formData.unit && formData.unit.trim()) {
        updateData.unit = formData.unit
      }
      if (formData.specifications && formData.specifications.trim()) {
        updateData.specifications = formData.specifications
      }
      if (formData.quality_requirements && formData.quality_requirements.trim()) {
        updateData.quality_requirements = formData.quality_requirements
      }
      if (formData.delivery_date && formData.delivery_date.trim()) {
        updateData.delivery_date = formData.delivery_date
      }
      if (formData.cost && formData.cost.trim()) {
        updateData.cost = formData.cost
      }
      if (formData.currency && formData.currency.trim()) {
        updateData.currency = formData.currency
      }

      // ✅ DEBUG: Log the data being sent
      console.log('Sending update data:', updateData)

      const response = await fetch(`/api/samples/${sampleId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        // ✅ DEBUG: Log the full error response
        console.error('API Error Response:', {
          status: response.status,
          statusText: response.statusText,
          errorData: errorData
        })

        throw new Error(errorData.message || 'Failed to update sample')
      }

      toastSuccess(t("samples.edit.success.title"), t("samples.edit.success.description"))
      router.push(`/samples/${sampleId}`)
    } catch (error) {
      console.error('Error updating sample:', error)
      toastError(t("samples.edit.error.title"), error instanceof Error ? error.message : t("samples.edit.error.description"))
    } finally {
      setLoading(false)
    }
  }

  // ✅ HANDLE INPUT CHANGES
  const handleInputChange = (field: keyof SampleData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // ✅ LOADING STATE
  if (initialLoading) {
    return (
      <AppShell>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>{t("samples.edit.loading")}</span>
          </div>
        </div>
      </AppShell>
    )
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ HEADER */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/samples/${sampleId}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("samples.edit.back")}
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t("samples.edit.title")}</h1>
            <p className="text-muted-foreground">
              {t("samples.edit.description")}
            </p>
          </div>
        </div>

        {/* ✅ EDIT FORM */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                {t("samples.edit.basic_info")}
              </CardTitle>
              <CardDescription>
                {t("samples.edit.basic_info_desc")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="code">{t("samples.edit.sample_code")}</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    disabled
                    className="bg-muted"
                  />
                  <p className="text-xs text-muted-foreground">
                    {t("samples.edit.code.readonly")}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name">{t("samples.edit.sample_name")} *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder={t("samples.edit.sample_name_placeholder")}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sample_type">{t("samples.edit.sample_type")}</Label>
                  <Select
                    value={formData.sample_type}
                    onValueChange={(value) => handleInputChange('sample_type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="development">{t("samples.filters.type.development")}</SelectItem>
                      <SelectItem value="production">{t("samples.filters.type.production")}</SelectItem>
                      <SelectItem value="quality">{t("samples.filters.type.quality")}</SelectItem>
                      <SelectItem value="prototype">{t("samples.filters.type.prototype")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">{t("samples.edit.priority")}</Label>
                  <Select
                    value={formData.priority}
                    onValueChange={(value) => handleInputChange('priority', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* ✅ RELATIONSHIPS */}
          <Card>
            <CardHeader>
              <CardTitle>{t("samples.edit.relationships")}</CardTitle>
              <CardDescription>
                {t("samples.edit.relationships_desc")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customer_id">{t("samples.edit.customer")}</Label>
                  <CustomerSelect
                    customers={customers}
                    value={formData.customer_id}
                    onValueChange={(value) => handleInputChange('customer_id', value)}
                    placeholder={t("samples.edit.customer_placeholder")}
                    onCustomerAdded={(newCustomer) => {
                      setCustomers(prev => [...prev, newCustomer])
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="product_id">{t("samples.edit.product")}</Label>
                  <ProductSelect
                    products={products}
                    value={formData.product_id}
                    onValueChange={(value) => handleInputChange('product_id', value)}
                    placeholder={t("samples.edit.product_placeholder")}
                    showAddNew={false}
                    onProductAdded={(newProduct) => {
                      setProducts(prev => [...prev, newProduct])
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="supplier_id">{t("samples.edit.supplier")}</Label>
                  <SupplierSelect
                    suppliers={suppliers}
                    value={formData.supplier_id}
                    onValueChange={(value) => handleInputChange('supplier_id', value)}
                    placeholder={t("samples.edit.supplier_placeholder")}
                    onSupplierAdded={(newSupplier) => {
                      setSuppliers(prev => [...prev, newSupplier])
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* ✅ SPECIFICATIONS */}
          <Card>
            <CardHeader>
              <CardTitle>{t("samples.edit.specifications")}</CardTitle>
              <CardDescription>
                {t("samples.edit.specifications_desc")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quantity">{t("samples.edit.quantity")}</Label>
                  <Input
                    id="quantity"
                    value={formData.quantity}
                    onChange={(e) => handleInputChange('quantity', e.target.value)}
                    placeholder="e.g., 100"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="unit">{t("samples.edit.unit")}</Label>
                  <Input
                    id="unit"
                    value={formData.unit}
                    onChange={(e) => handleInputChange('unit', e.target.value)}
                    placeholder="e.g., pcs, kg, m"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cost">{t("samples.edit.cost")}</Label>
                  <Input
                    id="cost"
                    value={formData.cost}
                    onChange={(e) => handleInputChange('cost', e.target.value)}
                    placeholder="e.g., 10.50"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currency">{t("samples.edit.currency")}</Label>
                  <Select
                    value={formData.currency}
                    onValueChange={(value) => handleInputChange('currency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="CNY">CNY</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="delivery_date">{t("samples.edit.delivery_date")}</Label>
                  <Input
                    id="delivery_date"
                    type="date"
                    value={formData.delivery_date}
                    onChange={(e) => handleInputChange('delivery_date', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="specifications">{t("samples.edit.technical_specs")}</Label>
                  <Textarea
                    id="specifications"
                    value={formData.specifications}
                    onChange={(e) => handleInputChange('specifications', e.target.value)}
                    placeholder={t("samples.edit.technical_specs_placeholder")}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="quality_requirements">{t("samples.edit.quality_requirements")}</Label>
                  <Textarea
                    id="quality_requirements"
                    value={formData.quality_requirements}
                    onChange={(e) => handleInputChange('quality_requirements', e.target.value)}
                    placeholder={t("samples.edit.quality_requirements_placeholder")}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">{t("samples.edit.notes")}</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder={t("samples.edit.notes_placeholder")}
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* ✅ FORM ACTIONS */}
          <div className="flex items-center justify-end gap-4">
            <Button type="button" variant="outline" asChild>
              <Link href={`/samples/${sampleId}`}>{t("samples.edit.cancel")}</Link>
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t("samples.edit.saving")}
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {t("samples.edit.save")}
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </AppShell>
  )
}
