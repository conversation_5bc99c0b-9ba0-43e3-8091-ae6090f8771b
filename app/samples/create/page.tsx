"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { AppShell } from "@/components/app-shell"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Save, Package } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { CustomerSelect } from "@/components/forms/customer-select"
import { ProductSelect } from "@/components/forms/product-select"
import { SupplierSelect } from "@/components/forms/supplier-select"
import Link from "next/link"

export default function CreateSamplePage() {
  const router = useRouter()
  const { success: toastSuccess, error: toastError, info: toastInfo } = useSafeToast()
  const { t } = useI18n()

  const [loading, setLoading] = useState(false)
  const [customers, setCustomers] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [suppliers, setSuppliers] = useState<any[]>([])
  const [formData, setFormData] = useState({
    // ✅ BASIC INFORMATION
    code: "",
    name: "",
    date: new Date().toISOString().split('T')[0],
    sample_type: "development",
    priority: "normal",
    status: "active",

    // ✅ BIDIRECTIONAL WORKFLOW FIELDS
    sample_direction: "outbound", // "outbound" | "inbound" | "internal"
    sample_purpose: "customer_evaluation", // Dynamic based on direction
    sender_type: "", // "customer" | "supplier" | "internal"
    receiver_type: "", // "customer" | "supplier" | "internal"

    // ✅ RELATIONSHIP FIELDS (Context-Aware)
    customer_id: "",
    product_id: "",
    supplier_id: "",

    // ✅ INBOUND SAMPLE FIELDS
    received_date: "",
    testing_status: "not_started",
    testing_results: "",
    quote_requested: false,
    quote_provided: false,

    // ✅ SPECIFICATION FIELDS
    quantity: "",
    unit: "",
    specifications: "",
    quality_requirements: "",
    delivery_date: "",
    cost: "",
    currency: "USD",
    notes: "",
  })

  // ✅ FETCH RELATIONSHIP DATA
  useEffect(() => {
    const fetchRelationshipData = async () => {
      try {
        const [customersRes, productsRes, suppliersRes] = await Promise.all([
          fetch('/api/customers'),
          fetch('/api/products'),
          fetch('/api/suppliers'),
        ])

        if (customersRes.ok) {
          const customersData = await customersRes.json()
          setCustomers(Array.isArray(customersData) ? customersData : [])
        }

        if (productsRes.ok) {
          const productsData = await productsRes.json()
          setProducts(Array.isArray(productsData) ? productsData : [])
        }

        if (suppliersRes.ok) {
          const suppliersData = await suppliersRes.json()
          setSuppliers(Array.isArray(suppliersData) ? suppliersData : [])
        }
      } catch (error) {
        console.error('Error fetching relationship data:', error)
        toastError(
          "Warning",
          "Could not load customers, products, or suppliers for selection."
        )
      }
    }

    fetchRelationshipData()
  }, [toastError])

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // ✅ DYNAMIC FORM LOGIC - Update related fields when direction changes
  const handleDirectionChange = (direction: string) => {
    setFormData(prev => ({
      ...prev,
      sample_direction: direction,
      // Reset context-dependent fields
      sample_purpose: direction === "outbound" ? "customer_evaluation" :
        direction === "inbound" ? "manufacturing_quote" : "quality_control",
      sender_type: direction === "outbound" ? "internal" : "",
      receiver_type: direction === "outbound" ? "customer" : "internal",
      // Clear relationship fields to avoid confusion
      customer_id: "",
      supplier_id: "",
      // Clear product_id when switching to inbound (since it becomes optional)
      product_id: direction === "inbound" ? "" : prev.product_id,
    }))

    // ✅ SHOW BUSINESS RULE NOTIFICATION
    if (direction === "outbound") {
      toastInfo(
        "Product Required",
        "📤 Outbound samples require product selection (sampling our own product)"
      )
    } else if (direction === "inbound") {
      toastInfo(
        "Product Optional",
        "📥 Product selection is optional (sample may not exist in our catalog yet)"
      )
    }
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    console.log("🚀 Form submission started")
    console.log("📝 Form data:", formData)

    // ✅ CLIENT-SIDE VALIDATION: Outbound samples require product
    if (formData.sample_direction === "outbound" && (!formData.product_id || formData.product_id === "")) {
      toastError(
        "Product Required",
        "Outbound samples require product selection (we are sampling our own product)"
      )
      setLoading(false)
      return
    }

    try {
      console.log("📡 Making POST request to /api/samples")
      const response = await fetch('/api/samples', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      console.log("📡 Response received:", response.status, response.statusText)

      if (!response.ok) {
        throw new Error('Failed to create sample')
      }

      const result = await response.json()
      console.log("✅ Sample created successfully:", result)

      toastSuccess(
        "Sample Created",
        `Sample ${formData.code || formData.name} has been created successfully.`
      )

      // ✅ REDIRECT TO SAMPLES LIST PAGE (ALWAYS WORKS)
      console.log("🔄 Redirecting to samples list...")
      router.push('/samples')

    } catch (error) {
      console.error('Error creating sample:', error)
      toastError(
        "Creation Failed",
        "Failed to create sample. Please try again."
      )
    } finally {
      setLoading(false)
    }
  }

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ HEADER */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/samples">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Samples
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Create New Sample</h1>
            <p className="text-muted-foreground">Add a new sample to track customer requests and approvals</p>
          </div>
        </div>

        {/* ✅ PROFESSIONAL ERP FORM LAYOUT */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* ✅ CARD 1: SAMPLE CLASSIFICATION & BASIC INFO */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Sample Classification
                </CardTitle>
                <CardDescription>
                  Sample direction and basic information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* ✅ SAMPLE DIRECTION - CRITICAL BUSINESS LOGIC */}
                <div>
                  <Label htmlFor="sample_direction">Sample Direction *</Label>
                  <Select
                    value={formData.sample_direction}
                    onValueChange={handleDirectionChange}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="outbound">📤 Outbound - We send to customer</SelectItem>
                      <SelectItem value="inbound">📥 Inbound - Customer/supplier sends to us</SelectItem>
                      <SelectItem value="internal">🏭 Internal - Internal testing/R&D</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="code">Sample Code *</Label>
                    <Input
                      id="code"
                      value={formData.code}
                      onChange={(e) => handleInputChange('code', e.target.value)}
                      placeholder={formData.sample_direction === "inbound" ? "e.g. IN-2024-001" : "e.g. OUT-2024-001"}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="date">
                      {formData.sample_direction === "inbound" ? "Received Date *" : "Sample Date *"}
                    </Label>
                    <Input
                      id="date"
                      type="date"
                      value={formData.date}
                      onChange={(e) => handleInputChange('date', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="name">Sample Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder={formData.sample_direction === "inbound" ? "e.g. Customer Fabric Sample" : "e.g. Cotton Fabric Sample"}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="sample_type">Sample Type</Label>
                    <Select
                      value={formData.sample_type}
                      onValueChange={(value) => handleInputChange('sample_type', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="development">Development</SelectItem>
                        <SelectItem value="production">Production</SelectItem>
                        <SelectItem value="quality">Quality</SelectItem>
                        <SelectItem value="prototype">Prototype</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select
                      value={formData.priority}
                      onValueChange={(value) => handleInputChange('priority', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* ✅ CARD 2: PRODUCT INFORMATION */}
            <Card>
              <CardHeader>
                <CardTitle>Product Information</CardTitle>
                <CardDescription>
                  Product details and specifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="product_id">
                    Product {formData.sample_direction === "outbound" ? "*" : ""}
                  </Label>
                  <ProductSelect
                    products={products}
                    value={formData.product_id}
                    onValueChange={(value) => handleInputChange('product_id', value)}
                    placeholder={
                      formData.sample_direction === "outbound"
                        ? "Search products (required for outbound)..."
                        : "Search products (optional)..."
                    }
                    showAddNew={false}
                    onProductAdded={(newProduct) => {
                      setProducts(prev => [...prev, newProduct])
                    }}
                  />
                  {formData.sample_direction === "outbound" && (
                    <p className="text-sm text-muted-foreground mt-1">
                      📤 Outbound samples require product selection (sampling our own product)
                    </p>
                  )}
                  {formData.sample_direction === "inbound" && (
                    <p className="text-sm text-muted-foreground mt-1">
                      📥 Product selection is optional (sample may not exist in our catalog yet)
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="quantity">Quantity</Label>
                    <Input
                      id="quantity"
                      value={formData.quantity}
                      onChange={(e) => handleInputChange('quantity', e.target.value)}
                      placeholder="e.g. 100"
                    />
                  </div>
                  <div>
                    <Label htmlFor="unit">Unit</Label>
                    <Input
                      id="unit"
                      value={formData.unit}
                      onChange={(e) => handleInputChange('unit', e.target.value)}
                      placeholder="e.g. meters, pieces"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="specifications">Technical Specifications</Label>
                  <Textarea
                    id="specifications"
                    value={formData.specifications}
                    onChange={(e) => handleInputChange('specifications', e.target.value)}
                    placeholder="Technical specifications..."
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="quality_requirements">Quality Requirements</Label>
                  <Textarea
                    id="quality_requirements"
                    value={formData.quality_requirements}
                    onChange={(e) => handleInputChange('quality_requirements', e.target.value)}
                    placeholder="Quality standards..."
                    rows={2}
                  />
                </div>
              </CardContent>
            </Card>

          </div>

          {/* ✅ SECOND ROW: BUSINESS RELATIONS & COMMERCIAL DETAILS */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* ✅ CARD 3: DYNAMIC BUSINESS RELATIONS */}
            <Card>
              <CardHeader>
                <CardTitle>
                  {formData.sample_direction === "outbound" ? "Customer Delivery" :
                    formData.sample_direction === "inbound" ? "Sample Source" : "Internal Relations"}
                </CardTitle>
                <CardDescription>
                  {formData.sample_direction === "outbound" ? "Customer receiving the sample" :
                    formData.sample_direction === "inbound" ? "Who sent us this sample" : "Internal department/project"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* ✅ OUTBOUND: Customer receiving sample */}
                {formData.sample_direction === "outbound" && (
                  <>
                    <div>
                      <Label htmlFor="customer_id">Customer (Receiving Sample) *</Label>
                      <CustomerSelect
                        customers={customers}
                        value={formData.customer_id}
                        onValueChange={(value) => handleInputChange('customer_id', value)}
                        placeholder="Search customers to send sample to..."
                        onCustomerAdded={(newCustomer) => {
                          setCustomers(prev => [...prev, newCustomer])
                        }}
                      />
                    </div>
                    <div>
                      <Label htmlFor="sample_purpose">Sample Purpose</Label>
                      <Select
                        value={formData.sample_purpose}
                        onValueChange={(value) => handleInputChange('sample_purpose', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Why are we sending this sample?" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="customer_evaluation">Customer Evaluation</SelectItem>
                          <SelectItem value="marketing_demo">Marketing Demo</SelectItem>
                          <SelectItem value="trade_show">Trade Show</SelectItem>
                          <SelectItem value="sales_presentation">Sales Presentation</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="notes">Delivery Instructions</Label>
                      <Textarea
                        id="notes"
                        value={formData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        placeholder="Special delivery instructions, customer requirements..."
                        rows={3}
                      />
                    </div>
                  </>
                )}

                {/* ✅ INBOUND: Customer/supplier sending sample to us */}
                {formData.sample_direction === "inbound" && (
                  <>
                    <div>
                      <Label htmlFor="sender_type">Sample Sender Type *</Label>
                      <Select
                        value={formData.sender_type}
                        onValueChange={(value) => {
                          handleInputChange('sender_type', value)
                          // Clear the other relationship when sender type changes
                          if (value === "customer") {
                            handleInputChange('supplier_id', "")
                          } else if (value === "supplier") {
                            handleInputChange('customer_id', "")
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Who sent us this sample?" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="customer">Customer</SelectItem>
                          <SelectItem value="supplier">Supplier</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Show customer dropdown if sender is customer */}
                    {formData.sender_type === "customer" && (
                      <div>
                        <Label htmlFor="customer_id">Customer (Sender) *</Label>
                        <CustomerSelect
                          customers={customers}
                          value={formData.customer_id}
                          onValueChange={(value) => handleInputChange('customer_id', value)}
                          placeholder="Search customer who sent sample..."
                          onCustomerAdded={(newCustomer) => {
                            setCustomers(prev => [...prev, newCustomer])
                          }}
                        />
                      </div>
                    )}

                    {/* Show supplier dropdown if sender is supplier */}
                    {formData.sender_type === "supplier" && (
                      <div>
                        <Label htmlFor="supplier_id">Supplier (Sender) *</Label>
                        <SupplierSelect
                          suppliers={suppliers}
                          value={formData.supplier_id}
                          onValueChange={(value) => handleInputChange('supplier_id', value)}
                          placeholder="Search supplier who sent sample..."
                          onSupplierAdded={(newSupplier) => {
                            setSuppliers(prev => [...prev, newSupplier])
                          }}
                        />
                      </div>
                    )}

                    <div>
                      <Label htmlFor="sample_purpose">Sample Purpose</Label>
                      <Select
                        value={formData.sample_purpose}
                        onValueChange={(value) => handleInputChange('sample_purpose', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Why did they send this sample?" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="manufacturing_quote">Manufacturing Quote Request</SelectItem>
                          <SelectItem value="material_testing">Material Testing</SelectItem>
                          <SelectItem value="reverse_engineering">Reverse Engineering</SelectItem>
                          <SelectItem value="quality_comparison">Quality Comparison</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="notes">Sample Condition & Requirements</Label>
                      <Textarea
                        id="notes"
                        value={formData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        placeholder="Sample condition when received, analysis requirements, customer specifications..."
                        rows={3}
                      />
                    </div>
                  </>
                )}

                {/* ✅ INTERNAL: Internal testing/R&D */}
                {formData.sample_direction === "internal" && (
                  <>
                    <div>
                      <Label htmlFor="sample_purpose">Internal Purpose</Label>
                      <Select
                        value={formData.sample_purpose}
                        onValueChange={(value) => handleInputChange('sample_purpose', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Internal testing purpose" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="quality_control">Quality Control</SelectItem>
                          <SelectItem value="r_and_d">R&D Testing</SelectItem>
                          <SelectItem value="process_improvement">Process Improvement</SelectItem>
                          <SelectItem value="product_development">Product Development</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="notes">Internal Requirements</Label>
                      <Textarea
                        id="notes"
                        value={formData.notes}
                        onChange={(e) => handleInputChange('notes', e.target.value)}
                        placeholder="Internal testing requirements, project details, department specifications..."
                        rows={3}
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* ✅ CARD 4: DYNAMIC COMMERCIAL/TESTING DETAILS */}
            <Card>
              <CardHeader>
                <CardTitle>Commercial Details</CardTitle>
                <CardDescription>
                  Pricing and delivery information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* ✅ OUTBOUND SAMPLE FIELDS */}
                {formData.sample_direction === "outbound" && (
                  <>
                    <div>
                      <Label htmlFor="delivery_date">Delivery Date</Label>
                      <Input
                        id="delivery_date"
                        type="date"
                        value={formData.delivery_date}
                        onChange={(e) => handleInputChange('delivery_date', e.target.value)}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="cost">Sample Cost</Label>
                        <Input
                          id="cost"
                          value={formData.cost}
                          onChange={(e) => handleInputChange('cost', e.target.value)}
                          placeholder="e.g. 150.00"
                        />
                      </div>
                      <div>
                        <Label htmlFor="currency">Currency</Label>
                        <Select
                          value={formData.currency}
                          onValueChange={(value) => handleInputChange('currency', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                            <SelectItem value="CNY">CNY</SelectItem>
                            <SelectItem value="GBP">GBP</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </>
                )}

                {/* ✅ INBOUND SAMPLE FIELDS */}
                {formData.sample_direction === "inbound" && (
                  <>
                    <div>
                      <Label htmlFor="testing_status">Testing Status</Label>
                      <Select
                        value={formData.testing_status}
                        onValueChange={(value) => handleInputChange('testing_status', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="not_started">Not Started</SelectItem>
                          <SelectItem value="in_progress">In Progress</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="failed">Failed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="testing_results">Testing Results</Label>
                      <Textarea
                        id="testing_results"
                        value={formData.testing_results}
                        onChange={(e) => handleInputChange('testing_results', e.target.value)}
                        placeholder="Quality analysis, material properties, test results..."
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="quote_requested"
                          checked={formData.quote_requested}
                          onChange={(e) => handleInputChange('quote_requested', e.target.checked)}
                          className="rounded border-gray-300"
                          aria-label="Quote Requested"
                        />
                        <Label htmlFor="quote_requested">Quote Requested</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="quote_provided"
                          checked={formData.quote_provided}
                          onChange={(e) => handleInputChange('quote_provided', e.target.checked)}
                          className="rounded border-gray-300"
                          aria-label="Quote Provided"
                        />
                        <Label htmlFor="quote_provided">Quote Provided</Label>
                      </div>
                    </div>
                  </>
                )}

                {/* ✅ INTERNAL SAMPLE FIELDS */}
                {formData.sample_direction === "internal" && (
                  <div>
                    <Label htmlFor="delivery_date">Target Completion Date</Label>
                    <Input
                      id="delivery_date"
                      type="date"
                      value={formData.delivery_date}
                      onChange={(e) => handleInputChange('delivery_date', e.target.value)}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </div>



          {/* ✅ ACTIONS */}
          <div className="flex items-center gap-4">
            <Button type="submit" disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Creating...' : 'Create Sample'}
            </Button>
            <Button type="button" variant="outline" asChild>
              <Link href="/samples">Cancel</Link>
            </Button>
          </div>
        </form>
      </div>
    </AppShell>
  )
}
