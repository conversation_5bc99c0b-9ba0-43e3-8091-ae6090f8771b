import { db } from "@/lib/db"
import { createSuccessResponse, createErrorResponse } from "@/lib/api-helpers"
import { qualityInspections } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"
import { writeFile, mkdir, unlink } from "fs/promises"
import { join } from "path"
import { createClient } from '@supabase/supabase-js'

// ✅ DUAL ENVIRONMENT: Detect if running in production (Vercel)
const isProduction = process.env.VERCEL === '1' || process.env.NODE_ENV === 'production'

// ✅ SUPABASE STORAGE: Initialize client for production file storage (lazy initialization)
function getSupabaseClient() {
  if (!isProduction) return null

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase environment variables')
    return null
  }

  return createClient(supabaseUrl, supabaseKey)
}

// ✅ PROFESSIONAL ERP: File upload endpoint
export const POST = withTenantAuth(async function POST(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // ✅ SECURITY: Check if inspection exists and belongs to company
    const existing = await db.query.qualityInspections.findFirst({
      where: and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      )
    })

    if (!existing) {
      return createErrorResponse("Quality inspection not found", 404)
    }

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const type = formData.get('type') as string

    if (!files || files.length === 0) {
      return createErrorResponse("No files provided", 400)
    }

    if (!['attachment', 'photo'].includes(type)) {
      return createErrorResponse("Invalid file type", 400)
    }

    const uploadedFiles: string[] = []

    // ✅ DUAL STORAGE: Process each file with environment-aware storage
    for (const file of files) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        return createErrorResponse(`File ${file.name} exceeds 10MB limit`, 400)
      }

      // ✅ SECURITY: Sanitize filename
      const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
      const timestamp = Date.now()
      const filename = `${timestamp}_${sanitizedName}`

      const supabase = getSupabaseClient()
      if (isProduction && supabase) {
        // ✅ PRODUCTION: Use Supabase Storage
        const bucketPath = `quality/${context.companyId}/${id}/${type}/${filename}`

        const bytes = await file.arrayBuffer()
        const { error } = await supabase.storage
          .from('attachments')
          .upload(bucketPath, bytes, {
            contentType: file.type,
            upsert: false
          })

        if (error) {
          console.error(`Failed to upload ${filename} to Supabase:`, error)
          return createErrorResponse(`Failed to upload ${filename}`, 500)
        }

        uploadedFiles.push(filename)
      } else {
        // ✅ LOCAL: Use file system storage
        const uploadDir = join(process.cwd(), 'uploads', 'quality', context.companyId, id)
        await mkdir(uploadDir, { recursive: true })

        const filepath = join(uploadDir, filename)
        const bytes = await file.arrayBuffer()
        const buffer = Buffer.from(bytes)
        await writeFile(filepath, buffer)

        uploadedFiles.push(filename)
      }
    }

    // ✅ DATABASE: Update inspection with new attachments
    const currentAttachments = existing.attachments ? JSON.parse(existing.attachments) : []
    const currentPhotos = existing.photos ? JSON.parse(existing.photos) : []

    let updatedAttachments = currentAttachments
    let updatedPhotos = currentPhotos

    if (type === 'attachment') {
      updatedAttachments = [...currentAttachments, ...uploadedFiles]
    } else {
      updatedPhotos = [...currentPhotos, ...uploadedFiles]
    }

    await db
      .update(qualityInspections)
      .set({
        attachments: JSON.stringify(updatedAttachments),
        photos: JSON.stringify(updatedPhotos),
      })
      .where(and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      ))
      .returning()

    // ✅ AUDIT TRAIL: Log file upload
    console.log(`📎 Quality Inspection Files Uploaded:`, {
      inspectionId: id,
      companyId: context.companyId,
      userId: context.userId,
      fileCount: uploadedFiles.length,
      type,
      timestamp: new Date().toISOString(),
    })

    return createSuccessResponse({
      attachments: updatedAttachments,
      photos: updatedPhotos,
      uploadedFiles,
    })
  } catch (error) {
    console.error("File upload error:", error)
    return createErrorResponse(error)
  }
})

// ✅ PROFESSIONAL ERP: File removal endpoint
export const DELETE = withTenantAuth(async function DELETE(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { filename, type } = await request.json()

    // ✅ SECURITY: Check if inspection exists and belongs to company
    const existing = await db.query.qualityInspections.findFirst({
      where: and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      )
    })

    if (!existing) {
      return createErrorResponse("Quality inspection not found", 404)
    }

    // ✅ DATABASE: Remove file from attachments
    const currentAttachments = existing.attachments ? JSON.parse(existing.attachments) : []
    const currentPhotos = existing.photos ? JSON.parse(existing.photos) : []

    let updatedAttachments = currentAttachments
    let updatedPhotos = currentPhotos

    if (type === 'attachment') {
      updatedAttachments = currentAttachments.filter((f: string) => f !== filename)
    } else {
      updatedPhotos = currentPhotos.filter((f: string) => f !== filename)
    }

    // ✅ DUAL STORAGE: Remove file from appropriate storage system
    const supabase = getSupabaseClient()
    if (isProduction && supabase) {
      // ✅ PRODUCTION: Delete from Supabase Storage
      const bucketPath = `quality/${context.companyId}/${id}/${type}/${filename}`
      const { error } = await supabase.storage
        .from('attachments')
        .remove([bucketPath])

      if (error) {
        console.warn("Failed to delete file from Supabase:", filename, error)
      }
    } else {
      // ✅ LOCAL: Delete from file system
      try {
        const filepath = join(process.cwd(), 'uploads', 'quality', context.companyId, id, filename)
        await unlink(filepath)
      } catch (fileError) {
        console.warn("File not found on disk:", filename)
      }
    }

    await db
      .update(qualityInspections)
      .set({
        attachments: JSON.stringify(updatedAttachments),
        photos: JSON.stringify(updatedPhotos),
      })
      .where(and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      ))
      .returning()

    // ✅ AUDIT TRAIL: Log file removal
    console.log(`🗑️ Quality Inspection File Removed:`, {
      inspectionId: id,
      companyId: context.companyId,
      userId: context.userId,
      filename,
      type,
      timestamp: new Date().toISOString(),
    })

    return createSuccessResponse({
      attachments: updatedAttachments,
      photos: updatedPhotos,
    })
  } catch (error) {
    console.error("File removal error:", error)
    return createErrorResponse(error)
  }
})
