import { db } from "@/lib/db"
import { json<PERSON><PERSON>, json<PERSON>rror, createErrorResponse } from "@/lib/api-helpers"
import { qualityInspections } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"
import { withTenantAuth } from "@/lib/tenant-utils"
import { qualityInventoryIntegrationService } from "@/lib/services/quality-inventory-integration"

// ✅ MANUFACTURING ERP: Comprehensive schema for quality inspection updates
const patchSchema = z.object({
  work_order_id: z.string().optional(),
  inspector: z.string().optional(),
  inspection_type: z.enum(["incoming", "in_process", "final", "pre_shipment"]).optional(),
  status: z.enum(["pending", "in-progress", "in_progress", "passed", "completed", "failed", "scheduled"]).optional(),
  scheduled_date: z.string().optional(),
  completed_date: z.string().optional(),
  notes: z.string().optional(),
  // ✅ ATTACHMENT PRESERVATION: These fields are managed by attachment API
  // attachments and photos are intentionally excluded to prevent overwriting
})

// 🛡️ SECURE: Multi-tenant GET endpoint
export const GET = withTenantAuth(async function GET(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // ✅ FIXED: Simplified query to avoid relation errors
    const row = await db.select().from(qualityInspections)
      .where(and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId) // ✅ MULTI-TENANT SECURITY
      ))
      .limit(1)
      .then(rows => rows[0] || null)

    if (!row) {
      return jsonError("Quality inspection not found", 404)
    }

    return jsonOk(row)
  } catch (error) {
    console.error("GET quality inspection error:", error)
    return jsonError("Failed to fetch quality inspection", 500)
  }
})

// 🛡️ SECURE: Multi-tenant PATCH endpoint - SIMPLIFIED
export const PATCH = withTenantAuth(async function PATCH(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const data = patchSchema.parse(body)

    // ✅ SECURITY: Check if inspection exists and belongs to company
    const existing = await db.query.qualityInspections.findFirst({
      where: and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      )
    })

    if (!existing) {
      return jsonError("Quality inspection not found", 404)
    }

    // ✅ AUDIT TRAIL: Log status changes
    if (data.status && data.status !== existing.status) {
      console.log(`🔍 Quality Inspection Status Change:`, {
        inspectionId: id,
        companyId: context.companyId,
        userId: context.userId,
        oldStatus: existing.status,
        newStatus: data.status,
        timestamp: new Date().toISOString(),
      })
    }

    // ✅ ATTACHMENT PRESERVATION: Only update provided fields, preserving attachments/photos
    console.log(`🔍 Quality Inspection Update:`, {
      inspectionId: id,
      companyId: context.companyId,
      userId: context.userId,
      fieldsUpdated: Object.keys(data),
      timestamp: new Date().toISOString(),
    })

    const [updatedRow] = await db
      .update(qualityInspections)
      .set(data) // Only updates provided fields, preserves attachments/photos
      .where(and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      ))
      .returning()

    // ✅ PROFESSIONAL INVENTORY INTEGRATION: 4-Status Quality System
    if (data.status && data.status !== existing.status) {
      try {
        // Professional ERP Workflow: Status-based inventory management
        if (data.status === "passed" || data.status === "completed") {
          await qualityInventoryIntegrationService.onQualityInspectionPassed(id, {
            companyId: context.companyId,
            userId: context.userId
          })
          console.log(`✅ Quality-Inventory Integration: Inspection ${id} approved, inventory updated`)
        } else if (data.status === "failed") {
          await qualityInventoryIntegrationService.onQualityInspectionFailed(id, {
            companyId: context.companyId,
            userId: context.userId
          })
          console.log(`⚠️ Quality-Inventory Integration: Inspection ${id} failed, stock quarantined for review`)
        } else if (data.status === "in-progress" || data.status === "scheduled") {
          // Professional ERP: Create pending stock lot when inspection starts
          await qualityInventoryIntegrationService.createPendingStockLot(id, {
            companyId: context.companyId,
            userId: context.userId
          })
          console.log(`📋 Quality-Inventory Integration: Inspection ${id} started, pending stock lot created`)
        }
        // Note: "rejected" status handled manually via separate endpoint for business approval
      } catch (integrationError) {
        console.error(`❌ Quality-Inventory Integration Error for inspection ${id}:`, integrationError)
        // Don't fail the main update, just log the integration error
      }
    }

    return jsonOk(updatedRow)
  } catch (error) {
    console.error("PATCH quality inspection error:", error)
    return jsonError("Failed to update quality inspection", 500)
  }
})

// 🚫 COMPLIANCE: Hard delete disabled for regulatory compliance
// Use /api/quality/inspections/[id]/archive endpoint instead
export const DELETE = withTenantAuth(async function DELETE(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  // ✅ COMPLIANCE: Prevent hard deletion for audit trail preservation
  return createErrorResponse(
    "Hard deletion not allowed for compliance. Use archive functionality instead.",
    { status: 403 }
  )
})
