import { NextRequest } from 'next/server'
import { withTenantAuth } from '@/lib/tenant-utils'
import { jsonError } from '@/lib/api-helpers'
import path from 'path'
import fs from 'fs'
import { createClient } from '@supabase/supabase-js'

// ✅ DUAL ENVIRONMENT: Detect if running in production (Vercel)
const isProduction = process.env.VERCEL === '1' || process.env.NODE_ENV === 'production'

// ✅ SUPABASE STORAGE: Initialize client for production file serving (lazy initialization)
function getSupabaseClient() {
  if (!isProduction) return null

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase environment variables')
    return null
  }

  return createClient(supabaseUrl, supabaseKey)
}

// ✅ MANUFACTURING ERP: Secure file serving endpoint for quality inspection attachments
export const GET = withTenantAuth(async function GET(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string; filename: string }> }
) {
  try {
    const { id, filename } = await params

    console.log('🔍 File serving request:', {
      inspectionId: id,
      filename,
      companyId: context.companyId,
      userId: context.userId,
    })

    // ✅ SECURITY: Validate filename to prevent directory traversal
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      console.error('❌ Invalid filename detected:', filename)
      return jsonError('Invalid filename', 400)
    }

    let fileBuffer: Buffer
    let fileSize: number

    const supabase = getSupabaseClient()
    if (isProduction && supabase) {
      // ✅ PRODUCTION: Serve from Supabase Storage
      // Try both attachment and photo paths since we don't know the type
      const attachmentPath = `quality/${context.companyId}/${id}/attachment/${filename}`
      const photoPath = `quality/${context.companyId}/${id}/photo/${filename}`

      let { data, error } = await supabase.storage
        .from('attachments')
        .download(attachmentPath)

      if (error) {
        // Try photo path if attachment path fails
        const photoResult = await supabase.storage
          .from('attachments')
          .download(photoPath)

        if (photoResult.error) {
          console.error('❌ File not found in Supabase storage:', filename)
          return new Response('File not found', { status: 404 })
        }

        data = photoResult.data
      }

      fileBuffer = Buffer.from(await data.arrayBuffer())
      fileSize = fileBuffer.length
    } else {
      // ✅ LOCAL: Serve from file system
      const uploadsDir = path.join(process.cwd(), 'uploads', 'quality', context.companyId, id)
      const filePath = path.join(uploadsDir, filename)

      console.log('🔍 Attempting to serve file:', {
        uploadsDir,
        filePath,
        exists: fs.existsSync(filePath),
      })

      // ✅ SECURITY: Verify file exists and is within allowed directory
      if (!fs.existsSync(filePath)) {
        console.error('❌ File not found:', filePath)
        return new Response('File not found', { status: 404 })
      }

      // ✅ SECURITY: Ensure file is within the expected directory (prevent path traversal)
      const resolvedPath = path.resolve(filePath)
      const resolvedUploadsDir = path.resolve(uploadsDir)
      if (!resolvedPath.startsWith(resolvedUploadsDir)) {
        console.error('❌ Path traversal attempt detected:', { resolvedPath, resolvedUploadsDir })
        return jsonError('Access denied', 403)
      }

      // ✅ FILE SERVING: Read file from local storage
      fileBuffer = fs.readFileSync(filePath)
      fileSize = fileBuffer.length
    }
    const fileExtension = path.extname(filename).toLowerCase()

    // ✅ CONTENT TYPE: Set appropriate content type based on file extension
    let contentType = 'application/octet-stream'
    const contentTypeMap: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.txt': 'text/plain',
    }

    if (contentTypeMap[fileExtension]) {
      contentType = contentTypeMap[fileExtension]
    }

    console.log('✅ File served successfully:', {
      filename,
      contentType,
      size: fileSize,
      environment: isProduction ? 'production' : 'local',
      timestamp: new Date().toISOString(),
    })

    // ✅ RESPONSE: Return file with appropriate headers
    return new Response(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Length': fileSize.toString(),
        'Cache-Control': 'private, max-age=3600', // Cache for 1 hour
        'Content-Disposition': `inline; filename="${filename}"`,
      },
    })

  } catch (error) {
    console.error('❌ File serving error:', error)
    return jsonError('Failed to serve file', 500)
  }
})
