import { db } from "@/lib/db"
import { json<PERSON>k, jsonError } from "@/lib/api-helpers"
import { qualityInspections } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"
import { withTenantAuth } from "@/lib/tenant-utils"

// ✅ COMPLIANCE: Unarchive schema with business justification
const unarchiveSchema = z.object({
  businessJustification: z.string().min(10, "Business justification must be at least 10 characters"),
  originalArchiveReason: z.string(),
  originalArchivedBy: z.string(),
  originalArchivedAt: z.string(),
})

// 🔓 ERP STANDARD: Unarchive endpoint with full audit trail
export const POST = withTenantAuth(async function POST(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    console.log('🔍 Unarchive request received:', { id, body })

    const data = unarchiveSchema.parse(body)
    console.log('✅ Validation passed:', data)

    // ✅ SECURITY: Verify inspection exists and is archived (same pattern as archive endpoint)
    const existing = await db.query.qualityInspections.findFirst({
      where: and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      )
    })

    if (!existing) {
      console.error('❌ Inspection not found:', { id, companyId: context.companyId })
      return jsonError("Quality inspection not found", 404)
    }

    console.log('✅ Inspection found:', {
      id: existing.id,
      archived: existing.archived,
      archivedAt: existing.archived_at
    })

    if (!existing.archived) {
      console.error('❌ Inspection is not archived:', { id, archived: existing.archived })
      return jsonError("Inspection is not archived", 400)
    }

    // ✅ BUSINESS RULE: Check unarchive time window (30 days for standard operations)
    const archiveDate = new Date(existing.archived_at!)
    const daysSinceArchive = Math.floor((Date.now() - archiveDate.getTime()) / (1000 * 60 * 60 * 24))

    if (daysSinceArchive > 30) {
      // ✅ COMPLIANCE: Extended retention requires additional justification
      if (!data.businessJustification.toLowerCase().includes('regulatory') &&
        !data.businessJustification.toLowerCase().includes('compliance') &&
        !data.businessJustification.toLowerCase().includes('audit')) {
        return jsonError(
          "Inspections archived over 30 days require regulatory/compliance justification",
          400
        )
      }
    }

    // ✅ COMPLIANCE: Enhanced audit trail for unarchive
    const unarchiveAuditNotes = [
      existing.notes || '',
      '',
      `=== UNARCHIVE AUDIT TRAIL ===`,
      `Unarchived: ${new Date().toISOString()}`,
      `Unarchived by: ${context.userId}`,
      `Business Justification: ${data.businessJustification}`,
      `Original Archive Reason: ${data.originalArchiveReason}`,
      `Original Archived By: ${data.originalArchivedBy}`,
      `Original Archived At: ${data.originalArchivedAt}`,
      `Days Since Archive: ${daysSinceArchive}`,
      `Company ID: ${context.companyId}`,
      '================================'
    ].filter(Boolean).join('\n')

    // ✅ RESTORE: Unarchive the inspection with full audit trail
    console.log('🔄 Attempting to unarchive inspection...')
    const updateResult = await db
      .update(qualityInspections)
      .set({
        archived: false,
        archived_at: null,
        archived_by: null,
        archive_reason: null,
        notes: unarchiveAuditNotes,
      })
      .where(and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      ))
      .returning()

    console.log('🔍 Update result:', updateResult)

    if (!updateResult || updateResult.length === 0) {
      console.error('❌ No rows updated during unarchive')
      return jsonError("Failed to unarchive inspection", 500)
    }

    const [unarchivedInspection] = updateResult

    // ✅ AUDIT TRAIL: Log unarchive action
    console.log('📂 Quality Inspection Unarchived:', {
      inspectionId: id,
      companyId: context.companyId,
      userId: context.userId,
      businessJustification: data.businessJustification,
      originalArchiveReason: data.originalArchiveReason,
      daysSinceArchive,
      timestamp: new Date().toISOString(),
    })

    return jsonOk(unarchivedInspection)
  } catch (error) {
    console.error("❌ UNARCHIVE quality inspection error:", error)

    // Get id from params safely
    let inspectionId = 'unknown'
    try {
      const { id: paramId } = await params
      inspectionId = paramId
    } catch (e) {
      // params might not be available in error context
    }

    console.error("Error details:", {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      inspectionId,
      companyId: context?.companyId || 'unknown'
    })

    if (error instanceof z.ZodError) {
      console.error("Validation errors:", error.errors)
      return jsonError("Validation failed", 400, error.errors)
    }

    return jsonError("Failed to unarchive inspection", 500)
  }
})
