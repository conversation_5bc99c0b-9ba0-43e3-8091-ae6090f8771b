import { db } from "@/lib/db"
import { json<PERSON>k, jsonError } from "@/lib/api-helpers"
import { qualityInspections } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"
import { withTenantAuth } from "@/lib/tenant-utils"

// ✅ COMPLIANCE: Archive schema with reason codes
const archiveSchema = z.object({
  reason: z.enum([
    "cancelled_by_customer",
    "inspection_no_longer_needed",
    "duplicate_inspection",
    "data_entry_error",
    "process_change",
    "other"
  ]),
  notes: z.string().optional(),
})

// 🛡️ SECURE: Multi-tenant ARCHIVE endpoint (compliance-friendly)
export const POST = withTenantAuth(async function POST(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const data = archiveSchema.parse(body)

    // ✅ SECURITY: Check if inspection exists and belongs to company
    const existing = await db.query.qualityInspections.findFirst({
      where: and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      )
    })

    if (!existing) {
      return jsonError("Quality inspection not found", 404)
    }

    // ✅ BUSINESS RULE: Prevent archiving of passed inspections (compliance)
    if (existing.status === "passed") {
      return jsonError("Cannot archive passed inspections - contact quality manager", 400)
    }

    // ✅ COMPLIANCE: Archive instead of delete with full audit trail
    console.log('🔍 Archiving inspection:', { id, companyId: context.companyId, reason: data.reason })

    const updateResult = await db
      .update(qualityInspections)
      .set({
        archived: true,
        archived_at: new Date(),
        archived_by: context.userId,
        archive_reason: data.reason,
        notes: data.notes ? `${existing.notes || ''}\n\nArchive Notes: ${data.notes}`.trim() : existing.notes,
      })
      .where(and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      ))
      .returning()

    console.log('🔍 Update result:', updateResult)

    if (!updateResult || updateResult.length === 0) {
      console.error('❌ No rows updated during archive')
      return jsonError("Failed to archive inspection - no rows updated", 500)
    }

    const [archivedInspection] = updateResult

    // ✅ AUDIT TRAIL: Log archive action
    console.log(`📁 Quality Inspection Archived:`, {
      inspectionId: id,
      companyId: context.companyId,
      userId: context.userId,
      reason: data.reason,
      timestamp: new Date().toISOString(),
    })

    return jsonOk(archivedInspection)
  } catch (error) {
    console.error("ARCHIVE quality inspection error:", error)
    return jsonError("Failed to archive inspection", 500)
  }
})

// 🛡️ SECURE: Multi-tenant RESTORE endpoint (unarchive)
export const DELETE = withTenantAuth(async function DELETE(
  request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // ✅ SECURITY: Check if inspection exists and belongs to company
    const existing = await db.query.qualityInspections.findFirst({
      where: and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      )
    })

    if (!existing) {
      return jsonError("Quality inspection not found", 404)
    }

    if (!existing.archived) {
      return jsonError("Inspection is not archived", 400)
    }

    // ✅ COMPLIANCE: Restore archived inspection
    const [restoredInspection] = await db
      .update(qualityInspections)
      .set({
        archived: false,
        archived_at: null,
        archived_by: null,
        archive_reason: null,
      })
      .where(and(
        eq(qualityInspections.id, id),
        eq(qualityInspections.company_id, context.companyId)
      ))
      .returning()

    // ✅ AUDIT TRAIL: Log restore action
    console.log(`📤 Quality Inspection Restored:`, {
      inspectionId: id,
      companyId: context.companyId,
      userId: context.userId,
      timestamp: new Date().toISOString(),
    })

    return jsonOk(restoredInspection)
  } catch (error) {
    console.error("RESTORE quality inspection error:", error)
    return jsonError("Failed to restore inspection", 500)
  }
})
