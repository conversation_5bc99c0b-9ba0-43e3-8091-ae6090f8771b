import { db, uid } from "@/lib/db"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { qualityInspections } from "@/lib/schema-postgres"
import { desc, eq, and, not } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { qualityInspectionSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { createReferenceNotFoundError } from "@/lib/errors"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    // ✅ FIXED: Full query with archive fields working
    const rows = await db.select().from(qualityInspections)
      .where(eq(qualityInspections.company_id, context.companyId))
      .orderBy(desc(qualityInspections.created_at))
    return jsonOk(rows)
  } catch (error) {
    console.error("GET quality inspections error:", error)
    return jsonError("Failed to fetch inspections", 500)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: NextRequest, context) {
  try {
    console.log('🔍 Quality Inspection Creation - Request received')

    // Validate request body
    const validation = await validateRequestBody(req, qualityInspectionSchema)

    if (!validation.success) {
      console.error('❌ Validation failed:', validation.error.issues)
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    const id = uid("qi")

    console.log('✅ Validation passed. Creating inspection:', {
      id,
      companyId: context.companyId,
      workOrderId: body.work_order_id,
      inspector: body.inspector,
      inspectionType: body.inspection_type
    })

    // 🛡️ CRITICAL: Validate that work order exists and belongs to current company
    if (body.work_order_id) {
      const workOrder = await db.query.workOrders.findFirst({
        where: (workOrders, { eq, and }) => and(
          eq(workOrders.id, body.work_order_id),
          eq(workOrders.company_id, context.companyId)
        )
      })

      if (!workOrder) {
        throw createReferenceNotFoundError("Work Order", body.work_order_id)
      }
    }

    const newInspection = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      work_order_id: body.work_order_id,
      inspection_type: body.inspection_type,
      inspector: body.inspector,
      inspection_date: body.scheduled_date || new Date().toISOString().split('T')[0], // 🔧 FIX: Map scheduled_date to required inspection_date
      status: body.status || "scheduled",
      notes: body.notes,
    }

    // 🔧 FIX: Insert inspection and handle response properly
    const insertResult = await db.insert(qualityInspections).values(newInspection).returning()
    const createdInspection = insertResult[0]

    console.log('✅ Inspection created successfully:', {
      id: createdInspection.id,
      workOrderId: createdInspection.work_order_id,
      inspector: createdInspection.inspector
    })

    // 🔧 FIX: Return simple response to avoid relation errors
    // The client will refresh the table to get full data with relations
    return jsonOk({
      id: createdInspection.id,
      message: "Quality inspection created successfully"
    }, { status: 201 })
  } catch (error) {
    console.error("POST quality inspection error:", error)
    return jsonError("Failed to create inspection", 500)
  }
})
