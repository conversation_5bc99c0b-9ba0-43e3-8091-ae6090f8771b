import { NextRequest } from "next/server"
import { db } from "@/lib/db"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { workOrders, qualityInspections } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint for quality gate validation
export const GET = withTenantAuth(async function GET(
  req: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 1. Get work order with product and quality inspections
    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, id),
        eq(workOrders.company_id, context.companyId)
      ),
      with: {
        product: true,
        qualityInspections: true,
      }
    })

    if (!workOrder) {
      return jsonError("Work order not found", 404)
    }

    if (!workOrder.product) {
      return jsonError("Product not found for work order", 404)
    }

    // 2. Determine if quality inspection is required
    const requiresInspection = workOrder.product.inspection_required === "true"

    // 3. Check quality gate status using same logic as quality workflow service
    let canComplete = true
    let reason = ""

    if (requiresInspection) {
      const allInspections = workOrder.qualityInspections
      const passedInspections = allInspections.filter(qi => qi.status === "passed")
      const pendingInspections = allInspections.filter(qi => qi.status === "pending")
      const failedInspections = allInspections.filter(qi => qi.status === "failed")

      console.log(`🔍 Quality Gate API: Checking ${allInspections.length} inspections`)
      console.log(`   - Passed: ${passedInspections.length}`)
      console.log(`   - Pending: ${pendingInspections.length}`)
      console.log(`   - Failed: ${failedInspections.length}`)

      // ✅ PROFESSIONAL ERP: Must have NO pending inspections AND at least one passed inspection
      if (pendingInspections.length > 0) {
        canComplete = false
        reason = `${pendingInspections.length} quality inspection(s) are still pending approval`
      } else if (passedInspections.length === 0) {
        canComplete = false
        if (allInspections.length === 0) {
          reason = "No quality inspections found - inspection may need to be created"
        } else {
          reason = "No quality inspections have been approved yet"
        }
      } else if (failedInspections.length > 0) {
        canComplete = false
        reason = `${failedInspections.length} quality inspection(s) have failed and need to be addressed`
      }
    }

    // 4. Return quality gate status
    return jsonOk({
      canComplete,
      reason,
      requiresInspection,
      workOrder: {
        id: workOrder.id,
        number: workOrder.number,
        status: workOrder.status
      },
      product: {
        id: workOrder.product.id,
        name: workOrder.product.name,
        sku: workOrder.product.sku,
        inspection_required: workOrder.product.inspection_required
      },
      qualityInspections: workOrder.qualityInspections.map(qi => ({
        id: qi.id,
        status: qi.status,
        inspection_type: qi.inspection_type,
        inspector: qi.inspector,
        inspection_date: qi.inspection_date,
        notes: qi.notes
      }))
    })

  } catch (error) {
    console.error("Quality gate check error:", error)
    return jsonError("Failed to check quality gate", 500)
  }
})
