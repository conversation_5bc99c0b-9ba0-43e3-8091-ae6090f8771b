import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse, jsonError } from "@/lib/api-helpers"
import { workOrders, workOperations, qualityInspections } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"
import { qualityWorkflowService } from "@/lib/services/quality-workflow"
import { workOrderInventoryIntegrationService } from "@/lib/services/work-order-inventory-integration"
import { withTenantAuth } from "@/lib/tenant-utils"
import {
  createReferenceNotFoundError,
  createInvalidWorkflowTransitionError
} from "@/lib/errors"

const patchSchema = z.object({
  workOperationId: z.string().optional(),
  status: z.string().optional(),
  action: z.enum(["update_operation", "start_production", "complete_production"]).optional(),
  // ✅ PROFESSIONAL ERP: Inline editing fields
  qty: z.string().optional(),
  due_date: z.string().nullable().optional(),
  priority: z.enum(["low", "normal", "high", "urgent"]).optional(),
  notes: z.string().nullable().optional(),
})

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, id),
        eq(workOrders.company_id, context.companyId) // 🛡️ CRITICAL: Multi-tenant isolation
      ),
      with: {
        salesContract: {
          with: {
            customer: true,
          },
        },
        product: true,
        operations: true,
        // TODO: Add quality inspections when schema relationships are fixed
        // qualityInspections: true,
      },
    })

    if (!workOrder) {
      throw createReferenceNotFoundError("Work Order", id)
    }

    return createSuccessResponse(workOrder)
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant PATCH endpoint with proper isolation
export const PATCH = withTenantAuth(async function PATCH(req: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const body = await req.json()
    const data = patchSchema.parse(body)

    // 🛡️ CRITICAL: Verify work order belongs to current company
    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, id),
        eq(workOrders.company_id, context.companyId)
      ),
    })

    if (!workOrder) {
      throw createReferenceNotFoundError("Work Order", id)
    }

    // Handle different types of updates
    if (data.action === "update_operation" && data.workOperationId) {
      // Update work operation status
      const updated = await db
        .update(workOperations)
        .set({ status: data.status })
        .where(eq(workOperations.id, data.workOperationId))
        .returning()

      return createSuccessResponse(updated[0], "Operation status updated")

    } else if (data.action === "start_production") {
      // Check quality gate before starting production
      const canStart = await qualityWorkflowService.validateQualityGate(id, "start_production")

      if (!canStart) {
        throw createInvalidWorkflowTransitionError(
          "Cannot start production: Quality approval required"
        )
      }

      const updated = await db
        .update(workOrders)
        .set({ status: "in_progress" })
        .where(eq(workOrders.id, id))
        .returning()

      return createSuccessResponse(updated[0], "Production started")

    } else if (data.action === "complete_production") {
      // Check quality gate before completing production
      const canComplete = await qualityWorkflowService.validateQualityGate(id, "complete_production")

      if (!canComplete) {
        throw createInvalidWorkflowTransitionError(
          "Cannot complete production: Quality approval required"
        )
      }

      const updated = await db
        .update(workOrders)
        .set({ status: "completed" })
        .where(eq(workOrders.id, id))
        .returning()

      // ✅ INVENTORY INTEGRATION: Trigger inventory updates when work order is completed
      console.log(`🚀 API: Starting inventory integration for work order ${id}`)
      try {
        await workOrderInventoryIntegrationService.onWorkOrderCompleted(id, {
          companyId: context.companyId,
          userId: context.userId
        })
        console.log(`🎉 API: Work Order-Inventory Integration SUCCESSFUL for work order ${id}`)
      } catch (integrationError) {
        console.error(`🚨 API: Work Order-Inventory Integration FAILED for work order ${id}`)
        console.error(`❌ Integration Error Type: ${integrationError instanceof Error ? integrationError.constructor.name : typeof integrationError}`)
        console.error(`❌ Integration Error Message: ${integrationError instanceof Error ? integrationError.message : String(integrationError)}`)
        console.error(`❌ Integration Error Stack:`, integrationError instanceof Error ? integrationError.stack : 'No stack trace')

        // Don't fail the main update, but ensure error is visible
        console.error(`⚠️ Work order status updated but inventory integration failed - manual intervention may be required`)
      }

      return createSuccessResponse(updated[0], "Production completed")

    } else {
      // ✅ PROFESSIONAL ERP: Direct field updates for inline editing
      if (!data.action) {
        // Prepare update object with only provided fields
        const updateData: any = {}
        if (data.status !== undefined) updateData.status = data.status
        if (data.qty !== undefined) updateData.qty = data.qty
        if (data.due_date !== undefined) updateData.due_date = data.due_date
        if (data.priority !== undefined) updateData.priority = data.priority
        if (data.notes !== undefined) updateData.notes = data.notes

        // Only update if there are fields to update
        if (Object.keys(updateData).length === 0) {
          throw createReferenceNotFoundError("Update Data", "No fields provided")
        }

        // ✅ PROFESSIONAL ERP: Simple work order update (no contract sync)
        const updated = await db
          .update(workOrders)
          .set(updateData)
          .where(and(
            eq(workOrders.id, id),
            eq(workOrders.company_id, context.companyId)
          ))
          .returning()

        if (updated.length === 0) {
          throw createReferenceNotFoundError("Work Order", id)
        }

        return createSuccessResponse(updated[0], "Work order updated successfully")
      }

      // Legacy support: update work operation if workOperationId provided
      if (data.workOperationId) {
        const updated = await db
          .update(workOperations)
          .set({ status: data.status })
          .where(eq(workOperations.id, data.workOperationId))
          .returning()

        return createSuccessResponse(updated[0], "Operation status updated")
      } else {
        throw createReferenceNotFoundError("Work Operation or Action", "N/A")
      }
    }
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant DELETE endpoint with proper isolation
export const DELETE = withTenantAuth(async function DELETE(request: Request, context, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params

    // 🛡️ CRITICAL: Verify work order belongs to current company before deletion
    const workOrder = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, id),
        eq(workOrders.company_id, context.companyId)
      ),
    })

    if (!workOrder) {
      return jsonError("Work order not found", 404)
    }

    // 🗑️ CASCADE DELETE: Delete related records in correct order (respecting foreign key constraints)

    // 1. Delete quality inspections first (they reference work_order_id)
    try {
      await db.delete(qualityInspections).where(
        and(
          eq(qualityInspections.work_order_id, id),
          eq(qualityInspections.company_id, context.companyId)
        )
      )
    } catch (error) {
      console.log("Quality inspections delete attempt completed:", error)
    }

    // 2. Delete work operations (they reference work_order_id)
    await db.delete(workOperations).where(
      and(
        eq(workOperations.work_order_id, id),
        eq(workOperations.company_id, context.companyId)
      )
    )

    // 3. Finally, delete the work order itself
    await db.delete(workOrders).where(
      and(
        eq(workOrders.id, id),
        eq(workOrders.company_id, context.companyId)
      )
    )

    return new Response(null, { status: 204 })
  } catch (error) {
    console.error("Error deleting work order:", error)
    return jsonError("Failed to delete work order", 500)
  }
})
