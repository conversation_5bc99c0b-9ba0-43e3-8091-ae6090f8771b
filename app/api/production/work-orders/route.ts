import { db, uid } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { workOrders, workOperations, qualityInspections, products } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { createReferenceNotFoundError } from "@/lib/errors"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.workOrders.findMany({
      where: eq(workOrders.company_id, context.companyId),
      orderBy: [desc(workOrders.created_at)],
      with: {
        salesContract: true,
        product: true,
      },
    })
    return createSuccessResponse(rows)
  } catch (error) {
    return createErrorResponse(error)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: Request, context) {
  try {
    const body = await req.json()
    const id = uid("wo")

    // 🛡️ CRITICAL: Validate that product exists and belongs to current company
    const product = await db.query.products.findFirst({
      where: and(
        eq(products.id, body.product_id),
        eq(products.company_id, context.companyId)
      )
    })

    if (!product) {
      throw createReferenceNotFoundError("Product", body.product_id)
    }

    // Determine quality requirements for this work order
    let qualityRequired = "auto" // Default
    let qualityStatus = "pending"

    if (product.inspection_required === "true") {
      qualityRequired = "required"
    } else if (product.inspection_required === "false") {
      qualityRequired = "not_required"
      qualityStatus = "approved" // Auto-approve if no inspection required
    }

    const newWorkOrder = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      number: body.number,
      sales_contract_id: body.sales_contract_id,
      product_id: body.product_id,
      qty: body.qty,
      quality_required: qualityRequired,
      quality_status: qualityStatus,
    }

    const operations = (body.operations || []).map((op: any, index: number) => ({
      id: uid("wop"),
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      work_order_id: id,
      operation_name: op.name,
      sequence: (index + 1).toString(),
      status: "pending",
    }))

    // ✅ MANUFACTURING ERP: Only create FINAL inspection for work orders
    // Incoming inspections are for materials, in-process are workflow-triggered
    const inspections = []
    if (qualityRequired === "required" || qualityRequired === "auto") {
      // Create only final inspection - proper manufacturing ERP pattern
      inspections.push({
        id: uid("qi"),
        company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
        work_order_id: id,
        inspection_type: "final", // Only final inspection for work orders
        inspector: "TBD", // To be assigned
        status: "scheduled", // Scheduled for work order completion
        inspection_date: new Date().toISOString().split('T')[0], // Required field: today's date
        notes: `Final inspection scheduled for work order ${body.number}`,
      })
    }

    await db.transaction(async (tx) => {
      await tx.insert(workOrders).values(newWorkOrder)

      if (operations.length > 0) {
        await tx.insert(workOperations).values(operations)
      }

      if (inspections.length > 0) {
        await tx.insert(qualityInspections).values(inspections)
      }
    })

    // 🛡️ SECURE: Only return work order if it belongs to current company
    const row = await db.query.workOrders.findFirst({
      where: and(
        eq(workOrders.id, id),
        eq(workOrders.company_id, context.companyId)
      ),
      with: {
        salesContract: true,
        product: true,
        operations: true,
      },
    })
    return createSuccessResponse(row, "Work order created successfully", 201)
  } catch (error) {
    return createErrorResponse(error)
  }
})
