import { db } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { samples } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { withTenantAuth, createForbiddenResponse } from "@/lib/tenant-utils"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { sampleUpdateSchema, sampleDetailQuerySchema } from "@/lib/validations"
import { NextRequest } from "next/server"

// 🛡️ SECURE: Multi-tenant GET endpoint with comprehensive sample detail retrieval
export const GET = withTenantAuth(async function GET(request: Request, context, { params }: { params: Promise<{ id: string }> }) {
  const startTime = Date.now()

  try {
    const { id } = await params

    // ✅ VALIDATE SAMPLE ID FORMAT
    if (!id || typeof id !== 'string' || id.length < 3) {
      return jsonError("Invalid sample ID format", 400)
    }

    // ✅ PARSE AND VALIDATE QUERY PARAMETERS
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())

    const queryValidation = sampleDetailQuerySchema.safeParse(queryParams)
    if (!queryValidation.success) {
      console.error("Sample detail query validation failed:", queryValidation.error.issues)
      return createValidationErrorResponse(queryValidation.error.issues)
    }

    const {
      include,
      include_customer_details,
      include_product_details,
      include_supplier_details,
      include_approval_history,
      include_workflow_actions,
      include_metadata,
      format
    } = queryValidation.data

    // ✅ DETERMINE WHAT TO INCLUDE BASED ON PARAMETERS
    const includeFields = include ? include.split(',').map(field => field.trim()) : ['customer', 'product', 'supplier']

    // ✅ BUILD DYNAMIC RELATIONSHIP LOADING
    const withClause: any = {}

    // Core relationships
    if (includeFields.includes('customer') || include_customer_details) {
      withClause.customer = include_customer_details ? {
        // Include detailed customer information
        columns: {
          id: true,
          name: true,
          contact_name: true,
          contact_phone: true,
          contact_email: true,
          address: true,
          incoterm: true,
          payment_term: true,
          status: true,
        }
      } : true
    }

    if (includeFields.includes('product') || include_product_details) {
      withClause.product = include_product_details ? {
        // Include detailed product information
        columns: {
          id: true,
          sku: true,
          name: true,
          unit: true,
          hs_code: true,
          origin: true,
          package: true,
          image: true,
        }
      } : true
    }

    if (includeFields.includes('supplier') || include_supplier_details) {
      withClause.supplier = include_supplier_details ? {
        // Include detailed supplier information
        columns: {
          id: true,
          name: true,
          contact_name: true,
          contact_phone: true,
          contact_email: true,
          address: true,
          bank: true,
          tax_id: true,
          status: true,
        }
      } : true
    }

    // ✅ FETCH SAMPLE WITH OPTIMIZED RELATIONSHIPS
    const sample = await db.query.samples.findFirst({
      where: and(
        eq(samples.id, id),
        eq(samples.company_id, context.companyId)
      ),
      with: withClause,
    })

    if (!sample) {
      console.warn("Sample not found or unauthorized access:", {
        sampleId: id,
        companyId: context.companyId,
        userId: context.userId,
        timestamp: new Date().toISOString(),
      })
      return jsonError("Sample not found", 404)
    }

    // ✅ APPROVAL WORKFLOW STATUS TRACKING
    const approvalWorkflow = {
      current_status: sample.approval_status,
      approved_by: sample.approved_by,
      approved_date: sample.approved_date,
      rejection_reason: sample.rejection_reason,

      // ✅ WORKFLOW STATE VALIDATION
      can_approve: sample.approval_status === 'pending' || sample.approval_status === 'revision_required',
      can_reject: sample.approval_status === 'pending' || sample.approval_status === 'revision_required',
      can_request_revision: sample.approval_status === 'pending',
      can_resubmit: sample.approval_status === 'rejected' || sample.approval_status === 'revision_required',

      // ✅ NEXT POSSIBLE ACTIONS
      next_actions: (() => {
        const actions = []
        if (sample.approval_status === 'pending' || sample.approval_status === 'revision_required') {
          actions.push('approve', 'reject', 'request_revision')
        }
        if (sample.approval_status === 'rejected' || sample.approval_status === 'revision_required') {
          actions.push('resubmit')
        }
        if (sample.approval_status === 'approved') {
          actions.push('generate_contract', 'create_work_order') // Phase 2 actions
        }
        return actions
      })(),
    }

    // ✅ APPROVAL HISTORY (if requested)
    const approvalHistory = include_approval_history ? [
      {
        action: 'created',
        timestamp: sample.created_at,
        status: 'pending',
        user: 'System',
        notes: 'Sample created and submitted for approval'
      },
      ...(sample.approved_date ? [{
        action: sample.approval_status === 'approved' ? 'approved' : sample.approval_status === 'rejected' ? 'rejected' : 'revised',
        timestamp: sample.approved_date,
        status: sample.approval_status,
        user: sample.approved_by || 'Unknown',
        notes: sample.rejection_reason || 'Sample processed'
      }] : [])
    ] : undefined

    // ✅ FORMAT RESPONSE BASED ON REQUEST
    let responseData: any = sample

    if (format === 'detailed') {
      responseData = {
        ...sample,
        workflow: include_workflow_actions ? approvalWorkflow : {
          current_status: sample.approval_status,
          approved_by: sample.approved_by,
          approved_date: sample.approved_date,
        },
        approval_history: approvalHistory,
      }
    } else if (format === 'minimal') {
      responseData = {
        id: sample.id,
        code: sample.code,
        name: sample.name,
        approval_status: sample.approval_status,
        customer: sample.customer ? { id: sample.customer.id, name: sample.customer.name } : null,
        product: sample.product ? { id: sample.product.id, name: sample.product.name } : null,
      }
    }

    const queryTime = Date.now() - startTime
    console.log(`Sample detail retrieved in ${queryTime}ms - ID: ${id}, Format: ${format}`)

    // ✅ ENHANCED RESPONSE WITH METADATA
    const response = include_metadata ? {
      data: responseData,
      meta: {
        queryTime: `${queryTime}ms`,
        format,
        included_relationships: Object.keys(withClause),
        workflow_status: {
          current: sample.approval_status,
          can_modify: sample.approval_status !== 'approved',
          next_actions: approvalWorkflow.next_actions.length,
        },
        data_freshness: {
          last_updated: sample.updated_at || sample.created_at,
          cache_hint: 'no-cache', // Always fetch fresh data for detail views
        }
      }
    } : responseData

    return jsonOk(response)
  } catch (error) {
    const queryTime = Date.now() - startTime
    console.error("Sample detail GET endpoint error:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      sampleId: await params.then(p => p.id).catch(() => 'unknown'),
      companyId: context.companyId,
      userId: context.userId,
      queryTime: `${queryTime}ms`,
      timestamp: new Date().toISOString(),
    })

    if (error instanceof Error) {
      if (error.message.includes('database') || error.message.includes('connection')) {
        return jsonError("Database connection error. Please try again later.", 503)
      }
      if (error.message.includes('timeout')) {
        return jsonError("Query timeout. Please try again with fewer included relationships.", 408)
      }
    }

    return jsonError("An error occurred while fetching the sample details. Please try again.", 500)
  }
})

// 🛡️ SECURE: Multi-tenant PATCH endpoint with comprehensive validation
export const PATCH = withTenantAuth(async function PATCH(req: NextRequest, context, { params }: { params: Promise<{ id: string }> }) {
  const startTime = Date.now()

  try {
    const { id } = await params

    // 🛡️ CRITICAL: Verify sample belongs to current company before updating
    const existingSample = await db.query.samples.findFirst({
      where: and(
        eq(samples.id, id),
        eq(samples.company_id, context.companyId)
      ),
    })

    if (!existingSample) {
      return createForbiddenResponse()
    }

    // ✅ VALIDATE REQUEST BODY
    const validation = await validateRequestBody(req, sampleUpdateSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const validatedData = validation.data

    // ✅ UPDATE SAMPLE WITH VALIDATED DATA
    await db.update(samples)
      .set({
        ...validatedData,
        // Ensure we don't accidentally change company_id
        company_id: context.companyId,
      })
      .where(and(
        eq(samples.id, id),
        eq(samples.company_id, context.companyId)
      ))

    // 🛡️ SECURE: Return updated sample with relationships
    const updatedSample = await db.query.samples.findFirst({
      where: and(
        eq(samples.id, id),
        eq(samples.company_id, context.companyId)
      ),
      with: {
        customer: true,
        product: true,
        supplier: true,
      },
    })

    const queryTime = Date.now() - startTime
    console.log(`Sample updated in ${queryTime}ms - ID: ${id}`)

    return jsonOk(updatedSample)
  } catch (error) {
    const queryTime = Date.now() - startTime
    console.error("Sample PATCH endpoint error:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      sampleId: await params.then(p => p.id).catch(() => 'unknown'),
      companyId: context.companyId,
      userId: context.userId,
      queryTime: `${queryTime}ms`,
      timestamp: new Date().toISOString(),
    })

    if (error instanceof Error && error.message.includes('validation')) {
      return jsonError("Validation error occurred during update", 400)
    }

    return jsonError("An error occurred while updating the sample. Please try again.", 500)
  }
})

// 🛡️ SECURE: Multi-tenant DELETE endpoint with proper isolation
export const DELETE = withTenantAuth(async function DELETE(_: Request, context, { params }: { params: Promise<{ id: string }> }) {
  const startTime = Date.now()

  try {
    const { id } = await params

    // ✅ VALIDATE SAMPLE ID FORMAT
    if (!id || typeof id !== 'string' || id.length < 3) {
      return jsonError("Invalid sample ID format", 400)
    }

    // 🛡️ CRITICAL: Verify sample belongs to current company before deleting
    const existingSample = await db.query.samples.findFirst({
      where: and(
        eq(samples.id, id),
        eq(samples.company_id, context.companyId)
      ),
      columns: { id: true, name: true, code: true }
    })

    if (!existingSample) {
      console.warn("Sample delete attempted on non-existent or unauthorized sample:", {
        sampleId: id,
        companyId: context.companyId,
        userId: context.userId,
        timestamp: new Date().toISOString(),
      })
      return jsonError("Sample not found", 404)
    }

    await db.delete(samples).where(and(
      eq(samples.id, id),
      eq(samples.company_id, context.companyId) // 🛡️ Double-check tenant isolation
    ))

    const queryTime = Date.now() - startTime
    console.log(`Sample deleted in ${queryTime}ms - ID: ${id}, Name: ${existingSample.name}`)

    return new Response(null, { status: 204 })
  } catch (error) {
    const queryTime = Date.now() - startTime
    console.error("Sample DELETE endpoint error:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      sampleId: await params.then(p => p.id).catch(() => 'unknown'),
      companyId: context.companyId,
      userId: context.userId,
      queryTime: `${queryTime}ms`,
      timestamp: new Date().toISOString(),
    })

    return jsonError("An error occurred while deleting the sample. Please try again.", 500)
  }
})
