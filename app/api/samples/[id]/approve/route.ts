import { db } from "@/lib/db"
import { json<PERSON>rror, json<PERSON>k } from "@/lib/api-helpers"
import { samples } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { withTenantAuth, createForbiddenResponse } from "@/lib/tenant-utils"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { sampleApprovalSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { logSuccess, logFailure, AuditAction, AuditResource } from "@/lib/audit"

// ✅ WORKFLOW AUTOMATION HELPER FUNCTIONS

/**
 * Trigger workflow automation based on approval status and flags
 */
async function triggerWorkflowAutomation(
  sample: any,
  approval_status: string,
  options: {
    auto_generate_contract: boolean
    auto_create_quality_check: boolean
    notify_customer: boolean
    approval_reason?: string
    next_steps?: string
    approved_by: string
    context: any
  }
) {
  const actions_triggered = []
  const actions_scheduled = []
  const notifications_sent = []

  try {
    if (approval_status === 'approved') {
      // ✅ PHASE 2 HOOK: Auto-generate sales contract
      if (options.auto_generate_contract && sample.customer_id) {
        actions_scheduled.push({
          action: 'generate_sales_contract',
          sample_id: sample.id,
          customer_id: sample.customer_id,
          product_id: sample.product_id,
          scheduled_for: 'Phase 2 Implementation',
          reason: 'Sample approved with auto-contract flag'
        })
        console.log(`🔄 PHASE 2: Auto-generate sales contract for sample ${sample.id}`)
      }

      // ✅ PHASE 2 HOOK: Create quality inspection requirements
      if (options.auto_create_quality_check) {
        actions_scheduled.push({
          action: 'create_quality_inspection',
          sample_id: sample.id,
          product_id: sample.product_id,
          scheduled_for: 'Phase 2 Implementation',
          reason: 'Sample approved with quality check flag'
        })
        console.log(`🔄 PHASE 2: Create quality inspection for sample ${sample.id}`)
      }

      // ✅ IMMEDIATE ACTION: Customer notification
      if (options.notify_customer && sample.customer) {
        notifications_sent.push({
          type: 'sample_approved',
          recipient: sample.customer.contact_email || sample.customer.name,
          sample_code: sample.code,
          sample_name: sample.name,
          approved_by: options.approved_by,
          approval_reason: options.approval_reason,
          next_steps: options.next_steps || 'Contract generation will follow',
          sent_at: new Date().toISOString()
        })
        actions_triggered.push('customer_notification_sent')
        console.log(`📧 Customer notification sent for approved sample ${sample.id}`)
      }

      // ✅ PHASE 2 HOOK: Update customer relationship status
      actions_scheduled.push({
        action: 'update_customer_relationship',
        customer_id: sample.customer_id,
        relationship_status: 'sample_approved',
        scheduled_for: 'Phase 2 Implementation'
      })

    } else if (approval_status === 'rejected') {
      // ✅ IMMEDIATE ACTION: Rejection notification
      if (options.notify_customer && sample.customer) {
        notifications_sent.push({
          type: 'sample_rejected',
          recipient: sample.customer.contact_email || sample.customer.name,
          sample_code: sample.code,
          sample_name: sample.name,
          rejected_by: options.approved_by,
          rejection_reason: options.approval_reason,
          next_steps: options.next_steps || 'Please review and resubmit if needed',
          sent_at: new Date().toISOString()
        })
        actions_triggered.push('customer_notification_sent')
        console.log(`📧 Customer rejection notification sent for sample ${sample.id}`)
      }

    } else if (approval_status === 'revision_required') {
      // ✅ IMMEDIATE ACTION: Revision request notification
      if (options.notify_customer && sample.customer) {
        notifications_sent.push({
          type: 'sample_revision_required',
          recipient: sample.customer.contact_email || sample.customer.name,
          sample_code: sample.code,
          sample_name: sample.name,
          requested_by: options.approved_by,
          revision_reason: options.approval_reason,
          next_steps: options.next_steps || 'Please make the requested changes and resubmit',
          sent_at: new Date().toISOString()
        })
        actions_triggered.push('customer_notification_sent')
        console.log(`📧 Customer revision request sent for sample ${sample.id}`)
      }
    }

    return {
      actions_triggered,
      actions_scheduled,
      notifications_sent,
      automation_successful: true,
      automation_timestamp: new Date().toISOString()
    }

  } catch (error) {
    console.error('Workflow automation error:', error)
    return {
      actions_triggered,
      actions_scheduled,
      notifications_sent,
      automation_successful: false,
      automation_error: error instanceof Error ? error.message : 'Unknown error',
      automation_timestamp: new Date().toISOString()
    }
  }
}

/**
 * Get next possible actions based on approval status
 */
function getNextPossibleActions(approval_status: string): string[] {
  const actionMap = {
    'pending': ['approve', 'reject', 'request_revision'],
    'approved': ['generate_contract', 'create_work_order', 'schedule_production'],
    'rejected': ['resubmit', 'modify_and_resubmit'],
    'revision_required': ['resubmit', 'approve', 'reject']
  }

  return actionMap[approval_status] || []
}

// 🛡️ SECURE: Multi-tenant PATCH endpoint for enhanced sample approval workflow
export const PATCH = withTenantAuth(async function PATCH(req: NextRequest, context, { params }: { params: Promise<{ id: string }> }) {
  const startTime = Date.now()

  try {
    const { id } = await params

    // ✅ VALIDATE SAMPLE ID FORMAT
    if (!id || typeof id !== 'string' || id.length < 3) {
      return jsonError("Invalid sample ID format", 400)
    }

    // 🛡️ CRITICAL: Verify sample belongs to current company and get full details
    const existingSample = await db.query.samples.findFirst({
      where: and(
        eq(samples.id, id),
        eq(samples.company_id, context.companyId)
      ),
      with: {
        customer: true,
        product: true,
        supplier: true,
      },
    })

    if (!existingSample) {
      console.warn("Sample approval attempted on non-existent or unauthorized sample:", {
        sampleId: id,
        companyId: context.companyId,
        userId: context.userId,
        timestamp: new Date().toISOString(),
      })
      return jsonError("Sample not found", 404)
    }

    // ✅ VALIDATE APPROVAL REQUEST BODY
    const validation = await validateRequestBody(req, sampleApprovalSchema)

    if (!validation.success) {
      console.error("Sample approval validation failed:", {
        sampleId: id,
        errors: validation.error.issues,
        companyId: context.companyId,
        userId: context.userId,
      })
      return createValidationErrorResponse(validation.error.issues)
    }

    const {
      approval_status,
      approved_by,
      approved_date,
      rejection_reason,
      notes,
      approval_reason,
      next_steps,
      priority_change,
      auto_generate_contract,
      auto_create_quality_check,
      notify_customer,
      batch_operation,
      batch_id
    } = validation.data

    // ✅ COMPREHENSIVE WORKFLOW STATE VALIDATION
    const currentStatus = existingSample.approval_status
    const validTransitions = {
      'pending': ['approved', 'rejected', 'revision_required'],
      'revision_required': ['approved', 'rejected', 'pending'],
      'rejected': ['pending', 'revision_required'], // Can resubmit
      'approved': [] // Cannot change approved status
    }

    if (!validTransitions[currentStatus]?.includes(approval_status)) {
      console.warn("Invalid approval status transition attempted:", {
        sampleId: id,
        currentStatus,
        requestedStatus: approval_status,
        companyId: context.companyId,
        userId: context.userId,
      })
      return jsonError(
        `Cannot change approval status from '${currentStatus}' to '${approval_status}'. Valid transitions: ${validTransitions[currentStatus]?.join(', ') || 'none'}`,
        400
      )
    }

    // ✅ BUSINESS RULE VALIDATION
    if (approval_status === 'approved' && currentStatus === 'approved') {
      return jsonError("Sample is already approved", 400)
    }

    if (approval_status === 'rejected' && !rejection_reason) {
      return jsonError("Rejection reason is required for rejected samples", 400)
    }

    // ✅ PREPARE UPDATE DATA WITH ENHANCED FIELDS
    const updateData: any = {
      approval_status,
      approved_by,
      approved_date,
      rejection_reason: approval_status === 'rejected' ? rejection_reason : null,
      notes: notes || existingSample.notes, // Preserve existing notes if not provided
    }

    // ✅ APPLY PRIORITY CHANGE IF REQUESTED
    if (priority_change) {
      updateData.priority = priority_change
    }

    // ✅ UPDATE SAMPLE WITH COMPREHENSIVE DATA
    await db.update(samples)
      .set(updateData)
      .where(and(
        eq(samples.id, id),
        eq(samples.company_id, context.companyId)
      ))

    // ✅ COMPREHENSIVE AUDIT LOGGING
    const auditDetails = {
      sample_id: id,
      sample_code: existingSample.code,
      sample_name: existingSample.name,
      previous_status: currentStatus,
      new_status: approval_status,
      approved_by,
      approval_reason: approval_reason || 'Not provided',
      rejection_reason: rejection_reason || null,
      priority_change: priority_change || null,
      customer_id: existingSample.customer_id,
      customer_name: existingSample.customer?.name || 'Unknown',
      product_id: existingSample.product_id,
      product_name: existingSample.product?.name || 'Unknown',
      batch_operation,
      batch_id,
      workflow_flags: {
        auto_generate_contract,
        auto_create_quality_check,
        notify_customer,
      }
    }

    // ✅ LOG APPROVAL ACTION
    logSuccess(
      { id: context.userId, email: approved_by, role: 'approver' },
      approval_status === 'approved' ? AuditAction.APPROVE : AuditAction.REJECT,
      AuditResource.SAMPLE,
      id,
      auditDetails,
      req.headers.get('x-forwarded-for') || 'unknown',
      req.headers.get('user-agent') || 'unknown'
    )

    // 🛡️ SECURE: Return updated sample with relationships
    const updatedSample = await db.query.samples.findFirst({
      where: and(
        eq(samples.id, id),
        eq(samples.company_id, context.companyId)
      ),
      with: {
        customer: true,
        product: true,
        supplier: true,
      },
    })

    // ✅ WORKFLOW AUTOMATION HOOKS FOR PHASE 2 INTEGRATION
    const workflowResults = await triggerWorkflowAutomation(
      updatedSample,
      approval_status,
      {
        auto_generate_contract,
        auto_create_quality_check,
        notify_customer,
        approval_reason,
        next_steps,
        approved_by,
        context
      }
    )

    const queryTime = Date.now() - startTime
    console.log(`Sample approval processed in ${queryTime}ms - ID: ${id}, Status: ${approval_status}, Workflow: ${workflowResults.actions_triggered.length} actions`)

    // ✅ ENHANCED RESPONSE WITH WORKFLOW INFORMATION
    const response = {
      sample: updatedSample,
      approval_summary: {
        previous_status: currentStatus,
        new_status: approval_status,
        approved_by,
        approved_date,
        approval_reason: approval_reason || 'Not provided',
        rejection_reason: rejection_reason || null,
        priority_changed: priority_change ? `Changed to ${priority_change}` : null,
      },
      workflow_automation: workflowResults,
      next_actions: getNextPossibleActions(approval_status),
      meta: {
        queryTime: `${queryTime}ms`,
        audit_logged: true,
        batch_operation,
        batch_id,
      }
    }

    return jsonOk(response)
  } catch (error) {
    const queryTime = Date.now() - startTime
    const sampleId = await params.then(p => p.id).catch(() => 'unknown')

    // ✅ COMPREHENSIVE ERROR LOGGING
    console.error("Sample approval endpoint error:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      sampleId,
      companyId: context.companyId,
      userId: context.userId,
      queryTime: `${queryTime}ms`,
      timestamp: new Date().toISOString(),
    })

    // ✅ AUDIT LOG FAILURE
    logFailure(
      { id: context.userId, email: 'unknown', role: 'user' },
      AuditAction.APPROVE,
      AuditResource.SAMPLE,
      error instanceof Error ? error.message : 'Unknown error',
      sampleId,
      {
        error_type: error instanceof Error ? error.constructor.name : 'UnknownError',
        queryTime: `${queryTime}ms`,
      },
      req.headers.get('x-forwarded-for') || 'unknown',
      req.headers.get('user-agent') || 'unknown'
    )

    // ✅ SPECIFIC ERROR HANDLING
    if (error instanceof Error) {
      if (error.message.includes('validation')) {
        return jsonError("Invalid approval data provided. Please check your input and try again.", 400)
      }
      if (error.message.includes('database') || error.message.includes('connection')) {
        return jsonError("Database connection error. Please try again later.", 503)
      }
      if (error.message.includes('timeout')) {
        return jsonError("Request timeout. Please try again.", 408)
      }
      if (error.message.includes('constraint') || error.message.includes('foreign key')) {
        return jsonError("Data integrity error. Please ensure all referenced data exists.", 409)
      }
    }

    return jsonError("An error occurred while processing the sample approval. Please try again.", 500)
  }
})
