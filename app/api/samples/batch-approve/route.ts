import { db } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { samples } from "@/lib/schema-postgres"
import { eq, and, inArray } from "drizzle-orm"
import { withTenantAuth, createForbiddenResponse } from "@/lib/tenant-utils"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { sampleBatchApprovalSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { logSuccess, logFailure, AuditAction, AuditResource } from "@/lib/audit"

// 🛡️ SECURE: Multi-tenant PATCH endpoint for batch sample approval
export const PATCH = withTenantAuth(async function PATCH(req: NextRequest, context) {
  const startTime = Date.now()
  
  try {
    // ✅ VALIDATE BATCH APPROVAL REQUEST BODY
    const validation = await validateRequestBody(req, sampleBatchApprovalSchema)
    
    if (!validation.success) {
      console.error("Batch approval validation failed:", {
        errors: validation.error.issues,
        companyId: context.companyId,
        userId: context.userId,
      })
      return createValidationErrorResponse(validation.error.issues)
    }

    const { 
      sample_ids, 
      approval_status, 
      approved_by, 
      approved_date, 
      rejection_reason, 
      notes,
      approval_reason,
      batch_notes,
      auto_generate_contracts,
      notify_customers
    } = validation.data
    
    // 🛡️ CRITICAL: Verify all samples belong to current company
    const existingSamples = await db.query.samples.findMany({
      where: and(
        inArray(samples.id, sample_ids),
        eq(samples.company_id, context.companyId)
      ),
      with: {
        customer: true,
        product: true,
        supplier: true,
      },
    })

    if (existingSamples.length !== sample_ids.length) {
      const foundIds = existingSamples.map(s => s.id)
      const missingIds = sample_ids.filter(id => !foundIds.includes(id))
      
      console.warn("Batch approval attempted on unauthorized samples:", {
        requestedIds: sample_ids,
        foundIds,
        missingIds,
        companyId: context.companyId,
        userId: context.userId,
      })
      
      return jsonError(`Some samples not found or unauthorized: ${missingIds.join(', ')}`, 404)
    }

    // ✅ VALIDATE WORKFLOW STATE TRANSITIONS FOR ALL SAMPLES
    const invalidTransitions = []
    const validTransitions = {
      'pending': ['approved', 'rejected', 'revision_required'],
      'revision_required': ['approved', 'rejected', 'pending'],
      'rejected': ['pending', 'revision_required'],
      'approved': []
    }

    for (const sample of existingSamples) {
      if (!validTransitions[sample.approval_status]?.includes(approval_status)) {
        invalidTransitions.push({
          id: sample.id,
          code: sample.code,
          currentStatus: sample.approval_status,
          requestedStatus: approval_status
        })
      }
    }

    if (invalidTransitions.length > 0) {
      return jsonError(
        `Invalid status transitions detected for samples: ${invalidTransitions.map(t => `${t.code} (${t.currentStatus} → ${t.requestedStatus})`).join(', ')}`,
        400
      )
    }

    // ✅ PERFORM BATCH UPDATE
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substring(2)}`
    
    await db.update(samples)
      .set({
        approval_status,
        approved_by,
        approved_date,
        rejection_reason: approval_status === 'rejected' ? rejection_reason : null,
        notes: batch_notes || notes,
      })
      .where(and(
        inArray(samples.id, sample_ids),
        eq(samples.company_id, context.companyId)
      ))

    // ✅ FETCH UPDATED SAMPLES
    const updatedSamples = await db.query.samples.findMany({
      where: and(
        inArray(samples.id, sample_ids),
        eq(samples.company_id, context.companyId)
      ),
      with: {
        customer: true,
        product: true,
        supplier: true,
      },
    })

    // ✅ COMPREHENSIVE BATCH AUDIT LOGGING
    const auditDetails = {
      batch_id: batchId,
      sample_count: updatedSamples.length,
      approval_status,
      approved_by,
      approval_reason: approval_reason || 'Batch operation',
      rejection_reason: rejection_reason || null,
      samples: updatedSamples.map(s => ({
        id: s.id,
        code: s.code,
        name: s.name,
        customer_name: s.customer?.name || 'Unknown',
        product_name: s.product?.name || 'Unknown',
      })),
      workflow_flags: {
        auto_generate_contracts,
        notify_customers,
      }
    }

    logSuccess(
      { id: context.userId, email: approved_by, role: 'approver' },
      approval_status === 'approved' ? AuditAction.APPROVE : AuditAction.REJECT,
      AuditResource.SAMPLE,
      batchId,
      auditDetails,
      req.headers.get('x-forwarded-for') || 'unknown',
      req.headers.get('user-agent') || 'unknown'
    )

    // ✅ BATCH WORKFLOW AUTOMATION
    const workflowResults = []
    for (const sample of updatedSamples) {
      if (approval_status === 'approved' && auto_generate_contracts && sample.customer_id) {
        workflowResults.push({
          sample_id: sample.id,
          action: 'contract_generation_scheduled',
          scheduled_for: 'Phase 2 Implementation'
        })
      }
      
      if (notify_customers && sample.customer) {
        workflowResults.push({
          sample_id: sample.id,
          action: 'customer_notification_sent',
          recipient: sample.customer.contact_email || sample.customer.name
        })
      }
    }

    const queryTime = Date.now() - startTime
    console.log(`Batch approval processed in ${queryTime}ms - ${updatedSamples.length} samples, Status: ${approval_status}`)

    // ✅ COMPREHENSIVE BATCH RESPONSE
    const response = {
      batch_summary: {
        batch_id: batchId,
        samples_processed: updatedSamples.length,
        approval_status,
        approved_by,
        approved_date,
        approval_reason: approval_reason || 'Batch operation',
        rejection_reason: rejection_reason || null,
      },
      samples: updatedSamples,
      workflow_automation: {
        actions_triggered: workflowResults.length,
        results: workflowResults,
      },
      meta: {
        queryTime: `${queryTime}ms`,
        audit_logged: true,
        batch_operation: true,
      }
    }

    return jsonOk(response)

  } catch (error) {
    const queryTime = Date.now() - startTime
    
    console.error("Batch approval endpoint error:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      companyId: context.companyId,
      userId: context.userId,
      queryTime: `${queryTime}ms`,
      timestamp: new Date().toISOString(),
    })
    
    logFailure(
      { id: context.userId, email: 'unknown', role: 'user' },
      AuditAction.APPROVE,
      AuditResource.SAMPLE,
      error instanceof Error ? error.message : 'Unknown error',
      'batch_operation',
      {
        error_type: error instanceof Error ? error.constructor.name : 'UnknownError',
        queryTime: `${queryTime}ms`,
      },
      req.headers.get('x-forwarded-for') || 'unknown',
      req.headers.get('user-agent') || 'unknown'
    )
    
    if (error instanceof Error && error.message.includes('validation')) {
      return jsonError("Invalid batch approval data provided", 400)
    }
    
    return jsonError("An error occurred during batch approval. Please try again.", 500)
  }
})
