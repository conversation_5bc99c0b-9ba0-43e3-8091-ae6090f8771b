import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { samples, customers, products, suppliers } from "@/lib/schema-postgres"
import { desc, asc, eq, and, like, or, gte, lte, count, sql } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { sampleSchema, sampleQuerySchema } from "@/lib/validations"
import { NextRequest } from "next/server"

// 🛡️ SECURE: Multi-tenant GET endpoint with advanced filtering and relationships
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  const startTime = Date.now()

  // ✅ EXTRACT QUERY PARAMS OUTSIDE TRY BLOCK FOR ERROR LOGGING
  const url = new URL(request.url)
  const queryParams = Object.fromEntries(url.searchParams.entries())

  try {
    // ✅ VALIDATE QUERY PARAMETERS
    const validation = sampleQuerySchema.safeParse(queryParams)
    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const {
      page = 1,
      limit = 50,
      search,
      sample_type,
      approval_status,
      status,
      sample_direction,
      customer_id,
      product_id,
      supplier_id,
      priority,
      sort_by = "created_at",
      sort_order = "desc",
      date_from,
      date_to,
    } = validation.data

    // ✅ BUILD SECURE WHERE CONDITIONS
    const conditions = [eq(samples.company_id, context.companyId)]

    // ✅ SEARCH CONDITIONS
    if (search) {
      conditions.push(
        or(
          like(samples.name, `%${search}%`),
          like(samples.code, `%${search}%`),
          like(samples.notes, `%${search}%`)
        )!
      )
    }

    // ✅ FILTER CONDITIONS
    if (sample_type) conditions.push(eq(samples.sample_type, sample_type))
    if (approval_status) conditions.push(eq(samples.approval_status, approval_status))
    if (status) conditions.push(eq(samples.status, status))
    if (sample_direction) conditions.push(eq(samples.sample_direction, sample_direction))
    if (customer_id) conditions.push(eq(samples.customer_id, customer_id))
    if (product_id) conditions.push(eq(samples.product_id, product_id))
    if (supplier_id) conditions.push(eq(samples.supplier_id, supplier_id))
    if (priority) conditions.push(eq(samples.priority, priority))

    // ✅ DATE RANGE CONDITIONS
    if (date_from) conditions.push(gte(samples.date, date_from))
    if (date_to) conditions.push(lte(samples.date, date_to))

    // ✅ BUILD ORDER BY
    const orderColumn = sort_by === "created_at" ? samples.created_at :
      sort_by === "date" ? samples.date :
        sort_by === "name" ? samples.name :
          sort_by === "code" ? samples.code :
            samples.created_at

    const orderDirection = sort_order === "asc" ? asc : desc

    // ✅ EXECUTE QUERIES WITH RELATIONSHIPS
    const offset = (page - 1) * limit
    const whereCondition = and(...conditions)

    const [rows, totalResult] = await Promise.all([
      db.query.samples.findMany({
        where: whereCondition,
        limit,
        offset,
        orderBy: orderDirection(orderColumn),
        with: {
          customer: {
            columns: { id: true, name: true, email: true }
          },
          product: {
            columns: { id: true, name: true, code: true }
          },
          supplier: {
            columns: { id: true, name: true, email: true }
          },
        },
      }),
      db.select({ count: count() }).from(samples).where(whereCondition),
    ])

    const total = totalResult[0]?.count || 0
    const totalPages = Math.ceil(total / limit)

    // ✅ PERFORMANCE LOGGING
    const duration = Date.now() - startTime
    console.log(`GET /api/samples completed in ${duration}ms - ${rows.length} rows, ${total} total`)

    return jsonOk({
      data: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      performance: {
        duration,
        query_count: 2,
      },
    })
  } catch (error) {
    console.error("GET /api/samples error:", error)
    console.error("Query params:", queryParams)
    return jsonError(error)
  }
})

// 🛡️ PRODUCTION-READY SECURE ENDPOINT
export const POST = withTenantAuth(async function POST(req: NextRequest, context) {
  console.log("🚀 POST /api/samples - Starting sample creation")
  console.log("🔍 Context:", { companyId: context.companyId, userId: context.userId })

  try {
    // ✅ VALIDATE REQUEST BODY (SAME PATTERN AS WORKING MODULES)
    const validation = await validateRequestBody(req, sampleSchema)

    if (!validation.success) {
      console.error("❌ Sample validation failed:")
      console.error("Validation errors:", JSON.stringify(validation.error.issues, null, 2))
      return createValidationErrorResponse(validation.error.issues)
    }

    const validatedData = validation.data
    console.log("✅ Sample validation successful:", JSON.stringify(validatedData, null, 2))
    const id = uid("smp")

    // ✅ CREATE SAMPLE OBJECT WITH ALL BIDIRECTIONAL FIELDS (CONVERT EMPTY STRINGS TO NULL)
    const newSample = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      code: validatedData.code,
      name: validatedData.name,
      date: validatedData.date,
      sample_type: validatedData.sample_type || "development",
      approval_status: validatedData.approval_status || "pending",
      notes: validatedData.notes || null,
      status: validatedData.status || "active",

      // ✅ BIDIRECTIONAL WORKFLOW FIELDS
      sample_direction: validatedData.sample_direction || "outbound",
      sample_purpose: validatedData.sample_purpose || null,
      sender_type: validatedData.sender_type || null,
      receiver_type: validatedData.receiver_type || null,

      // ✅ INBOUND SAMPLE FIELDS
      received_date: validatedData.received_date || null,
      testing_status: validatedData.testing_status || "not_started",
      testing_results: validatedData.testing_results || null,
      quote_requested: validatedData.quote_requested || false,
      quote_provided: validatedData.quote_provided || false,

      // ✅ RELATIONSHIP FIELDS (CONVERT EMPTY STRINGS TO NULL)
      customer_id: validatedData.customer_id && validatedData.customer_id !== "" ? validatedData.customer_id : null,
      product_id: validatedData.product_id && validatedData.product_id !== "" ? validatedData.product_id : null,
      supplier_id: validatedData.supplier_id && validatedData.supplier_id !== "" ? validatedData.supplier_id : null,

      // ✅ PRODUCT FIELDS (CONVERT EMPTY STRINGS TO NULL)
      quantity: validatedData.quantity && validatedData.quantity !== "" ? validatedData.quantity : null,
      unit: validatedData.unit && validatedData.unit !== "" ? validatedData.unit : null,
      specifications: validatedData.specifications && validatedData.specifications !== "" ? validatedData.specifications : null,
      quality_requirements: validatedData.quality_requirements && validatedData.quality_requirements !== "" ? validatedData.quality_requirements : null,
      delivery_date: validatedData.delivery_date && validatedData.delivery_date !== "" ? validatedData.delivery_date : null,
      priority: validatedData.priority || "normal",

      // ✅ COMMERCIAL FIELDS (CONVERT EMPTY STRINGS TO NULL)
      cost: validatedData.cost && validatedData.cost !== "" ? validatedData.cost : null,
      currency: validatedData.currency || "USD",
    }

    console.log("💾 Inserting sample into database:", JSON.stringify(newSample, null, 2))

    await db.insert(samples).values(newSample)

    // 🛡️ SECURE: Return sample with relationships
    const row = await db.query.samples.findFirst({
      where: and(
        eq(samples.id, id),
        eq(samples.company_id, context.companyId)
      ),
      with: {
        customer: true,
        product: true,
        supplier: true,
      },
    })

    console.log("✅ Sample created successfully:", row?.id)
    return jsonOk(row, { status: 201 })
  } catch (e) {
    console.error("❌ POST /api/samples - Error:", e)
    console.error("❌ Error stack:", e instanceof Error ? e.stack : 'No stack trace')
    return jsonError(e)
  }
})
