import { db } from "@/lib/db"
import { json<PERSON>rror, json<PERSON>k } from "@/lib/api-helpers"
import { samples } from "@/lib/schema-postgres"
import { eq, and, inArray } from "drizzle-orm"
import { withTenantAuth, createForbiddenResponse } from "@/lib/tenant-utils"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { sampleBulkUpdateSchema, sampleBulkDeleteSchema } from "@/lib/validations"
import { NextRequest } from "next/server"

// 🛡️ SECURE: Multi-tenant PATCH endpoint for bulk sample updates
export const PATCH = withTenantAuth(async function PATCH(req: NextRequest, context) {
  const startTime = Date.now()
  
  try {
    // ✅ VALIDATE REQUEST BODY
    const validation = await validateRequestBody(req, sampleBulkUpdateSchema)
    
    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const { ids, updates } = validation.data
    
    // ✅ SECURITY: Verify all samples belong to current company
    const existingSamples = await db.query.samples.findMany({
      where: and(
        inArray(samples.id, ids),
        eq(samples.company_id, context.companyId)
      ),
      columns: { id: true, name: true, code: true }
    })

    if (existingSamples.length !== ids.length) {
      console.warn("Bulk update attempted on unauthorized samples:", {
        requestedIds: ids,
        foundIds: existingSamples.map(s => s.id),
        companyId: context.companyId,
        userId: context.userId,
      })
      return createForbiddenResponse()
    }

    // ✅ PERFORM BULK UPDATE
    const updateResult = await db
      .update(samples)
      .set({
        ...updates,
        // Ensure we don't accidentally change company_id
        company_id: context.companyId,
      })
      .where(and(
        inArray(samples.id, ids),
        eq(samples.company_id, context.companyId)
      ))

    // ✅ FETCH UPDATED SAMPLES WITH RELATIONSHIPS
    const updatedSamples = await db.query.samples.findMany({
      where: and(
        inArray(samples.id, ids),
        eq(samples.company_id, context.companyId)
      ),
      with: {
        customer: true,
        product: true,
        supplier: true,
      },
    })

    const queryTime = Date.now() - startTime
    console.log(`Bulk update completed in ${queryTime}ms - ${updatedSamples.length} samples updated`)

    return jsonOk({
      message: `Successfully updated ${updatedSamples.length} samples`,
      data: updatedSamples,
      meta: {
        updated_count: updatedSamples.length,
        queryTime: `${queryTime}ms`,
        updates_applied: updates,
      }
    })

  } catch (error) {
    const queryTime = Date.now() - startTime
    console.error("Samples bulk update error:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      companyId: context.companyId,
      userId: context.userId,
      queryTime: `${queryTime}ms`,
      timestamp: new Date().toISOString(),
    })

    return jsonError("An error occurred during bulk update. Please try again.", 500)
  }
})

// 🛡️ SECURE: Multi-tenant DELETE endpoint for bulk sample deletion
export const DELETE = withTenantAuth(async function DELETE(req: NextRequest, context) {
  const startTime = Date.now()
  
  try {
    // ✅ VALIDATE REQUEST BODY
    const validation = await validateRequestBody(req, sampleBulkDeleteSchema)
    
    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const { ids, confirm } = validation.data
    
    if (!confirm) {
      return jsonError("Confirmation is required for bulk delete operations", 400)
    }
    
    // ✅ SECURITY: Verify all samples belong to current company
    const existingSamples = await db.query.samples.findMany({
      where: and(
        inArray(samples.id, ids),
        eq(samples.company_id, context.companyId)
      ),
      columns: { id: true, name: true, code: true }
    })

    if (existingSamples.length !== ids.length) {
      console.warn("Bulk delete attempted on unauthorized samples:", {
        requestedIds: ids,
        foundIds: existingSamples.map(s => s.id),
        companyId: context.companyId,
        userId: context.userId,
      })
      return createForbiddenResponse()
    }

    // ✅ PERFORM BULK DELETE
    await db
      .delete(samples)
      .where(and(
        inArray(samples.id, ids),
        eq(samples.company_id, context.companyId)
      ))

    const queryTime = Date.now() - startTime
    console.log(`Bulk delete completed in ${queryTime}ms - ${existingSamples.length} samples deleted`)

    return jsonOk({
      message: `Successfully deleted ${existingSamples.length} samples`,
      meta: {
        deleted_count: existingSamples.length,
        deleted_samples: existingSamples.map(s => ({ id: s.id, name: s.name, code: s.code })),
        queryTime: `${queryTime}ms`,
      }
    })

  } catch (error) {
    const queryTime = Date.now() - startTime
    console.error("Samples bulk delete error:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      companyId: context.companyId,
      userId: context.userId,
      queryTime: `${queryTime}ms`,
      timestamp: new Date().toISOString(),
    })

    return jsonError("An error occurred during bulk delete. Please try again.", 500)
  }
})
