import { db } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { stockLots } from "@/lib/schema-postgres"
import { asc, eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(req: Request, context) {
  try {
    const url = new URL(req.url)
    const qualityStatus = url.searchParams.get("quality_status")
    const location = url.searchParams.get("location")
    const productId = url.searchParams.get("product_id")

    // Build where conditions with tenant isolation
    const whereConditions = [eq(stockLots.company_id, context.companyId)] // 🛡️ CRITICAL: Tenant filtering
    if (qualityStatus) {
      whereConditions.push(eq(stockLots.quality_status, qualityStatus))
    }
    if (location) {
      whereConditions.push(eq(stockLots.location, location))
    }
    if (productId) {
      whereConditions.push(eq(stockLots.product_id, productId))
    }

    const whereClause = and(...whereConditions)

    const rows = await db.query.stockLots.findMany({
      where: whereClause,
      orderBy: [asc(stockLots.created_at)],
      with: {
        product: true,
        workOrder: true, // Direct work order relationship
        inspection: {
          with: {
            workOrder: true,
          },
        },
      },
    })
    return createSuccessResponse(rows)
  } catch (error) {
    return createErrorResponse(error)
  }
})
