import { db } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { stockTxns } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation and workflow filtering
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const url = new URL(request.url)
    const workflowTrigger = url.searchParams.get("workflow_trigger")
    const type = url.searchParams.get("type")
    const location = url.searchParams.get("location")

    // Build where conditions with tenant isolation
    const whereConditions = [eq(stockTxns.company_id, context.companyId)] // 🛡️ CRITICAL: Tenant filtering
    if (workflowTrigger) {
      whereConditions.push(eq(stockTxns.workflow_trigger, workflowTrigger))
    }
    if (type) {
      whereConditions.push(eq(stockTxns.type, type))
    }
    if (location) {
      whereConditions.push(eq(stockTxns.location, location))
    }

    const whereClause = whereConditions.length > 1 ? and(...whereConditions) : whereConditions[0]

    const rows = await db.query.stockTxns.findMany({
      where: whereClause,
      orderBy: [desc(stockTxns.created_at)],
      with: {
        product: true,
      },
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
})
