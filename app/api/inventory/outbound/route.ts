import { db, uid } from "@/lib/db"
import { createErrorResponse, createSuccessResponse } from "@/lib/api-helpers"
import { stockLots, stockTxns } from "@/lib/schema-postgres"
import { and, asc, eq, sql } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { outboundSchema } from "@/lib/validations"
import { NextRequest } from "next/server"

export async function POST(req: NextRequest) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, outboundSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const { product_id, qty, location, ref } = validation.data

    await db.transaction(async (tx) => {
      // Only get approved stock lots for outbound operations
      const lots = await tx.query.stockLots.findMany({
        where: and(
          eq(stockLots.product_id, product_id),
          eq(stockLots.location, location),
          eq(stockLots.quality_status, "approved") // Only approved stock can be shipped
        ),
        orderBy: [asc(stockLots.created_at)],
      })

      // Check total approved stock available
      const totalApprovedStock = lots.reduce((sum, lot) => sum + Number(lot.qty), 0)

      if (totalApprovedStock < Number(qty)) {
        // Check if there's pending/rejected stock that could explain the shortage
        const allLots = await tx.query.stockLots.findMany({
          where: and(eq(stockLots.product_id, product_id), eq(stockLots.location, location)),
        })

        const pendingStock = allLots
          .filter(lot => lot.quality_status === "pending")
          .reduce((sum, lot) => sum + Number(lot.qty), 0)

        const rejectedStock = allLots
          .filter(lot => lot.quality_status === "rejected")
          .reduce((sum, lot) => sum + Number(lot.qty), 0)

        const { createInsufficientStockError } = await import("@/lib/errors")
        const errorMessage = pendingStock > 0 || rejectedStock > 0
          ? `Insufficient approved stock. Available: ${totalApprovedStock}, Pending: ${pendingStock}, Rejected: ${rejectedStock}`
          : `Insufficient approved stock. Available: ${totalApprovedStock}`

        throw createInsufficientStockError("Approved Product", totalApprovedStock, Number(qty))
      }

      let remaining = Number(qty)
      for (const lot of lots) {
        if (remaining <= 0) break
        const lotQty = Number(lot.qty)
        const use = Math.min(lotQty, remaining)
        const newQty = lotQty - use

        if (newQty <= 0) {
          await tx.delete(stockLots).where(eq(stockLots.id, lot.id))
        } else {
          await tx
            .update(stockLots)
            .set({ qty: newQty.toString() })
            .where(eq(stockLots.id, lot.id))
        }
        remaining -= use
      }

      await tx.insert(stockTxns).values({
        id: uid("txn"),
        type: "outbound",
        product_id: product_id,
        qty: qty.toString(),
        location: location,
        ref: ref,
      })
    })

    return createSuccessResponse(null, "Stock updated successfully", 201)
  } catch (e) {
    return createErrorResponse(e)
  }
}
