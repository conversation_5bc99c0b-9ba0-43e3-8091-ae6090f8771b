import { db } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { inventoryTransactions, stockLots } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"
import { inventoryTransactionSchema, TransactionStatus } from "@/lib/validations"
import { z } from "zod"

/**
 * Manufacturing ERP - Individual Inventory Transaction API
 * 
 * Handles individual transaction operations:
 * - GET: Retrieve single transaction with full details
 * - PATCH: Update transaction (status, notes, approval)
 * - DELETE: Soft delete transaction (cancel status)
 */

// 🛡️ SECURE: Multi-tenant GET endpoint for individual transaction
export const GET = withTenantAuth(async function GET(
  request, 
  context, 
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    const transaction = await db.query.inventoryTransactions.findFirst({
      where: and(
        eq(inventoryTransactions.id, id),
        eq(inventoryTransactions.company_id, context.companyId) // 🛡️ CRITICAL: Tenant validation
      ),
      with: {
        stockLot: {
          with: {
            product: true,
            workOrder: true,
            inspection: true,
          },
        },
      },
    })

    if (!transaction) {
      return jsonError("Transaction not found", 404)
    }

    return jsonOk(transaction)
  } catch (error) {
    console.error("GET /api/inventory/transactions/[id] error:", error)
    return jsonError("Failed to fetch transaction", 500)
  }
})

// 🛡️ SECURE: Multi-tenant PATCH endpoint for updating transactions
export const PATCH = withTenantAuth(async function PATCH(
  request, 
  context, 
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    // Verify transaction exists and belongs to company
    const existingTransaction = await db.query.inventoryTransactions.findFirst({
      where: and(
        eq(inventoryTransactions.id, id),
        eq(inventoryTransactions.company_id, context.companyId) // 🛡️ CRITICAL: Tenant validation
      ),
    })

    if (!existingTransaction) {
      return jsonError("Transaction not found", 404)
    }

    // Validate partial update data
    const partialSchema = inventoryTransactionSchema.partial()
    const validatedData = partialSchema.parse(body)

    // Prepare update data
    const updateData: any = {
      updated_at: new Date(),
    }

    // Handle status updates
    if (validatedData.transactionStatus) {
      updateData.transaction_status = validatedData.transactionStatus
      
      // Set approval fields for completed transactions
      if (validatedData.transactionStatus === TransactionStatus.COMPLETED) {
        updateData.approved_by = context.userId
        updateData.approved_at = new Date()
      }
    }

    // Handle other field updates
    if (validatedData.notes !== undefined) {
      updateData.notes = validatedData.notes
    }
    if (validatedData.unitCost !== undefined) {
      updateData.unit_cost = validatedData.unitCost?.toString()
      // Recalculate total cost if quantity exists
      if (existingTransaction.quantity) {
        const quantity = parseFloat(existingTransaction.quantity)
        updateData.total_cost = validatedData.unitCost 
          ? (validatedData.unitCost * Math.abs(quantity)).toString()
          : null
      }
    }
    if (validatedData.locationFrom !== undefined) {
      updateData.location_from = validatedData.locationFrom
    }
    if (validatedData.locationTo !== undefined) {
      updateData.location_to = validatedData.locationTo
    }
    if (validatedData.referenceNumber !== undefined) {
      updateData.reference_number = validatedData.referenceNumber
    }

    // Update transaction
    const [updatedTransaction] = await db.update(inventoryTransactions)
      .set(updateData)
      .where(and(
        eq(inventoryTransactions.id, id),
        eq(inventoryTransactions.company_id, context.companyId) // 🛡️ CRITICAL: Tenant validation
      ))
      .returning()

    // Fetch complete updated transaction
    const completeTransaction = await db.query.inventoryTransactions.findFirst({
      where: eq(inventoryTransactions.id, updatedTransaction.id),
      with: {
        stockLot: {
          with: {
            product: true,
          },
        },
      },
    })

    return jsonOk(completeTransaction)
  } catch (error) {
    console.error("PATCH /api/inventory/transactions/[id] error:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }
    
    return jsonError("Failed to update transaction", 500)
  }
})

// 🛡️ SECURE: Multi-tenant DELETE endpoint (soft delete - cancel transaction)
export const DELETE = withTenantAuth(async function DELETE(
  request, 
  context, 
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Verify transaction exists and belongs to company
    const existingTransaction = await db.query.inventoryTransactions.findFirst({
      where: and(
        eq(inventoryTransactions.id, id),
        eq(inventoryTransactions.company_id, context.companyId) // 🛡️ CRITICAL: Tenant validation
      ),
    })

    if (!existingTransaction) {
      return jsonError("Transaction not found", 404)
    }

    // Check if transaction can be cancelled
    if (existingTransaction.transaction_status === TransactionStatus.COMPLETED) {
      return jsonError("Cannot cancel completed transaction", 400)
    }

    // Soft delete by setting status to CANCELLED
    const [cancelledTransaction] = await db.update(inventoryTransactions)
      .set({
        transaction_status: TransactionStatus.CANCELLED,
        updated_at: new Date(),
        notes: existingTransaction.notes 
          ? `${existingTransaction.notes}\n\nCancelled by user on ${new Date().toISOString()}`
          : `Cancelled by user on ${new Date().toISOString()}`
      })
      .where(and(
        eq(inventoryTransactions.id, id),
        eq(inventoryTransactions.company_id, context.companyId) // 🛡️ CRITICAL: Tenant validation
      ))
      .returning()

    return jsonOk({ 
      message: "Transaction cancelled successfully",
      transaction: cancelledTransaction 
    })
  } catch (error) {
    console.error("DELETE /api/inventory/transactions/[id] error:", error)
    return jsonError("Failed to cancel transaction", 500)
  }
})
