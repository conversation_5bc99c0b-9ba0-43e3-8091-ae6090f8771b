import { db, uid } from "@/lib/db"
import { json<PERSON><PERSON>r, jsonOk } from "@/lib/api-helpers"
import { inventoryTransactions, stockLots } from "@/lib/schema-postgres"
import { desc, eq, and, like, gte, lte } from "drizzle-orm"
import { withTenantAuth } from "@/lib/tenant-utils"
import { inventoryTransactionSchema, TransactionType, TransactionStatus } from "@/lib/validations"
import { z } from "zod"

/**
 * Manufacturing ERP - Comprehensive Inventory Transactions API
 * 
 * Handles CRUD operations for inventory transactions with:
 * - Multi-tenant security isolation
 * - Comprehensive filtering and search
 * - Transaction type validation
 * - Audit trail logging
 * - Integration with existing workflow systems
 */

// 🛡️ SECURE: Multi-tenant GET endpoint with comprehensive filtering
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const url = new URL(request.url)
    
    // Extract query parameters for filtering
    const transactionType = url.searchParams.get("transaction_type")
    const transactionStatus = url.searchParams.get("transaction_status")
    const referenceType = url.searchParams.get("reference_type")
    const referenceId = url.searchParams.get("reference_id")
    const stockLotId = url.searchParams.get("stock_lot_id")
    const locationFrom = url.searchParams.get("location_from")
    const locationTo = url.searchParams.get("location_to")
    const search = url.searchParams.get("search")
    const dateFrom = url.searchParams.get("date_from")
    const dateTo = url.searchParams.get("date_to")
    const limit = parseInt(url.searchParams.get("limit") || "50")
    const offset = parseInt(url.searchParams.get("offset") || "0")

    // Build where conditions with tenant isolation
    const whereConditions = [eq(inventoryTransactions.company_id, context.companyId)] // 🛡️ CRITICAL: Tenant filtering

    // Add filtering conditions
    if (transactionType) {
      whereConditions.push(eq(inventoryTransactions.transaction_type, transactionType))
    }
    if (transactionStatus) {
      whereConditions.push(eq(inventoryTransactions.transaction_status, transactionStatus))
    }
    if (referenceType) {
      whereConditions.push(eq(inventoryTransactions.reference_type, referenceType))
    }
    if (referenceId) {
      whereConditions.push(eq(inventoryTransactions.reference_id, referenceId))
    }
    if (stockLotId) {
      whereConditions.push(eq(inventoryTransactions.stock_lot_id, stockLotId))
    }
    if (locationFrom) {
      whereConditions.push(eq(inventoryTransactions.location_from, locationFrom))
    }
    if (locationTo) {
      whereConditions.push(eq(inventoryTransactions.location_to, locationTo))
    }
    if (search) {
      whereConditions.push(
        like(inventoryTransactions.transaction_number, `%${search}%`)
      )
    }
    if (dateFrom) {
      whereConditions.push(gte(inventoryTransactions.transaction_date, new Date(dateFrom)))
    }
    if (dateTo) {
      whereConditions.push(lte(inventoryTransactions.transaction_date, new Date(dateTo)))
    }

    const whereClause = whereConditions.length > 1 ? and(...whereConditions) : whereConditions[0]

    // Execute query with relationships
    const transactions = await db.query.inventoryTransactions.findMany({
      where: whereClause,
      orderBy: [desc(inventoryTransactions.created_at)],
      limit: Math.min(limit, 100), // Cap at 100 for performance
      offset,
      with: {
        stockLot: {
          with: {
            product: true,
          },
        },
      },
    })

    // Get total count for pagination
    const totalCount = await db.query.inventoryTransactions.findMany({
      where: whereClause,
    }).then(rows => rows.length)

    return jsonOk({
      transactions,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: totalCount > offset + limit
      }
    })
  } catch (error) {
    console.error("GET /api/inventory/transactions error:", error)
    return jsonError("Failed to fetch inventory transactions", 500)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint for creating transactions
export const POST = withTenantAuth(async function POST(request, context) {
  try {
    const body = await request.json()
    
    // Validate input data
    const validatedData = inventoryTransactionSchema.parse(body)
    
    // Generate unique transaction number
    const transactionNumber = `TXN-${Date.now()}-${uid().slice(0, 6).toUpperCase()}`
    
    // Verify stock lot exists and belongs to company
    const stockLot = await db.query.stockLots.findFirst({
      where: and(
        eq(stockLots.id, validatedData.stockLotId),
        eq(stockLots.company_id, context.companyId) // 🛡️ CRITICAL: Tenant validation
      ),
    })
    
    if (!stockLot) {
      return jsonError("Stock lot not found or access denied", 404)
    }

    // Calculate total cost if unit cost provided
    const totalCost = validatedData.unitCost 
      ? (validatedData.unitCost * Math.abs(validatedData.quantity)).toString()
      : undefined

    // Create transaction record
    const transactionData = {
      id: uid(),
      company_id: context.companyId,
      transaction_number: transactionNumber,
      transaction_type: validatedData.transactionType,
      transaction_status: validatedData.transactionStatus || TransactionStatus.PENDING,
      stock_lot_id: validatedData.stockLotId,
      quantity: validatedData.quantity.toString(),
      unit_cost: validatedData.unitCost?.toString(),
      total_cost: totalCost,
      location_from: validatedData.locationFrom,
      location_to: validatedData.locationTo,
      reference_type: validatedData.referenceType,
      reference_id: validatedData.referenceId,
      reference_number: validatedData.referenceNumber,
      created_by: context.userId,
      notes: validatedData.notes,
      transaction_date: validatedData.transactionDate 
        ? new Date(validatedData.transactionDate)
        : new Date(),
    }

    const [newTransaction] = await db.insert(inventoryTransactions)
      .values(transactionData)
      .returning()

    // Fetch the complete transaction with relationships
    const completeTransaction = await db.query.inventoryTransactions.findFirst({
      where: eq(inventoryTransactions.id, newTransaction.id),
      with: {
        stockLot: {
          with: {
            product: true,
          },
        },
      },
    })

    return jsonOk(completeTransaction, { status: 201 })
  } catch (error) {
    console.error("POST /api/inventory/transactions error:", error)
    
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }
    
    return jsonError("Failed to create inventory transaction", 500)
  }
})
