import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { declarations, declarationItems } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { declarationSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request, context) {
  try {
    const rows = await db.query.declarations.findMany({
      where: eq(declarations.company_id, context.companyId),
      orderBy: [desc(declarations.created_at)],
      with: {
        items: {
          with: {
            product: true,
          },
        },
      },
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: NextRequest, context) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, declarationSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    const id = uid("decl")

    const newDeclaration = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      number: body.number,
      status: body.status || "draft",
    }
    const items = body.items.map((item) => ({
      id: uid("dcli"),
      declarationId: id,
      productId: item.productId,
      qty: item.qty.toString(),
      hs_code: item.hsCode,
    }))

    await db.transaction(async (tx) => {
      await tx.insert(declarations).values(newDeclaration)
      if (items.length > 0) {
        await tx.insert(declarationItems).values(items)
      }
    })

    // 🛡️ SECURE: Only return declaration if it belongs to current company
    const row = await db.query.declarations.findFirst({
      where: and(
        eq(declarations.id, id),
        eq(declarations.company_id, context.companyId)
      ),
      with: {
        items: {
          with: {
            product: true,
          },
        },
      },
    })
    return jsonOk(row, { status: 201 })
  } catch (e) {
    return jsonError(e)
  }
})
