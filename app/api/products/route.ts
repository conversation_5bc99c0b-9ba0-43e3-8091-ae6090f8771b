import { db, uid } from "@/lib/db"
import { jsonError, jsonOk } from "@/lib/api-helpers"
import { products } from "@/lib/schema-postgres"
import { desc, eq, and } from "drizzle-orm"
import { validateRequestBody, createValidationErrorResponse } from "@/lib/validation-middleware"
import { productSchema } from "@/lib/validations"
import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"

// 🛡️ SECURE: Multi-tenant GET endpoint with proper isolation
export const GET = withTenantAuth(async function GET(request: NextRequest, context) {
  try {
    const rows = await db.query.products.findMany({
      where: eq(products.company_id, context.companyId),
      orderBy: [desc(products.created_at)],
    })
    return jsonOk(rows)
  } catch (e) {
    return jsonError(e)
  }
})

// 🛡️ SECURE: Multi-tenant POST endpoint with proper isolation
export const POST = withTenantAuth(async function POST(req: NextRequest, context) {
  try {
    // Validate request body
    const validation = await validateRequestBody(req, productSchema)

    if (!validation.success) {
      return createValidationErrorResponse(validation.error.issues)
    }

    const body = validation.data
    const id = uid("prod")

    // Map validated data to database schema with company_id for tenant isolation
    const newProduct = {
      id,
      company_id: context.companyId, // 🛡️ CRITICAL: Associate with current company
      sku: body.sku,
      name: body.name,
      unit: body.unit,
      hs_code: body.hsCode,
      origin: body.origin,
      package: body.package,
      image: body.image,
      // Quality Requirements
      inspection_required: body.inspection_required || "false",
      quality_tolerance: body.quality_tolerance,
      quality_notes: body.quality_notes,
    }

    await db.insert(products).values(newProduct)

    // 🛡️ SECURE: Only return product if it belongs to current company
    const row = await db.query.products.findFirst({
      where: and(
        eq(products.id, id),
        eq(products.company_id, context.companyId)
      ),
    })
    return jsonOk(row, { status: 201 })
  } catch (e) {
    return jsonError(e)
  }
})
