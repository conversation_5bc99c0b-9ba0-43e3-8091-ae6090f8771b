import { NextRequest } from "next/server"
import { withTenantAuth } from "@/lib/tenant-utils"
import { jsonOk, jsonError } from "@/lib/api-helpers"
import { workOrderGenerationService } from "@/lib/services/work-order-generation"
import { db } from "@/lib/db"
import { salesContracts } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { z } from "zod"

// ✅ VALIDATION SCHEMA
const generateWorkOrdersSchema = z.object({
  priority: z.enum(["low", "normal", "high", "urgent"]).default("normal"),
  dueDateOffset: z.number().min(1).max(365).optional(), // Days from now
  autoCreateQualityInspections: z.boolean().default(true),
  notes: z.string().optional(),
  preview: z.boolean().default(false), // If true, return preview without creating
})

// ✅ PREVIEW WORK ORDER GENERATION
export const GET = withTenantAuth(async function GET(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: contractId } = await params

    // Get generation preview
    const preview = await workOrderGenerationService.previewGeneration(
      contractId,
      context.companyId
    )

    return jsonOk({
      preview,
      message: `Ready to generate ${preview.totalWorkOrders} work orders for contract ${preview.contract.number}`,
    })
  } catch (error) {
    console.error("Error previewing work order generation:", error)
    return jsonError(
      error instanceof Error ? error.message : "Failed to preview work order generation",
      500
    )
  }
})

// ✅ GENERATE WORK ORDERS FROM CONTRACT
export const POST = withTenantAuth(async function POST(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: contractId } = await params

    // Parse and validate request body
    const body = await request.json().catch(() => ({}))
    const validation = generateWorkOrdersSchema.safeParse(body)

    if (!validation.success) {
      return jsonError("Invalid request parameters", 400, validation.error.errors)
    }

    const options = validation.data

    // If preview mode, return preview without creating
    if (options.preview) {
      const preview = await workOrderGenerationService.previewGeneration(
        contractId,
        context.companyId,
        options
      )

      return jsonOk({
        preview,
        message: `Preview: ${preview.totalWorkOrders} work orders would be created`,
      })
    }

    // Generate work orders
    const generatedWorkOrders = await workOrderGenerationService.generateFromContract(
      contractId,
      context.companyId,
      options
    )

    // Update contract status to 'active' after successful work order generation
    await db.update(salesContracts)
      .set({ status: "active" })
      .where(and(
        eq(salesContracts.id, contractId),
        eq(salesContracts.company_id, context.companyId)
      ))

    return jsonOk({
      workOrders: generatedWorkOrders,
      totalGenerated: generatedWorkOrders.length,
      message: `Successfully generated ${generatedWorkOrders.length} work orders and activated contract`,
    }, { status: 201 })

  } catch (error) {
    console.error("Error generating work orders:", error)
    
    // Return specific error messages for business logic errors
    if (error instanceof Error) {
      return jsonError(error.message, 400)
    }
    
    return jsonError("Failed to generate work orders", 500)
  }
})

// ✅ REGENERATE WORK ORDERS (DELETE EXISTING AND CREATE NEW)
export const PUT = withTenantAuth(async function PUT(
  request: NextRequest,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: contractId } = await params

    // Parse and validate request body
    const body = await request.json().catch(() => ({}))
    const validation = generateWorkOrdersSchema.safeParse(body)

    if (!validation.success) {
      return jsonError("Invalid request parameters", 400, validation.error.errors)
    }

    const options = validation.data

    // Verify contract exists and belongs to company
    const contract = await db.query.salesContracts.findFirst({
      where: and(
        eq(salesContracts.id, contractId),
        eq(salesContracts.company_id, context.companyId)
      ),
    })

    if (!contract) {
      return jsonError("Contract not found", 404)
    }

    if (contract.status !== "active" && contract.status !== "approved") {
      return jsonError(
        `Cannot regenerate work orders for contract with status: ${contract.status}`,
        400
      )
    }

    // TODO: Implement regeneration logic
    // This would involve:
    // 1. Check if existing work orders can be safely deleted (not in progress)
    // 2. Delete existing work orders and related records
    // 3. Generate new work orders with updated parameters
    
    return jsonError("Work order regeneration not yet implemented", 501)

  } catch (error) {
    console.error("Error regenerating work orders:", error)
    return jsonError("Failed to regenerate work orders", 500)
  }
})

// ✅ GET WORK ORDERS FOR CONTRACT
export const DELETE = withTenantAuth(async function DELETE(
  request: Request,
  context,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: contractId } = await params

    // TODO: Implement work order cancellation logic
    // This would involve:
    // 1. Find all work orders for the contract
    // 2. Check if they can be safely cancelled (not completed)
    // 3. Cancel work orders and update contract status
    // 4. Handle inventory reservations and notifications

    return jsonError("Work order cancellation not yet implemented", 501)

  } catch (error) {
    console.error("Error cancelling work orders:", error)
    return jsonError("Failed to cancel work orders", 500)
  }
})
