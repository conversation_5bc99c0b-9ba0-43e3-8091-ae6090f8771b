"use client";

import { useState, useMemo } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ArrowLeft, File, Mail, Phone, Users, Package, Eye, Search } from "lucide-react";
import Link from "next/link";

type Customer = {
  id: string;
  name: string;
  contact_name: string | null;
  contact_phone: string | null;
  contact_email: string | null;
  address: string | null;
  incoterm: string | null;
  payment_term: string | null;
  status: string;
  created_at: Date | null;
  salesContracts: Array<{
    id: string;
    number: string;
    date: string;
    status: string;
    items: Array<{
      id: string;
    }>;
  }>;
};

type Sample = {
  id: string;
  code: string;
  name: string;
  sample_direction: string;
  status: string;
  approval_status: string | null;
  priority: string | null;
  date: string;
  created_at: Date | null;
  supplier: {
    id: string;
    name: string;
  } | null;
  product: {
    id: string;
    name: string;
    sku: string;
  } | null;
};

interface CustomerViewClientProps {
  customer: Customer;
  relatedSamples: Sample[];
}

export function CustomerViewClient({ customer, relatedSamples }: CustomerViewClientProps) {
  const [sampleSearchTerm, setSampleSearchTerm] = useState("");

  // Helper functions for sample display
  const getSampleStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return "default";
      case "pending":
        return "secondary";
      case "rejected":
        return "destructive";
      case "in_progress":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getSampleDirectionIcon = (direction: string) => {
    switch (direction) {
      case "inbound":
        return "📥";
      case "outbound":
        return "📤";
      case "internal":
        return "🏭";
      default:
        return "📦";
    }
  };

  // Filter samples based on search term
  const filteredSamples = useMemo(() => {
    if (!sampleSearchTerm.trim()) {
      return relatedSamples;
    }

    const searchLower = sampleSearchTerm.toLowerCase();
    return relatedSamples.filter((sample) =>
      sample.name.toLowerCase().includes(searchLower) ||
      sample.code.toLowerCase().includes(searchLower) ||
      sample.sample_direction.toLowerCase().includes(searchLower) ||
      sample.status.toLowerCase().includes(searchLower) ||
      (sample.approval_status && sample.approval_status.toLowerCase().includes(searchLower)) ||
      (sample.product && sample.product.name.toLowerCase().includes(searchLower)) ||
      (sample.supplier && sample.supplier.name.toLowerCase().includes(searchLower))
    );
  }, [relatedSamples, sampleSearchTerm]);

  return (
    <div className="space-y-6">
      <div>
        <Button asChild variant="ghost">
          <Link href="/customers">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Customers
          </Link>
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>{customer.name}</CardTitle>
          <CardDescription>
            Status: <span className="capitalize">{customer.status}</span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-3">
              <h3 className="font-semibold">Contact Information</h3>
              <div className="space-y-2">
                {customer.contact_name && (
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    {customer.contact_name}
                  </div>
                )}
                {customer.contact_phone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    {customer.contact_phone}
                  </div>
                )}
                {customer.contact_email && (
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    {customer.contact_email}
                  </div>
                )}
              </div>
            </div>
            <div className="space-y-3">
              <h3 className="font-semibold">Details</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <strong>Address:</strong> {customer.address || "N/A"}
                </div>
                <div>
                  <strong>Incoterm:</strong> {customer.incoterm || "N/A"}
                </div>
                <div>
                  <strong>Payment Term:</strong> {customer.payment_term || "N/A"}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Sales Contracts</CardTitle>
          <CardDescription>Sales contracts for this customer.</CardDescription>
        </CardHeader>
        <CardContent>
          {customer.salesContracts.length === 0 ? (
            <p className="text-sm text-muted-foreground">No sales contracts found for this customer.</p>
          ) : (
            <div className="space-y-4">
              {customer.salesContracts.map((contract) => (
                <div key={contract.id} className="border rounded-lg p-4">
                  <h4 className="font-semibold">Contract: {contract.number}</h4>
                  <p className="text-sm text-muted-foreground">Date: {contract.date}</p>
                  <p className="text-sm text-muted-foreground">Status: {contract.status}</p>
                  {contract.items && contract.items.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium">Items: {contract.items.length}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Related Samples with Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Related Samples
          </CardTitle>
          <CardDescription>
            Samples related to this customer (both inbound and outbound)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {relatedSamples.length > 0 ? (
            <div className="space-y-4">
              {/* Search Input */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search samples by name, code, direction, status..."
                  value={sampleSearchTerm}
                  onChange={(e) => setSampleSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filtered Results */}
              {filteredSamples.length > 0 ? (
                <div className="space-y-4">
                  {filteredSamples.map((sample) => (
                    <div key={sample.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="text-sm">{getSampleDirectionIcon(sample.sample_direction)}</span>
                          <span className="font-medium">{sample.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {sample.code}
                          </Badge>
                          {sample.approval_status && (
                            <Badge variant={getSampleStatusColor(sample.approval_status)}>
                              {sample.approval_status}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>{new Date(sample.date).toLocaleDateString()}</span>
                          <span className="capitalize">{sample.sample_direction}</span>
                          {sample.product && (
                            <span className="flex items-center gap-1">
                              <Package className="h-3 w-3" />
                              {sample.product.name}
                            </span>
                          )}
                          {sample.priority && (
                            <Badge variant="outline" className="text-xs">
                              {sample.priority}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <Link href={`/samples/${sample.id}`}>
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </Link>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No samples found</h3>
                  <p className="text-muted-foreground mb-4">
                    No samples match your search criteria. Try adjusting your search terms.
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-6">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Related Samples</h3>
              <p className="text-muted-foreground mb-4">
                No samples have been associated with this customer yet.
              </p>
              <Link href="/samples/create">
                <Button>
                  <Package className="h-4 w-4 mr-2" />
                  Create Sample
                </Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
