import { AppShell } from "@/components/app-shell"
import { db } from "@/lib/db"
import { customers, samples } from "@/lib/schema-postgres"
import { eq, and } from "drizzle-orm"
import { notFound, redirect } from "next/navigation"
import { getTenantContext } from "@/lib/tenant-utils"
import { CustomerViewClient } from "./customer-view-client"

export default async function CustomerDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params

  // 🛡️ CRITICAL: Ensure proper tenant authentication
  const context = await getTenantContext();

  if (!context) {
    // User not authenticated or no company - redirect to login
    redirect('/api/auth/login');
  }

  // 🛡️ SECURE: Only fetch customer for the current company
  const customer = await db.query.customers.findFirst({
    where: and(
      eq(customers.id, id),
      eq(customers.company_id, context.companyId)
    ),
    with: {
      salesContracts: {
        with: {
          items: true,
        },
      },
      arInvoices: true,
    },
  })

  if (!customer) {
    notFound()
  }

  // 🛡️ SECURE: Only fetch related samples for this customer and company
  const relatedSamples = await db.query.samples.findMany({
    where: and(
      eq(samples.customer_id, id),
      eq(samples.company_id, context.companyId)
    ),
    with: {
      supplier: true,
      product: true,
    },
    orderBy: (samples, { desc }) => [desc(samples.created_at)],
  });

  return (
    <AppShell>
      <CustomerViewClient
        customer={customer}
        relatedSamples={relatedSamples}
      />
    </AppShell>
  )
}
