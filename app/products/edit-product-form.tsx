"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { useRouter } from "next/navigation"

import { Button } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import type { products } from "@/lib/schema-postgres"
import { useI18n } from "@/components/i18n-provider"

const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  sku: z.string().min(2, { message: "SKU must be at least 2 characters." }),
  unit: z.string().min(1, { message: "Unit is required." }),
  hs_code: z.string().optional(),
  origin: z.string().optional(),
  package: z.string().optional(),
  image: z.string().optional(),
  inspection_required: z.boolean().default(false),
  quality_tolerance: z.string().optional(),
  quality_notes: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>
type Product = typeof products.$inferSelect

export function EditProductForm({ setOpen, product }: { setOpen: (open: boolean) => void; product: Product }) {
  const router = useRouter()
  const { t } = useI18n()
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: product.name || "",
      sku: product.sku || "",
      unit: product.unit || "",
      hs_code: product.hs_code || "",
      origin: product.origin || "",
      package: product.package || "",
      image: product.image || "",
      inspection_required: product.inspection_required === "true",
      quality_tolerance: product.quality_tolerance || "",
      quality_notes: product.quality_notes || "",
    },
  })

  async function onSubmit(values: FormValues) {
    const response = await fetch(`/api/products/${product.id}`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        ...values,
        inspection_required: values.inspection_required ? "true" : "false", // Convert boolean to string
      }),
    })

    if (response.ok) {
      // Use setTimeout to prevent React setState during render errors
      setTimeout(() => {
        toast.success(t("products.success.updated"), {
          description: t("products.success.updated_desc"),
        })
      }, 0)

      setOpen(false)

      // Refresh after a delay to ensure state updates complete
      setTimeout(() => {
        router.refresh()
      }, 100)
    } else {
      const errorData = await response.json().catch(() => ({}))

      // Use setTimeout to prevent React setState during render errors
      setTimeout(() => {
        toast.error(t("products.error.update"), {
          description: errorData.error || "An unexpected error occurred.",
        })
      }, 0)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.name")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. High-Grade Widget" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="sku"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.sku")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. WID-HG-001" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="unit"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.unit")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. pcs, kg, meters" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="hs_code"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.hs_code")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. 847990" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="origin"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.origin")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. China" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="package"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("products.form.package")}</FormLabel>
              <FormControl>
                <Input placeholder="e.g. Carton, Bag, Pallet" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Quality Requirements Section */}
        <div className="border-t pt-4 mt-4">
          <h3 className="text-lg font-medium mb-4">{t("products.form.quality_requirements")}</h3>

          <FormField
            control={form.control}
            name="inspection_required"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">{t("products.form.inspection_required")}</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    {t("products.form.inspection_required_desc")}
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="quality_tolerance"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("products.form.quality_tolerance")}</FormLabel>
                <FormControl>
                  <Input placeholder="e.g. ±5%, 0.1mm tolerance" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="quality_notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("products.form.quality_notes")}</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={t("products.form.quality_notes_placeholder")}
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-2">
          <Button type="button" variant="ghost" onClick={() => setOpen(false)}>
            {t("common.cancel")}
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? t("common.loading") : t("common.save")}
          </Button>
        </div>
      </form>
    </Form>
  )
}
