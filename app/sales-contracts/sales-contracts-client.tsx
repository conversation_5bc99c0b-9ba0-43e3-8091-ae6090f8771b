"use client";


import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, <PERSON>Pen, FileText, Plus, Trash2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useI18n } from "@/components/i18n-provider";

// Type definitions matching the schema
type SalesContract = {
  id: string;
  number: string;
  customer_id: string;
  template_id?: string;
  date: string;
  currency?: string;
  status: string;
  created_at: Date | null;
  customer: {
    id: string;
    name: string;
    contact_name: string | null;
    contact_phone: string | null;
    contact_email: string | null;
    address: string | null;
    tax_id: string | null;
    bank: string | null;
    incoterm: string | null;
    payment_term: string | null;
    status: string | null;
    created_at: Date | null;
  };
  items: {
    id: string;
    contract_id: string;
    product_id: string;
    qty: string;
    price: string;
    product: {
      id: string;
      sku: string;
      name: string;
      unit: string;
      hs_code: string | null;
      origin: string | null;
      package: string | null;
      image: string | null;
      created_at: Date | null;
    };
  }[];
};

type Customer = {
  id: string;
  name: string;
  contact_name: string | null;
  contact_phone: string | null;
  contact_email: string | null;
  address: string | null;
  tax_id: string | null;
  bank: string | null;
  incoterm: string | null;
  payment_term: string | null;
  status: string | null;
  created_at: Date | null;
};

type Product = {
  id: string;
  sku: string;
  name: string;
  unit: string;
  hs_code: string | null;
  origin: string | null;
  package: string | null;
  image: string | null;
  created_at: Date | null;
};

type ContractTemplate = {
  id: string;
  name: string;
  type: string;
  content: string;
  currency?: string;
  payment_terms?: string;
  delivery_terms?: string;
  language: string;
  version: number;
  is_active: boolean;
  created_at: Date | null;
};

const formSchema = z.object({
  number: z.string().min(1, "Contract number is required."),
  customer_id: z.string().min(1, "Customer is required."),
  template_id: z.string().optional(),
  currency: z.string().min(1, "Currency is required.").optional(),
  items: z.array(
    z.object({
      product_id: z.string().min(1, "Product is required."),
      qty: z.coerce.number().min(0.01, "Quantity must be greater than 0."),
      price: z.coerce.number().min(0.01, "Price must be greater than 0."),
    }),
  ).min(1, "At least one item is required."),
});

// AddSalesContractForm removed - now using dedicated page at /sales-contracts/add
// EditSalesContractForm removed - now using dedicated page at /sales-contracts/edit/[id]

export function SalesContractsClientPage({
  initialContracts,
}: {
  initialContracts: SalesContract[];
}) {
  const router = useRouter();
  const { t } = useI18n();

  const [contractToDelete, setContractToDelete] = useState<SalesContract | null>(null);

  const handleViewDocument = (contract: SalesContract) => {
    // Navigate to the dedicated contract view page
    router.push(`/sales-contracts/view/${contract.id}`);
  };

  const handleDelete = async () => {
    if (!contractToDelete) return;
    const response = await fetch(`/api/contracts/sales/${contractToDelete.id}`, { method: "DELETE" });
    if (response.ok) {
      toast.success(t("sales_contracts.success.deleted"));
      setContractToDelete(null);
      router.refresh();
    } else {
      toast.error(t("sales_contracts.error.delete"));
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t("sales_contracts.title")}</h1>
          <p className="text-muted-foreground">{t("sales_contracts.subtitle")}</p>
        </div>
        <Link href="/sales-contracts/add">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            {t("sales_contracts.add")}
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("sales_contracts.title")}</CardTitle>
          <CardDescription>{t("sales_contracts.subtitle")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {initialContracts.map((contract) => (
              <Card key={contract.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <FileText className="h-6 w-6" />
                        <CardTitle className="text-lg">{contract.number}</CardTitle>
                        <Badge>{contract.status}</Badge>
                      </div>
                      <CardDescription>{t("field.customer")}: {contract.customer.name}</CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      {contract.template_id && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleViewDocument(contract)}
                          title="View Contract Document"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                      <Link href={`/sales-contracts/edit/${contract.id}`}>
                        <Button
                          variant="ghost"
                          size="icon"
                          title="Edit Contract"
                        >
                          <FilePen className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button variant="ghost" size="icon" onClick={() => setContractToDelete(contract)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <h4 className="font-semibold">{t("sales_contracts.form.items")} ({contract.items.length})</h4>
                  <ul className="list-disc pl-5 mt-2 text-sm">
                    {contract.items.map((item) => (
                      <li key={item.id}>
                        {item.product.name} - {item.qty} {item.product.unit} @ {item.price} {contract.currency}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>



      {/* Delete Alert Dialog */}
      <AlertDialog open={!!contractToDelete} onOpenChange={(open) => !open && setContractToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("sales_contracts.delete.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("sales_contracts.delete.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>{t("common.delete")}</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
