// Jest polyfills for Node.js environment

// Polyfill for TextEncoder/TextDecoder
const { TextEncoder, TextDecoder } = require('util')

global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Polyfill for URL and URLSearchParams
if (!global.URL) {
  global.URL = require('url').URL
}

if (!global.URLSearchParams) {
  global.URLSearchParams = require('url').URLSearchParams
}

// Polyfill for crypto
if (!global.crypto) {
  global.crypto = require('crypto').webcrypto
}

// Polyfill for performance
if (!global.performance) {
  global.performance = require('perf_hooks').performance
}

// Mock fetch for testing (using jest.fn instead of node-fetch)
if (!global.fetch) {
  global.fetch = jest.fn(() =>
    Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve({}),
      text: () => Promise.resolve(''),
    })
  )
}

// Mock AbortController for testing
if (!global.AbortController) {
  global.AbortController = class AbortController {
    constructor() {
      this.signal = {
        aborted: false,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      }
    }
    abort() {
      this.signal.aborted = true
    }
  }
}

// Mock FormData for testing
if (!global.FormData) {
  global.FormData = class FormData {
    constructor() {
      this.data = new Map()
    }
    append(key, value) {
      this.data.set(key, value)
    }
    get(key) {
      return this.data.get(key)
    }
    has(key) {
      return this.data.has(key)
    }
  }
}

// Mock Headers for testing
if (!global.Headers) {
  global.Headers = class Headers {
    constructor(init = {}) {
      this.headers = new Map(Object.entries(init))
    }
    get(name) {
      return this.headers.get(name.toLowerCase())
    }
    set(name, value) {
      this.headers.set(name.toLowerCase(), value)
    }
    has(name) {
      return this.headers.has(name.toLowerCase())
    }
  }
}

// Mock Request for testing
if (!global.Request) {
  global.Request = class Request {
    constructor(url, options = {}) {
      this.url = url
      this.method = options.method || 'GET'
      this.headers = new Headers(options.headers)
      this.body = options.body
    }
  }
}

// Mock Response for testing
if (!global.Response) {
  global.Response = class Response {
    constructor(body, options = {}) {
      this.body = body
      this.status = options.status || 200
      this.ok = this.status >= 200 && this.status < 300
      this.headers = new Headers(options.headers)
    }
    json() {
      return Promise.resolve(JSON.parse(this.body || '{}'))
    }
    text() {
      return Promise.resolve(this.body || '')
    }
  }
}
