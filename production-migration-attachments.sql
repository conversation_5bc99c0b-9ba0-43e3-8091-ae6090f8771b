-- ✅ MANUFACTURING ERP: Quality Control Attachments Migration
-- Production Migration Script for Supabase Database
-- 
-- This script adds attachment support to the quality_inspections table
-- Execute this in Supabase SQL Editor after local testing is complete

-- Add attachment fields to quality_inspections table
ALTER TABLE quality_inspections 
ADD COLUMN IF NOT EXISTS attachments text,
ADD COLUMN IF NOT EXISTS photos text;

-- Add comments for documentation
COMMENT ON COLUMN quality_inspections.attachments IS 'JSON array of document filenames uploaded for this inspection';
COMMENT ON COLUMN quality_inspections.photos IS 'JSON array of photo filenames uploaded for this inspection';

-- Verify the changes
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'quality_inspections' 
  AND column_name IN ('attachments', 'photos')
ORDER BY column_name;

-- Test query to ensure fields work correctly
SELECT id, inspector, attachments, photos 
FROM quality_inspections 
LIMIT 3;
