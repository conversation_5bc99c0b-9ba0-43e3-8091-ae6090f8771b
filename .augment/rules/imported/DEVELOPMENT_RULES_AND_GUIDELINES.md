---
type: "always_apply"
---

## 📋 Manufacturing ERP Development Rules and User Guidelines
### Comprehensive Reference for Enterprise-Grade Development

---

## **🎯 DOCUMENT PURPOSE**

This document establishes the definitive development standards, patterns, and guidelines for the Manufacturing ERP system. It serves as the authoritative reference for maintaining consistency, quality, and enterprise-grade standards across all development work.

**Target Audience**: Developers, AI assistants, and technical stakeholders working on the Manufacturing ERP system.

---

## **📊 1. MANUFACTURING ERP DEVELOPMENT STANDARDS**

### **🔧 Code Quality Standards**

#### **TypeScript Standards**
```typescript
// ✅ REQUIRED: Strict TypeScript configuration
// tsconfig.json must include:
{
  "compilerOptions": {
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true
  }
}

// ✅ REQUIRED: Interface definitions for all data structures
interface Customer {
  id: string
  company_id: string  // ALWAYS required for multi-tenant isolation
  name: string
  email: string
  created_at: Date
}

// ✅ REQUIRED: Zod schemas for all API validation
import { z } from "zod"

const customerSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Valid email required"),
  phone: z.string().optional(),
})

// ❌ FORBIDDEN: Any types, implicit any, or untyped objects
// ❌ FORBIDDEN: Direct database queries without company_id filtering
```

#### **Next.js/React Standards**
```typescript
// ✅ REQUIRED: Server Components for data fetching
export default async function CustomersPage() {
  const context = await getTenantContext()
  if (!context) redirect('/api/auth/login')
  
  const customers = await getCustomers(context.companyId)
  return <CustomersTable customers={customers} />
}

// ✅ REQUIRED: Client Components only when necessary
"use client"
import { useSafeToast } from "@/hooks/use-safe-toast"

// ✅ REQUIRED: Professional error handling
const { toast } = useSafeToast()
try {
  await apiCall()
  toast({ title: "Success", description: "Operation completed" })
} catch (error) {
  toast({ 
    title: "Error", 
    description: "Operation failed", 
    variant: "destructive" 
  })
}

// ❌ FORBIDDEN: useState during render (causes React errors)
// ❌ FORBIDDEN: Direct DOM manipulation
// ❌ FORBIDDEN: Inline styles (use Tailwind classes)
```

#### **Database/Drizzle Standards**
```typescript
// ✅ REQUIRED: All tables must include company_id and timestamps
export const newTable = pgTable("new_table", {
  id: text("id").primaryKey(),
  company_id: text("company_id").notNull().references(() => companies.id),
  // ... other fields
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  // ✅ REQUIRED: Index on company_id for performance
  companyIdIdx: index("table_company_id_idx").on(table.company_id),
}))

// ✅ REQUIRED: Relations definition
export const newTableRelations = relations(newTable, ({ one, many }) => ({
  company: one(companies, {
    fields: [newTable.company_id],
    references: [companies.id],
  }),
}))

// ✅ REQUIRED: All queries must filter by company_id
const items = await db.query.newTable.findMany({
  where: eq(newTable.company_id, context.companyId),
  with: { company: true },
})

// ❌ FORBIDDEN: Queries without company_id filtering
// ❌ FORBIDDEN: Raw SQL without parameterization
```

### **🔒 Multi-Tenant Security Requirements**

#### **API Endpoint Security Pattern**
```typescript
// ✅ REQUIRED: All API endpoints must use withTenantAuth
import { withTenantAuth } from "@/lib/tenant-utils"

export const GET = withTenantAuth(async function GET(request, context) {
  // context.companyId is automatically available and validated
  // context.userId contains the authenticated user ID
  
  const items = await db.query.table.findMany({
    where: eq(table.company_id, context.companyId), // ALWAYS required
  })
  
  return jsonOk(items)
})

// ✅ REQUIRED: Individual resource access validation
export const GET = withTenantAuth(async function GET(
  request, 
  context, 
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  const item = await db.query.table.findFirst({
    where: and(
      eq(table.id, id),
      eq(table.company_id, context.companyId) // CRITICAL: Prevent cross-tenant access
    ),
  })
  
  if (!item) return jsonError("Not found", 404)
  return jsonOk(item)
})

// ❌ FORBIDDEN: API endpoints without withTenantAuth
// ❌ FORBIDDEN: Database queries without company_id filtering
// ❌ FORBIDDEN: Exposing internal IDs or system information
```

#### **Page-Level Security Pattern**
```typescript
// ✅ REQUIRED: All pages must validate tenant context
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"

export default async function SecurePage() {
  const context = await getTenantContext()
  if (!context) {
    redirect('/api/auth/login')
  }
  
  // Use context.companyId for all data fetching
  const data = await getData(context.companyId)
  
  return <PageContent data={data} />
}

// ❌ FORBIDDEN: Pages without tenant context validation
// ❌ FORBIDDEN: Client-side authentication checks only
```

### **🗄️ Database Schema Evolution Rules**

#### **Migration Standards**
```sql
-- ✅ REQUIRED: Additive changes only (no breaking changes)
ALTER TABLE existing_table ADD COLUMN new_field TEXT DEFAULT 'default_value';
ALTER TABLE existing_table ADD COLUMN optional_field TEXT;

-- ✅ REQUIRED: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_table_new_field ON existing_table(new_field);

-- ✅ REQUIRED: Add foreign key constraints
ALTER TABLE existing_table ADD CONSTRAINT fk_table_reference 
  FOREIGN KEY (reference_id) REFERENCES reference_table(id);

-- ❌ FORBIDDEN: Dropping columns or tables
-- ❌ FORBIDDEN: Changing column types (breaking change)
-- ❌ FORBIDDEN: Removing constraints or indexes
```

#### **Schema Synchronization Protocol**
```bash
# ✅ REQUIRED: Local development first
npm run db:generate  # Generate migration
npm run db:migrate   # Apply locally
npm run dev         # Test locally

# ✅ REQUIRED: Production synchronization after commit
# Execute equivalent SQL in Supabase SQL Editor
# Verify schema matches between local and production
```

### **🌐 API Endpoint Conventions**

#### **Standard CRUD Pattern**
```typescript
// ✅ REQUIRED: Consistent endpoint structure
/app/api/[module]/
  route.ts           // GET (list), POST (create)
  [id]/route.ts      // GET (read), PATCH (update), DELETE (delete)
  [id]/[action]/route.ts  // Module-specific actions

// ✅ REQUIRED: Standard response format
import { jsonOk, jsonError } from "@/lib/api-helpers"

// Success response
return jsonOk(data, { status: 201 })

// Error response
return jsonError("Validation failed", 400, { field: "error details" })

// ✅ REQUIRED: Consistent error handling
try {
  // API logic
} catch (error) {
  console.error("API Error:", error)
  return jsonError("Internal server error", 500)
}
```

#### **Validation Pattern**
```typescript
// ✅ REQUIRED: Zod validation for all inputs
const schema = z.object({
  name: z.string().min(1, "Name required"),
  email: z.string().email("Valid email required"),
})

export const POST = withTenantAuth(async function POST(req, context) {
  try {
    const body = await req.json()
    const validatedData = schema.parse(body) // Throws on validation error
    
    // Process validated data
    const result = await createItem(validatedData, context.companyId)
    return jsonOk(result, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return jsonError("Validation failed", 400, error.errors)
    }
    return jsonError("Internal server error", 500)
  }
})
```

### **🎨 UI/UX Component Standards**

#### **Shadcn/ui Component Usage**
```typescript
// ✅ REQUIRED: Use Shadcn/ui components consistently
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// ✅ REQUIRED: Professional table layout (preferred over cards)
export function DataTable({ data }: { data: any[] }) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Column 1</TableHead>
          <TableHead>Column 2</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.map((item) => (
          <TableRow key={item.id}>
            <TableCell>{item.field1}</TableCell>
            <TableCell>{item.field2}</TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm">
                  <Eye className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Edit className="h-4 w-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

// ❌ FORBIDDEN: Custom CSS or styled-components
// ❌ FORBIDDEN: Inline styles
// ❌ FORBIDDEN: Card layouts for data tables
```

#### **Responsive Design Standards**
```typescript
// ✅ REQUIRED: Mobile-first responsive design
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Content */}
</div>

// ✅ REQUIRED: Responsive table handling
<div className="overflow-x-auto">
  <Table>
    {/* Table content */}
  </Table>
</div>

// ✅ REQUIRED: Mobile navigation patterns
<div className="hidden md:block">
  {/* Desktop navigation */}
</div>
<div className="md:hidden">
  {/* Mobile navigation */}
</div>
```

#### **Bilingual Support Standards**
```typescript
// ✅ REQUIRED: Use i18n for all user-facing text
import { useI18n } from "@/hooks/use-i18n"

export function Component() {
  const { t } = useI18n()
  
  return (
    <div>
      <h1>{t("customers.title")}</h1>
      <p>{t("customers.description")}</p>
    </div>
  )
}

// ✅ REQUIRED: Add translations to both languages
// locales/en.json
{
  "customers": {
    "title": "Customers",
    "description": "Manage your customer relationships"
  }
}

// locales/zh.json
{
  "customers": {
    "title": "客户",
    "description": "管理您的客户关系"
  }
}

// ❌ FORBIDDEN: Hardcoded English text in components
// ❌ FORBIDDEN: Missing Chinese translations
```

---

## **📋 2. PROJECT-SPECIFIC GUIDELINES**

### **🔄 Established Development Workflow**

#### **Development Cycle (MANDATORY)**
```bash
# ✅ PHASE 1: Local Development
npm run dev                    # Start development server
# ... make changes ...
npm run type-check            # TypeScript validation
npm run lint                  # ESLint validation
npm run build                 # Build verification

# ✅ PHASE 2: Quality Assurance
# Test all functionality locally
# Verify multi-tenant isolation
# Check responsive design
# Test bilingual support

# ✅ PHASE 3: Database Synchronization (if schema changes)
# Execute SQL commands in production Supabase
# Verify schema consistency

# ✅ PHASE 4: Deployment
git add .
git commit -m "feat: descriptive commit message"
git push origin main          # Triggers automatic deployment

# ✅ PHASE 5: Production Verification
# Wait 3-5 minutes for deployment
# Test at https://silk-road-john.vercel.app/
# Verify all functionality works
```

#### **Commit Message Standards**
```bash
# ✅ REQUIRED: Descriptive commit messages
git commit -m "feat: Add sample approval workflow with customer integration

✅ ENHANCEMENTS:
- Sample approval status tracking (pending/approved/rejected)
- Customer-sample relationship integration
- Professional approval UI with manager permissions
- Automated workflow state transitions

✅ TECHNICAL:
- Enhanced samples table with approval fields
- New approval API endpoint with validation
- Professional toast notifications
- Consistent multi-tenant security patterns

🎯 IMPACT:
- Streamlined sample management process
- Improved customer relationship tracking
- Professional approval workflow for quality control"

# ❌ FORBIDDEN: Vague commit messages like "fix bug" or "update code"
```

### **🗄️ Database Architecture Rules**

#### **Dual Database Management**
```typescript
// ✅ REQUIRED: Environment-specific database connections
// Local Development: PostgreSQL (localhost:5432/manufacturing_erp)
// Production: Supabase PostgreSQL (aws-1-ap-southeast-1.pooler.supabase.com:6543)

// ✅ REQUIRED: Schema synchronization protocol
// 1. Make changes locally first
// 2. Test thoroughly in local environment
// 3. Generate and apply migrations locally
// 4. After successful deployment, execute equivalent SQL in production Supabase
// 5. Verify schema consistency between environments

// ✅ REQUIRED: Database connection configuration
// lib/db.ts handles environment-specific connections automatically
const connectionString = process.env.DATABASE_URL ||
  "postgresql://localhost:5432/manufacturing_erp"

// ❌ FORBIDDEN: Direct production database modifications without local testing
// ❌ FORBIDDEN: Schema changes that break existing functionality
```

#### **Data Isolation Requirements**
```sql
-- ✅ REQUIRED: All tables must enforce company isolation
CREATE TABLE new_module_table (
  id TEXT PRIMARY KEY,
  company_id TEXT NOT NULL REFERENCES companies(id),
  -- other fields
);

-- ✅ REQUIRED: Performance indexes
CREATE INDEX idx_new_module_company ON new_module_table(company_id);

-- ✅ REQUIRED: All queries must filter by company_id
SELECT * FROM new_module_table WHERE company_id = $1;

-- ❌ FORBIDDEN: Cross-tenant data access
-- ❌ FORBIDDEN: Queries without company_id filtering
```

### **🏗️ Module Implementation Patterns**

#### **Standard Module Structure**
```typescript
// ✅ REQUIRED: Consistent file organization
/app/[module]/
  page.tsx                    // List view with search/filter
  create/page.tsx            // Create form
  [id]/page.tsx             // Detail view with relationships
  [id]/edit/page.tsx        // Edit form
  [id]/[action]/page.tsx    // Module-specific actions

/app/api/[module]/
  route.ts                  // GET (list), POST (create)
  [id]/route.ts            // GET (read), PATCH (update), DELETE
  [id]/[action]/route.ts   // Module-specific operations

/components/[module]/
  [module]-table.tsx       // Data table component
  [module]-form.tsx        // Reusable form component
  [module]-card.tsx        // Summary card component
  [module]-filters.tsx     // Search and filter controls
```

#### **Page Implementation Pattern**
```typescript
// ✅ REQUIRED: Standard page structure
import { AppShell } from "@/components/app-shell"
import { getTenantContext } from "@/lib/tenant-utils"
import { redirect } from "next/navigation"

export default async function ModulePage() {
  // ✅ REQUIRED: Tenant context validation
  const context = await getTenantContext()
  if (!context) redirect('/api/auth/login')

  // ✅ REQUIRED: Data fetching with company isolation
  const data = await getModuleData(context.companyId)

  return (
    <AppShell>
      <div className="space-y-6">
        {/* ✅ REQUIRED: Professional header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Module Name</h1>
            <p className="text-muted-foreground">Module description</p>
          </div>
          <Button asChild>
            <Link href="/module/create">
              <Plus className="mr-2 h-4 w-4" />
              New Item
            </Link>
          </Button>
        </div>

        {/* ✅ REQUIRED: Search and filters */}
        <div className="flex items-center gap-4">
          <SearchInput />
          <FilterSelects />
        </div>

        {/* ✅ REQUIRED: Data table */}
        <div className="rounded-md border">
          <ModuleTable data={data} />
        </div>
      </div>
    </AppShell>
  )
}
```

### **🔗 Integration Requirements**

#### **Business Workflow Integration**
```typescript
// ✅ REQUIRED: Event-driven workflow integration
interface BusinessWorkflow {
  // Contract → Work Order → Quality → Inventory → Shipping

  // 1. Contract Approval triggers Work Order generation
  onContractApproved: (contractId: string) => Promise<void>

  // 2. Work Order completion triggers Quality Inspection
  onWorkOrderCompleted: (workOrderId: string) => Promise<void>

  // 3. Quality approval triggers Inventory update
  onQualityApproved: (inspectionId: string) => Promise<void>

  // 4. Inventory ready triggers Shipping preparation
  onInventoryReady: (stockId: string) => Promise<void>
}

// ✅ REQUIRED: Workflow implementation example
export const POST = withTenantAuth(async function POST(req, context, { params }) {
  const { id } = await params

  // Update current entity
  await updateEntity(id, context.companyId)

  // Trigger downstream workflow
  await triggerNextWorkflowStep(id, context.companyId)

  return jsonOk({ success: true })
})
```

#### **Data Relationship Patterns**
```typescript
// ✅ REQUIRED: Comprehensive relationship definitions
export const salesContractsRelations = relations(salesContracts, ({ one, many }) => ({
  company: one(companies, {
    fields: [salesContracts.company_id],
    references: [companies.id],
  }),
  customer: one(customers, {
    fields: [salesContracts.customer_id],
    references: [customers.id],
  }),
  items: many(salesContractItems),
  workOrders: many(workOrders),        // Production integration
  qualityInspections: many(qualityInspections), // Quality integration
  shipments: many(shipments),          // Logistics integration
}))

// ✅ REQUIRED: Cross-module data fetching
const contractWithRelations = await db.query.salesContracts.findFirst({
  where: and(
    eq(salesContracts.id, contractId),
    eq(salesContracts.company_id, context.companyId)
  ),
  with: {
    customer: true,
    items: { with: { product: true } },
    workOrders: { with: { operations: true } },
    qualityInspections: true,
  },
})
```

---

## **📋 3. QUALITY ASSURANCE REQUIREMENTS**

### **🧪 Testing Protocols**

#### **Pre-Deployment Testing Checklist**
```typescript
// ✅ REQUIRED: Local testing protocol
const testingChecklist = {
  functionality: [
    "All CRUD operations work correctly",
    "Search and filtering functions properly",
    "Form validation prevents invalid data",
    "Error handling displays appropriate messages",
  ],
  security: [
    "Multi-tenant isolation verified (no cross-company data access)",
    "Authentication required for all protected routes",
    "API endpoints validate company_id in all queries",
    "User permissions respected throughout",
  ],
  performance: [
    "Page load times under 2 seconds",
    "API response times under 500ms",
    "Database queries optimized with proper indexes",
    "No N+1 query problems",
  ],
  userExperience: [
    "Responsive design works on mobile/tablet/desktop",
    "Bilingual support displays correctly",
    "Professional toast notifications work",
    "Navigation and breadcrumbs function properly",
  ],
}

// ✅ REQUIRED: Production verification protocol
const productionVerification = {
  deployment: [
    "Wait 3-5 minutes for deployment completion",
    "Clear browser cache (Ctrl+Shift+R)",
    "Verify deployment at https://silk-road-john.vercel.app/",
  ],
  functionality: [
    "Login/logout flow works correctly",
    "All modified features function as expected",
    "Database changes applied successfully",
    "No console errors or warnings",
  ],
  monitoring: [
    "Check Vercel function logs for errors",
    "Monitor response times and performance",
    "Verify error tracking in Sentry",
    "Confirm multi-tenant isolation maintained",
  ],
}
```

### **📊 Performance Benchmarks**

#### **Response Time Requirements**
```typescript
// ✅ REQUIRED: Performance standards
const performanceStandards = {
  apiEndpoints: {
    list: "< 200ms",      // GET /api/[module]
    read: "< 100ms",      // GET /api/[module]/[id]
    create: "< 300ms",    // POST /api/[module]
    update: "< 200ms",    // PATCH /api/[module]/[id]
    delete: "< 150ms",    // DELETE /api/[module]/[id]
  },
  pageLoads: {
    list: "< 1.5s",       // Module list pages
    detail: "< 1.0s",     // Individual item pages
    forms: "< 1.0s",      // Create/edit forms
    dashboard: "< 2.0s",  // Dashboard with metrics
  },
  database: {
    simpleQuery: "< 10ms",    // Single table queries
    joinQuery: "< 50ms",      // Multi-table joins
    aggregation: "< 100ms",   // COUNT, SUM operations
    fullTextSearch: "< 200ms", // Search operations
  },
}

// ✅ REQUIRED: Performance monitoring
// Monitor these metrics in production
// Alert if response times exceed thresholds
// Optimize queries that consistently exceed limits
```

### **🔒 Security Validation Checklist**

#### **Multi-Tenant Security Verification**
```typescript
// ✅ REQUIRED: Security testing protocol
const securityChecklist = {
  authentication: [
    "Unauthenticated users redirected to login",
    "Session expiration handled gracefully",
    "Auth0 integration working correctly",
  ],
  authorization: [
    "Users can only access their company's data",
    "API endpoints validate company_id in all queries",
    "Cross-tenant data access impossible",
    "Admin functions restricted to appropriate roles",
  ],
  dataProtection: [
    "All database queries filter by company_id",
    "No sensitive data exposed in client-side code",
    "Error messages don't leak system information",
    "Audit logging captures all data modifications",
  ],
  apiSecurity: [
    "All endpoints use withTenantAuth middleware",
    "Input validation prevents injection attacks",
    "Rate limiting prevents abuse",
    "HTTPS enforced in production",
  ],
}

// ✅ REQUIRED: Security testing commands
// Test cross-tenant access by attempting to access other company's data
// Verify API endpoints reject requests without proper authentication
// Confirm database queries always include company_id filtering
```

---

## **📋 4. FUTURE DEVELOPMENT MEMORY GUIDELINES**

### **🏗️ Key Architectural Decisions**

#### **Critical Patterns to Remember**
```typescript
// ✅ ARCHITECTURAL DECISION: Multi-tenant isolation pattern
// NEVER change this pattern - it's fundamental to system security
export const withTenantAuth = (handler: TenantAuthHandler) => {
  return async (req: Request, ...args: any[]) => {
    const session = await getSession(req)
    if (!session) return jsonError("Unauthorized", 401)

    const context = {
      userId: session.user.id,
      companyId: session.user.companyId, // CRITICAL: Always available
    }

    return handler(req, context, ...args)
  }
}

// ✅ ARCHITECTURAL DECISION: Database schema pattern
// ALL tables must follow this pattern for consistency
export const standardTable = pgTable("table_name", {
  id: text("id").primaryKey(),                    // UUID primary key
  company_id: text("company_id").notNull()       // Multi-tenant isolation
    .references(() => companies.id),
  // ... business fields
  created_at: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updated_at: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  companyIdIdx: index("table_company_id_idx").on(table.company_id),
}))

// ✅ ARCHITECTURAL DECISION: API response pattern
// Consistent success/error responses across all endpoints
return jsonOk(data, { status: 201 })           // Success
return jsonError("Error message", 400, details) // Error
```

#### **Technology Stack Decisions**
```typescript
// ✅ STACK DECISION: Core technologies (DO NOT CHANGE)
const technologyStack = {
  frontend: "Next.js 14+ with TypeScript",
  backend: "Next.js API routes with TypeScript",
  database: "PostgreSQL with Drizzle ORM",
  authentication: "Auth0",
  ui: "Shadcn/ui with Tailwind CSS",
  validation: "Zod schemas",
  deployment: "Vercel with automatic GitHub integration",
}

// ✅ STACK DECISION: Development tools (MAINTAIN CONSISTENCY)
const developmentTools = {
  typeChecking: "TypeScript strict mode",
  linting: "ESLint with professional configuration",
  formatting: "Prettier with consistent rules",
  testing: "Local manual testing + production verification",
  monitoring: "Sentry for error tracking",
}
```

### **💼 Critical Business Logic**

#### **Manufacturing Workflow Requirements**
```typescript
// ✅ BUSINESS LOGIC: Core manufacturing workflow
const manufacturingWorkflow = {
  // 1. Sales Contract Management
  contractLifecycle: [
    "draft", "pending_approval", "approved", "in_production",
    "quality_control", "ready_to_ship", "shipped", "completed"
  ],

  // 2. Production Planning
  workOrderGeneration: {
    trigger: "sales_contract.status = 'approved'",
    process: "auto_generate_work_orders_for_each_contract_item",
    result: "work_orders with operation_sequences",
  },

  // 3. Quality Control Integration
  qualityCheckpoints: {
    incoming: "raw_materials_inspection",
    inProcess: "production_quality_checks",
    final: "finished_goods_inspection",
    preShipment: "shipping_quality_verification",
  },

  // 4. Inventory Management
  stockMovements: {
    inbound: "purchase_orders, production_completion",
    outbound: "sales_orders, production_consumption",
    transfers: "location_changes, quality_holds",
  },
}

// ✅ BUSINESS LOGIC: Data relationships
const criticalRelationships = {
  "sales_contract → work_orders": "one_to_many",
  "work_order → quality_inspections": "one_to_many",
  "quality_inspection → certificates": "one_to_many",
  "work_order → stock_transactions": "one_to_many",
  "customer → sales_contracts": "one_to_many",
  "supplier → purchase_contracts": "one_to_many",
  "product → all_transactions": "referenced_everywhere",
}
```

#### **Integration Points**
```typescript
// ✅ INTEGRATION: Module interconnections
const moduleIntegrations = {
  samples: {
    connectsTo: ["customers", "products", "sales_contracts"],
    workflow: "sample_approval → contract_creation",
  },
  workOrders: {
    connectsTo: ["sales_contracts", "products", "quality", "inventory"],
    workflow: "contract_approved → work_order_generated → production_tracking",
  },
  quality: {
    connectsTo: ["work_orders", "products", "inventory", "certificates"],
    workflow: "inspection_required → quality_check → approval/rejection",
  },
  inventory: {
    connectsTo: ["products", "work_orders", "quality", "shipping"],
    workflow: "stock_movements → availability_tracking → shipping_preparation",
  },
  shipping: {
    connectsTo: ["sales_contracts", "inventory", "quality", "export"],
    workflow: "ready_to_ship → shipping_preparation → delivery_tracking",
  },
}
```

### **🎨 User Experience Standards**

#### **Professional UI Expectations**
```typescript
// ✅ UX STANDARD: Professional appearance requirements
const uiStandards = {
  layout: {
    pattern: "AppShell with sidebar navigation",
    responsive: "Mobile-first design with breakpoints",
    consistency: "Same layout patterns across all modules",
  },

  dataDisplay: {
    preferred: "Professional tables with actions",
    avoid: "Card layouts for data lists",
    features: "Search, filtering, sorting, pagination",
  },

  forms: {
    validation: "Real-time with Zod schemas",
    feedback: "Professional toast notifications",
    patterns: "Consistent field layouts and styling",
  },

  navigation: {
    structure: "Hierarchical with breadcrumbs",
    mobile: "Responsive sidebar with hamburger menu",
    consistency: "Same navigation across all pages",
  },

  internationalization: {
    languages: "English and Chinese (Simplified)",
    implementation: "useI18n hook with JSON translation files",
    coverage: "All user-facing text must be translatable",
  },
}

// ✅ UX STANDARD: Professional interactions
const interactionStandards = {
  loading: "Skeleton loaders and loading states",
  errors: "Professional error messages with recovery options",
  success: "Confirmation messages for all actions",
  feedback: "Immediate response to all user actions",
  accessibility: "Keyboard navigation and screen reader support",
}
```

#### **Brand and Design Consistency**
```typescript
// ✅ DESIGN STANDARD: FC-CHINA brand integration
const brandStandards = {
  logo: {
    component: "FCChinaLogo with size variants (sm/md/lg)",
    placement: "Header, footer, and login pages",
    consistency: "Same logo treatment throughout",
  },

  colors: {
    primary: "Taxonomy-inspired professional palette",
    consistency: "Same color scheme across all modules",
    accessibility: "WCAG compliant contrast ratios",
  },

  typography: {
    hierarchy: "Consistent heading and text sizes",
    readability: "Professional font choices and spacing",
    bilingual: "Proper font support for Chinese characters",
  },

  components: {
    library: "Shadcn/ui with consistent customizations",
    patterns: "Reusable component patterns across modules",
    quality: "Professional appearance and interactions",
  },
}
```

---

## **🎯 IMPLEMENTATION PRIORITIES**

### **📋 Development Order**
```typescript
// ✅ PRIORITY ORDER: Module implementation sequence
const implementationOrder = {
  phase1: {
    priority: "HIGH",
    modules: ["samples_management", "work_orders", "enhanced_dashboard"],
    timeline: "4-6 weeks",
    dependencies: "Existing contract system",
  },

  phase2: {
    priority: "MEDIUM",
    modules: ["quality_control", "inventory_management", "shipping"],
    timeline: "6-8 weeks",
    dependencies: "Phase 1 completion",
  },

  phase3: {
    priority: "LOW",
    modules: ["export_trade", "financial_integration", "advanced_analytics"],
    timeline: "8-10 weeks",
    dependencies: "Phase 2 completion",
  },
}

// ✅ PRIORITY: Always maintain production stability
// Never implement breaking changes
// Always test locally before deployment
// Always maintain multi-tenant security
// Always follow established patterns
```

### **🚨 Critical Success Factors**
```typescript
// ✅ SUCCESS FACTORS: Non-negotiable requirements
const criticalFactors = {
  security: "Multi-tenant isolation must never be compromised",
  performance: "Response times must remain under established thresholds",
  stability: "Production system must never break",
  consistency: "All new code must follow established patterns",
  quality: "Enterprise-grade standards must be maintained",
  documentation: "All changes must be properly documented",
  testing: "Comprehensive testing required before deployment",
  userExperience: "Professional UI/UX standards must be maintained",
}
```

---

## **📚 REFERENCE QUICK LINKS**

### **📖 Documentation References**
- **MANUFACTURING_ERP_ROADMAP.md**: Comprehensive 3-phase implementation plan
- **IMPLEMENTATION_GUIDE.md**: Step-by-step development instructions
- **ERP_TRANSFORMATION_SUMMARY.md**: Executive summary and business case
- **DEVELOPMENT_WORKFLOW.md**: Detailed development and deployment procedures

### **🔧 Code Pattern References**
- **withTenantAuth**: `/lib/tenant-utils.ts` - Multi-tenant security middleware
- **Database Schema**: `/lib/schema-postgres.ts` - All table definitions and relations
- **API Helpers**: `/lib/api-helpers.ts` - Standard response patterns
- **UI Components**: `/components/ui/` - Shadcn/ui component library
- **AppShell**: `/components/app-shell.tsx` - Standard page layout

### **🌐 Environment References**
- **Local Development**: http://localhost:3000
- **Production**: https://silk-road-john.vercel.app/
- **Database Local**: postgresql://localhost:5432/manufacturing_erp
- **Database Production**: Supabase PostgreSQL (configured in vercel.json)

---

**🎯 FINAL REMINDER: This document is the authoritative reference for all Manufacturing ERP development. Always consult these guidelines before making any changes to ensure consistency, quality, and adherence to established patterns.**
