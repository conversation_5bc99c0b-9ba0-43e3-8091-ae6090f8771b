#!/usr/bin/env tsx
/**
 * ✅ MANUFACTURING ERP: Fix Quality Inspection Data Consistency
 * Using PROPER DRIZZLE ORM METHODS (not raw SQL)
 * 
 * This script fixes the duplicate inspection issue and implements proper ERP workflow
 */

import { db } from "../lib/db"
import { qualityInspections } from "../lib/schema-postgres"
import { and, eq, isNotNull } from "drizzle-orm"

async function fixQualityInspectionData() {
  console.log("🔧 Starting Quality Inspection Data Fix (Drizzle ORM)...")

  try {
    // 1. ✅ BACKUP: Query existing data for safety
    const existingInspections = await db.query.qualityInspections.findMany({
      where: isNotNull(qualityInspections.work_order_id),
      with: {
        workOrder: true,
      }
    })
    
    console.log(`📊 Found ${existingInspections.length} work order inspections`)
    console.log("📋 Current inspection breakdown:")
    
    const breakdown = existingInspections.reduce((acc, inspection) => {
      acc[inspection.inspection_type] = (acc[inspection.inspection_type] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    console.table(breakdown)

    // 2. ✅ DELETE INCORRECT INSPECTIONS using Drizzle ORM
    // Manufacturing ERP Best Practice: Work orders should only have FINAL inspections
    console.log("\n🗑️ Removing incorrect inspections (incoming + in-process)...")
    
    const deletedIncoming = await db.delete(qualityInspections)
      .where(and(
        isNotNull(qualityInspections.work_order_id),
        eq(qualityInspections.inspection_type, "incoming")
      ))
    
    const deletedInProcess = await db.delete(qualityInspections)
      .where(and(
        isNotNull(qualityInspections.work_order_id),
        eq(qualityInspections.inspection_type, "in-process")
      ))

    console.log(`✅ Deleted ${deletedIncoming.rowCount || 0} incoming inspections`)
    console.log(`✅ Deleted ${deletedInProcess.rowCount || 0} in-process inspections`)

    // 3. ✅ UPDATE REMAINING FINAL INSPECTIONS using Drizzle ORM
    console.log("\n🔄 Updating final inspections to proper status...")
    
    const updatedFinal = await db.update(qualityInspections)
      .set({
        status: "scheduled",
        notes: "Final inspection scheduled for work order completion",
        inspector: "TBD"
      })
      .where(and(
        isNotNull(qualityInspections.work_order_id),
        eq(qualityInspections.inspection_type, "final")
      ))

    console.log(`✅ Updated ${updatedFinal.rowCount || 0} final inspections`)

    // 4. ✅ VERIFY THE FIX using Drizzle ORM
    console.log("\n📊 Verification - After cleanup:")
    
    const finalInspections = await db.query.qualityInspections.findMany({
      where: isNotNull(qualityInspections.work_order_id),
      with: {
        workOrder: true,
      }
    })

    console.log(`📈 Total inspections after cleanup: ${finalInspections.length}`)
    console.log(`📈 Unique work orders with inspections: ${new Set(finalInspections.map(i => i.work_order_id)).size}`)

    // 5. ✅ SHOW CLEAN DATA STRUCTURE using Drizzle ORM
    console.log("\n📋 Final inspection structure:")
    finalInspections.forEach(inspection => {
      console.log(`  ${inspection.workOrder?.number || 'Unknown'} → ${inspection.inspection_type} (${inspection.status})`)
    })

    console.log("\n🎉 Quality Inspection Data Fix Complete!")
    console.log("✅ Expected Result: 4 work orders = 4 final inspections (1:1 ratio)")

  } catch (error) {
    console.error("❌ Error fixing quality inspection data:", error)
    throw error
  }
}

// Run the fix
if (require.main === module) {
  fixQualityInspectionData()
    .then(() => {
      console.log("✅ Script completed successfully")
      process.exit(0)
    })
    .catch((error) => {
      console.error("❌ Script failed:", error)
      process.exit(1)
    })
}

export { fixQualityInspectionData }
