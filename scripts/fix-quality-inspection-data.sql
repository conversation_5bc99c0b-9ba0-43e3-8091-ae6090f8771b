-- ✅ MANUFACTURING ERP: Fix Quality Inspection Data Consistency
-- This script fixes the duplicate inspection issue and implements proper ERP workflow

-- 1. BACKUP EXISTING DATA (for safety)
CREATE TABLE quality_inspections_backup AS SELECT * FROM quality_inspections;

-- 2. <PERSON><PERSON><PERSON> INCORRECT INSPECTIONS (keep only final inspections for work orders)
-- Manufacturing ERP Best Practice: Work orders should only have FINAL inspections
DELETE FROM quality_inspections 
WHERE work_order_id IS NOT NULL 
AND inspection_type IN ('incoming', 'in-process');

-- 3. UPDATE REMAINING FINAL INSPECTIONS TO PROPER STATUS
UPDATE quality_inspections 
SET 
  status = 'scheduled',
  notes = 'Final inspection scheduled for work order completion',
  inspector = 'TBD'
WHERE work_order_id IS NOT NULL 
AND inspection_type = 'final';

-- 4. VERIFY THE FIX
SELECT 
  'AFTER CLEANUP' as phase,
  COUNT(*) as total_inspections,
  COUNT(DISTINCT work_order_id) as work_orders_with_inspections
FROM quality_inspections 
WHERE work_order_id IS NOT NULL;

-- 5. SHOW CLEAN DATA STRUCTURE
SELECT 
  wo.number as work_order,
  qi.inspection_type,
  qi.status,
  qi.notes
FROM quality_inspections qi 
JOIN work_orders wo ON qi.work_order_id = wo.id 
ORDER BY wo.number, qi.inspection_type;

-- Expected Result: 4 work orders = 4 final inspections (1:1 ratio)
