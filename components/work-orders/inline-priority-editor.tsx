"use client"

import { useState } from "react"
import { Bad<PERSON> } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { ArrowUp, ArrowDown, Minus, Alert<PERSON><PERSON>gle, Loader2, <PERSON>, X } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface InlinePriorityEditorProps {
  workOrderId: string
  currentPriority: string
  onPriorityChange: (newPriority: string) => void
  disabled?: boolean
}

const PRIORITY_OPTIONS = [
  { value: "low", label: "Low", icon: ArrowDown, variant: "secondary" as const, color: "text-green-600" },
  { value: "normal", label: "Normal", icon: Minus, variant: "outline" as const, color: "text-gray-600" },
  { value: "high", label: "High", icon: ArrowUp, variant: "default" as const, color: "text-orange-600" },
  { value: "urgent", label: "Urgent", icon: <PERSON><PERSON><PERSON><PERSON><PERSON>, variant: "destructive" as const, color: "text-red-600" },
]

export function InlinePriorityEditor({ 
  workOrderId, 
  currentPriority, 
  onPriorityChange, 
  disabled = false 
}: InlinePriorityEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [tempPriority, setTempPriority] = useState(currentPriority || "normal")
  const { success: toastSuccess, error: toastError } = useSafeToast()

  const currentPriorityConfig = PRIORITY_OPTIONS.find(p => p.value === (currentPriority || "normal")) || PRIORITY_OPTIONS[1]
  const PriorityIcon = currentPriorityConfig.icon

  const handlePriorityUpdate = async (newPriority: string) => {
    if (newPriority === currentPriority) {
      setIsEditing(false)
      return
    }

    setIsUpdating(true)
    
    try {
      // ✅ PROFESSIONAL ERP: Optimistic update with rollback
      const originalPriority = currentPriority
      onPriorityChange(newPriority) // Optimistic update
      
      const response = await fetch(`/api/production/work-orders/${workOrderId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ priority: newPriority }),
      })

      if (!response.ok) {
        throw new Error("Failed to update priority")
      }

      const newPriorityConfig = PRIORITY_OPTIONS.find(p => p.value === newPriority)
      toastSuccess(`Priority updated to ${newPriorityConfig?.label || newPriority}`)
      
    } catch (error) {
      console.error("Priority update failed:", error)
      onPriorityChange(currentPriority) // Rollback on error
      toastError("Failed to update priority")
    } finally {
      setIsUpdating(false)
      setIsEditing(false)
    }
  }

  const handleCancel = () => {
    setTempPriority(currentPriority || "normal")
    setIsEditing(false)
  }

  if (disabled || isUpdating) {
    return (
      <Badge variant={currentPriorityConfig.variant} className="relative">
        {isUpdating && <Loader2 className="mr-1 h-3 w-3 animate-spin" />}
        <PriorityIcon className="mr-1 h-3 w-3" />
        {currentPriorityConfig.label}
      </Badge>
    )
  }

  if (isEditing) {
    return (
      <div className="flex items-center gap-2">
        <Select value={tempPriority} onValueChange={setTempPriority}>
          <SelectTrigger className="w-28 h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {PRIORITY_OPTIONS.map((priority) => {
              const Icon = priority.icon
              return (
                <SelectItem key={priority.value} value={priority.value}>
                  <div className="flex items-center gap-2">
                    <Icon className={`h-3 w-3 ${priority.color}`} />
                    {priority.label}
                  </div>
                </SelectItem>
              )
            })}
          </SelectContent>
        </Select>
        <Button 
          size="sm" 
          variant="ghost" 
          onClick={() => handlePriorityUpdate(tempPriority)}
          className="h-8 w-8 p-0"
        >
          <Check className="h-3 w-3" />
        </Button>
        <Button 
          size="sm" 
          variant="ghost" 
          onClick={handleCancel}
          className="h-8 w-8 p-0"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    )
  }

  return (
    <Badge 
      variant={currentPriorityConfig.variant}
      className="cursor-pointer hover:opacity-80 transition-opacity"
      onClick={() => setIsEditing(true)}
    >
      <PriorityIcon className="mr-1 h-3 w-3" />
      {currentPriorityConfig.label}
    </Badge>
  )
}
