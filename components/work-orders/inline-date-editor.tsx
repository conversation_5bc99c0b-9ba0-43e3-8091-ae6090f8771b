"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Loader2, Check, X, Calendar } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface InlineDateEditorProps {
  workOrderId: string
  currentDate: string | null
  onDateChange: (newDate: string | null) => void
  disabled?: boolean
}

export function InlineDateEditor({ 
  workOrderId, 
  currentDate, 
  onDateChange, 
  disabled = false 
}: InlineDateEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [tempDate, setTempDate] = useState(currentDate || "")
  const [error, setError] = useState<string | null>(null)
  const { success: toastSuccess, error: toastError } = useSafeToast()

  const formatDisplayDate = (dateStr: string | null): string => {
    if (!dateStr) return "Not set"
    try {
      return new Date(dateStr).toLocaleDateString()
    } catch {
      return dateStr
    }
  }

  const formatInputDate = (dateStr: string | null): string => {
    if (!dateStr) return ""
    try {
      return new Date(dateStr).toISOString().split('T')[0]
    } catch {
      return ""
    }
  }

  const validateDate = (value: string): boolean => {
    if (!value) return true // Allow empty dates
    const date = new Date(value)
    return !isNaN(date.getTime()) && date >= new Date(new Date().setHours(0, 0, 0, 0))
  }

  const handleDateUpdate = async (newDate: string) => {
    const dateValue = newDate || null
    
    if (dateValue === currentDate) {
      setIsEditing(false)
      return
    }

    if (newDate && !validateDate(newDate)) {
      setError("Due date cannot be in the past")
      return
    }

    setError(null)
    setIsUpdating(true)
    
    try {
      // ✅ PROFESSIONAL ERP: Optimistic update with rollback
      const originalDate = currentDate
      onDateChange(dateValue) // Optimistic update
      
      const response = await fetch(`/api/production/work-orders/${workOrderId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ due_date: dateValue }),
      })

      if (!response.ok) {
        throw new Error("Failed to update due date")
      }

      toastSuccess(dateValue ? `Due date updated to ${formatDisplayDate(dateValue)}` : "Due date cleared")
      
    } catch (error) {
      console.error("Date update failed:", error)
      onDateChange(currentDate) // Rollback on error
      toastError("Failed to update due date")
    } finally {
      setIsUpdating(false)
      setIsEditing(false)
    }
  }

  const handleCancel = () => {
    setTempDate(currentDate || "")
    setError(null)
    setIsEditing(false)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleDateUpdate(tempDate)
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  if (disabled || isUpdating) {
    return (
      <div className="flex items-center gap-2">
        {isUpdating && <Loader2 className="h-3 w-3 animate-spin" />}
        <Calendar className="h-4 w-4 text-muted-foreground" />
        <span>{formatDisplayDate(currentDate)}</span>
      </div>
    )
  }

  if (isEditing) {
    return (
      <div className="flex items-center gap-2">
        <div className="space-y-1">
          <Input
            value={formatInputDate(tempDate)}
            onChange={(e) => {
              setTempDate(e.target.value)
              setError(null)
            }}
            onKeyDown={handleKeyPress}
            className={`w-36 h-8 ${error ? 'border-red-500' : ''}`}
            type="date"
            autoFocus
          />
          {error && (
            <p className="text-xs text-red-500">{error}</p>
          )}
        </div>
        <Button 
          size="sm" 
          variant="ghost" 
          onClick={() => handleDateUpdate(tempDate)}
          className="h-8 w-8 p-0"
          disabled={!!error}
        >
          <Check className="h-3 w-3" />
        </Button>
        <Button 
          size="sm" 
          variant="ghost" 
          onClick={handleCancel}
          className="h-8 w-8 p-0"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    )
  }

  return (
    <div 
      className="flex items-center gap-2 cursor-pointer hover:bg-gray-100 px-2 py-1 rounded transition-colors"
      onClick={() => setIsEditing(true)}
    >
      <Calendar className="h-4 w-4 text-muted-foreground" />
      <span>{formatDisplayDate(currentDate)}</span>
    </div>
  )
}
