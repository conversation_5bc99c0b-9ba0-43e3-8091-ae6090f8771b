"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Loader2, Check, X } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface InlineQuantityEditorProps {
  workOrderId: string
  currentQuantity: string
  onQuantityChange: (newQuantity: string) => void
  disabled?: boolean
}

export function InlineQuantityEditor({ 
  workOrderId, 
  currentQuantity, 
  onQuantityChange, 
  disabled = false 
}: InlineQuantityEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [tempQuantity, setTempQuantity] = useState(currentQuantity)
  const [error, setError] = useState<string | null>(null)
  const { success: toastSuccess, error: toastError } = useSafeToast()

  const validateQuantity = (value: string): boolean => {
    const num = parseFloat(value)
    return !isNaN(num) && num > 0 && Number.isFinite(num)
  }

  const handleQuantityUpdate = async (newQuantity: string) => {
    if (newQuantity === currentQuantity) {
      setIsEditing(false)
      return
    }

    if (!validateQuantity(newQuantity)) {
      setError("Quantity must be a positive number")
      return
    }

    setError(null)
    setIsUpdating(true)
    
    try {
      // ✅ PROFESSIONAL ERP: Optimistic update with rollback
      const originalQuantity = currentQuantity
      onQuantityChange(newQuantity) // Optimistic update
      
      const response = await fetch(`/api/production/work-orders/${workOrderId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ qty: newQuantity }),
      })

      if (!response.ok) {
        throw new Error("Failed to update quantity")
      }

      toastSuccess(`Quantity updated to ${newQuantity}`)
      
    } catch (error) {
      console.error("Quantity update failed:", error)
      onQuantityChange(currentQuantity) // Rollback on error
      toastError("Failed to update quantity")
    } finally {
      setIsUpdating(false)
      setIsEditing(false)
    }
  }

  const handleCancel = () => {
    setTempQuantity(currentQuantity)
    setError(null)
    setIsEditing(false)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleQuantityUpdate(tempQuantity)
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  if (disabled || isUpdating) {
    return (
      <div className="flex items-center gap-2">
        {isUpdating && <Loader2 className="h-3 w-3 animate-spin" />}
        <span className="font-medium">{currentQuantity}</span>
      </div>
    )
  }

  if (isEditing) {
    return (
      <div className="flex items-center gap-2">
        <div className="space-y-1">
          <Input
            value={tempQuantity}
            onChange={(e) => {
              setTempQuantity(e.target.value)
              setError(null)
            }}
            onKeyDown={handleKeyPress}
            className={`w-20 h-8 ${error ? 'border-red-500' : ''}`}
            type="number"
            min="0"
            step="any"
            autoFocus
          />
          {error && (
            <p className="text-xs text-red-500">{error}</p>
          )}
        </div>
        <Button 
          size="sm" 
          variant="ghost" 
          onClick={() => handleQuantityUpdate(tempQuantity)}
          className="h-8 w-8 p-0"
          disabled={!!error}
        >
          <Check className="h-3 w-3" />
        </Button>
        <Button 
          size="sm" 
          variant="ghost" 
          onClick={handleCancel}
          className="h-8 w-8 p-0"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    )
  }

  return (
    <span 
      className="cursor-pointer hover:bg-gray-100 px-2 py-1 rounded transition-colors font-medium"
      onClick={() => setIsEditing(true)}
    >
      {currentQuantity}
    </span>
  )
}
