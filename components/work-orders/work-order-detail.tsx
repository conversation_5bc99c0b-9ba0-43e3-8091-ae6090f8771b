"use client"

import { useState } from "react"
import { CheckCircle, XCircle, Clock, AlertTriangle, Edit, ArrowLeft, Calendar, User, Package, Building2, Settings, FileText } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import Link from "next/link"

interface WorkOrderDetailProps {
  workOrder: any
  onRefresh: () => void
  loading?: boolean
}

export function WorkOrderDetail({ workOrder, onRefresh, loading = false }: WorkOrderDetailProps) {
  const { success: toastSuccess, error: toastError } = useSafeToast()
  const { t } = useI18n()

  // ✅ STATUS MANAGEMENT
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4" />
      case "in_progress":
        return <AlertTriangle className="h-4 w-4" />
      case "pending":
        return <Clock className="h-4 w-4" />
      default:
        return <XCircle className="h-4 w-4" />
    }
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "completed":
        return "default"
      case "in_progress":
        return "secondary"
      case "pending":
        return "outline"
      default:
        return "destructive"
    }
  }

  const formatStatus = (status: string) => {
    return status?.replace("_", " ") || "Unknown"
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="grid gap-6 md:grid-cols-2">
          <div className="h-64 bg-gray-200 rounded animate-pulse" />
          <div className="h-64 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* ✅ HEADER WITH ACTIONS */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/production">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Work Orders
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{workOrder.number}</h1>
            <p className="text-muted-foreground">Work Order Details</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/production/${workOrder.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
        </div>
      </div>

      {/* ✅ MAIN CONTENT GRID */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* ✅ WORK ORDER OVERVIEW */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Work Order Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Status:</span>
                <Badge variant={getStatusVariant(workOrder.status)}>
                  {getStatusIcon(workOrder.status)}
                  <span className="ml-1">{formatStatus(workOrder.status)}</span>
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Quantity:</span>
                <span className="text-sm">{workOrder.qty}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Due Date:</span>
                <span className="text-sm">{workOrder.due_date || "Not set"}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Created:</span>
                <span className="text-sm">
                  {workOrder.created_at ? new Date(workOrder.created_at).toLocaleDateString() : "N/A"}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* ✅ SALES CONTRACT INFORMATION */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Sales Contract
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {workOrder.salesContract ? (
              <div className="grid gap-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Contract:</span>
                  <Link 
                    href={`/sales-contracts/${workOrder.salesContract.id}`}
                    className="text-sm text-blue-600 hover:underline"
                  >
                    {workOrder.salesContract.number}
                  </Link>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Customer:</span>
                  <span className="text-sm">
                    {workOrder.salesContract.customer?.name || "N/A"}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Contract Date:</span>
                  <span className="text-sm">
                    {workOrder.salesContract.date || "N/A"}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Status:</span>
                  <Badge variant="outline">
                    {workOrder.salesContract.status || "N/A"}
                  </Badge>
                </div>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No sales contract linked</p>
            )}
          </CardContent>
        </Card>

        {/* ✅ PRODUCT INFORMATION */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Product Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {workOrder.product ? (
              <div className="grid gap-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">SKU:</span>
                  <span className="text-sm font-mono">{workOrder.product.sku}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Name:</span>
                  <span className="text-sm">{workOrder.product.name}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Unit:</span>
                  <span className="text-sm">{workOrder.product.unit}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Category:</span>
                  <span className="text-sm">{workOrder.product.category || "N/A"}</span>
                </div>
                {workOrder.product.description && (
                  <div className="pt-2 border-t">
                    <span className="text-sm font-medium">Description:</span>
                    <p className="text-sm text-muted-foreground mt-1">
                      {workOrder.product.description}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No product information</p>
            )}
          </CardContent>
        </Card>

        {/* ✅ QUALITY INSPECTIONS */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Quality Inspections
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {workOrder.qualityInspections && workOrder.qualityInspections.length > 0 ? (
              <div className="space-y-3">
                {workOrder.qualityInspections.map((inspection: any) => (
                  <div key={inspection.id} className="flex justify-between items-center p-3 border rounded">
                    <div>
                      <p className="text-sm font-medium">{inspection.inspection_type}</p>
                      <p className="text-xs text-muted-foreground">Inspector: {inspection.inspector}</p>
                    </div>
                    <Badge variant={getStatusVariant(inspection.status)}>
                      {inspection.status}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No quality inspections</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
