"use client"

import { useState } from "react"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { FileText, Loader2, Check, X } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface InlineNotesEditorProps {
  workOrderId: string
  currentNotes: string | null
  onNotesChange: (newNotes: string | null) => void
  disabled?: boolean
}

export function InlineNotesEditor({ 
  workOrderId, 
  currentNotes, 
  onNotesChange, 
  disabled = false 
}: InlineNotesEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [tempNotes, setTempNotes] = useState(currentNotes || "")
  const { success: toastSuccess, error: toastError } = useSafeToast()

  const truncateText = (text: string | null, maxLength: number = 50): string => {
    if (!text) return "No notes"
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + "..."
  }

  const handleNotesUpdate = async (newNotes: string) => {
    const notesValue = newNotes.trim() || null
    
    if (notesValue === currentNotes) {
      setIsEditing(false)
      return
    }

    setIsUpdating(true)
    
    try {
      // ✅ PROFESSIONAL ERP: Optimistic update with rollback
      const originalNotes = currentNotes
      onNotesChange(notesValue) // Optimistic update
      
      const response = await fetch(`/api/production/work-orders/${workOrderId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ notes: notesValue }),
      })

      if (!response.ok) {
        throw new Error("Failed to update notes")
      }

      toastSuccess(notesValue ? "Notes updated successfully" : "Notes cleared")
      
    } catch (error) {
      console.error("Notes update failed:", error)
      onNotesChange(currentNotes) // Rollback on error
      toastError("Failed to update notes")
    } finally {
      setIsUpdating(false)
      setIsEditing(false)
    }
  }

  const handleCancel = () => {
    setTempNotes(currentNotes || "")
    setIsEditing(false)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel()
    }
    // Note: Don't handle Enter as it should create new lines in textarea
  }

  if (disabled || isUpdating) {
    return (
      <div className="flex items-center gap-2">
        {isUpdating && <Loader2 className="h-3 w-3 animate-spin" />}
        <FileText className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">
          {truncateText(currentNotes)}
        </span>
      </div>
    )
  }

  if (isEditing) {
    return (
      <div className="space-y-2">
        <Textarea
          value={tempNotes}
          onChange={(e) => setTempNotes(e.target.value)}
          onKeyDown={handleKeyPress}
          className="min-h-20 resize-none"
          placeholder="Add production notes..."
          autoFocus
        />
        <div className="flex items-center gap-2">
          <Button 
            size="sm" 
            variant="ghost" 
            onClick={() => handleNotesUpdate(tempNotes)}
            className="h-8 px-3"
          >
            <Check className="mr-1 h-3 w-3" />
            Save
          </Button>
          <Button 
            size="sm" 
            variant="ghost" 
            onClick={handleCancel}
            className="h-8 px-3"
          >
            <X className="mr-1 h-3 w-3" />
            Cancel
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div 
      className="flex items-center gap-2 cursor-pointer hover:bg-gray-100 px-2 py-1 rounded transition-colors"
      onClick={() => setIsEditing(true)}
      title={currentNotes || "Click to add notes"}
    >
      <FileText className="h-4 w-4 text-muted-foreground" />
      <span className="text-sm text-muted-foreground">
        {truncateText(currentNotes)}
      </span>
    </div>
  )
}
