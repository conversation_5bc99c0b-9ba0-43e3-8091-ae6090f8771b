"use client"

import * as React from "react"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { useI18n } from "@/components/i18n-provider"

interface Product {
  id: string
  name: string
  sku: string
  unit?: string
  price?: string
  description?: string
  category?: string
  status?: string
}

interface WorkOrderProductSelectProps {
  products: Product[]
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  required?: boolean
}

export function WorkOrderProductSelect({
  products,
  value,
  onValueChange,
  placeholder = "Search products...",
  className,
  disabled = false,
  required = false,
}: WorkOrderProductSelectProps) {
  const { t } = useI18n()

  // Convert products to searchable options with professional display
  const productOptions: SearchableSelectOption[] = React.useMemo(() => {
    return products
      .filter(product => product.status === 'active') // Only show active products
      .map((product) => {
        const unitDisplay = product.unit ? ` (${product.unit})` : ""
        const priceDisplay = product.price ? ` • $${product.price}` : ""
        const categoryDisplay = product.category ? ` • ${product.category}` : ""

        return {
          value: product.id,
          label: `${product.sku} - ${product.name}`,
          subtitle: `${unitDisplay}${priceDisplay}${categoryDisplay}`.trim(),
          description: product.description ? `📝 ${product.description}` : undefined,
        }
      })
  }, [products])

  // Get selected product for validation
  const selectedProduct = products.find(p => p.id === value)

  return (
    <div className="space-y-2">
      <SearchableSelect
        options={productOptions}
        value={value}
        onValueChange={onValueChange}
        placeholder={placeholder}
        searchPlaceholder="Search by SKU or product name..."
        emptyMessage="No products found. Try adjusting your search."
        className={className}
        disabled={disabled}
        allowClear={!required}
      />
      
      {/* Selected Product Summary */}
      {selectedProduct && (
        <div className="p-3 bg-muted/50 rounded-md border">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm">{selectedProduct.sku}</span>
                <span className="text-sm text-muted-foreground">•</span>
                <span className="text-sm">{selectedProduct.name}</span>
              </div>
              {selectedProduct.unit && (
                <div className="text-xs text-muted-foreground">
                  Unit: {selectedProduct.unit}
                </div>
              )}
              {selectedProduct.category && (
                <div className="text-xs text-muted-foreground">
                  Category: {selectedProduct.category}
                </div>
              )}
              {selectedProduct.description && (
                <div className="text-xs text-muted-foreground mt-2">
                  {selectedProduct.description}
                </div>
              )}
            </div>
            {selectedProduct.price && (
              <div className="text-sm font-medium text-green-600">
                ${selectedProduct.price}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
