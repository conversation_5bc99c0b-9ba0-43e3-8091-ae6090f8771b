"use client"

import * as React from "react"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { useI18n } from "@/components/i18n-provider"
import { Badge } from "@/components/ui/badge"
import { Calendar, User, FileText } from "lucide-react"

interface SalesContract {
  id: string
  number: string
  date: string
  status: string
  currency?: string
  customer?: {
    id: string
    name: string
    contact_email?: string
  }
}

interface WorkOrderSalesContractSelectProps {
  salesContracts: SalesContract[]
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  required?: boolean
}

export function WorkOrderSalesContractSelect({
  salesContracts,
  value,
  onValueChange,
  placeholder = "Search sales contracts...",
  className,
  disabled = false,
  required = false,
}: WorkOrderSalesContractSelectProps) {
  const { t } = useI18n()

  // Convert sales contracts to searchable options with professional display
  const contractOptions: SearchableSelectOption[] = React.useMemo(() => {
    return salesContracts
      .filter(contract => contract.status !== 'cancelled') // Exclude cancelled contracts
      .map((contract) => {
        const customerDisplay = contract.customer?.name || "No customer"
        const dateDisplay = contract.date ? new Date(contract.date).toLocaleDateString() : ""
        const statusDisplay = contract.status || "draft"
        const currencyDisplay = contract.currency ? ` (${contract.currency})` : ""

        return {
          value: contract.id,
          label: contract.number,
          subtitle: `${customerDisplay} • ${dateDisplay}${currencyDisplay}`,
          description: `Status: ${statusDisplay.charAt(0).toUpperCase() + statusDisplay.slice(1)}`,
        }
      })
  }, [salesContracts])

  // Get selected contract for validation and display
  const selectedContract = salesContracts.find(c => c.id === value)

  const getStatusVariant = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'default'
      case 'pending_approval':
        return 'secondary'
      case 'draft':
        return 'outline'
      case 'cancelled':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const formatStatus = (status: string) => {
    return status?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Draft'
  }

  return (
    <div className="space-y-2">
      <SearchableSelect
        options={contractOptions}
        value={value}
        onValueChange={onValueChange}
        placeholder={placeholder}
        searchPlaceholder="Search by contract number or customer..."
        emptyMessage="No sales contracts found. Try adjusting your search."
        className={className}
        disabled={disabled}
        allowClear={!required}
      />
      
      {/* Selected Contract Summary */}
      {selectedContract && (
        <div className="p-3 bg-muted/50 rounded-md border">
          <div className="space-y-3">
            {/* Contract Header */}
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium text-sm">{selectedContract.number}</span>
                  <Badge variant={getStatusVariant(selectedContract.status)} className="text-xs">
                    {formatStatus(selectedContract.status)}
                  </Badge>
                </div>
                
                {/* Customer Information */}
                {selectedContract.customer && (
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <User className="h-3 w-3" />
                    <span>{selectedContract.customer.name}</span>
                    {selectedContract.customer.contact_email && (
                      <>
                        <span>•</span>
                        <span>{selectedContract.customer.contact_email}</span>
                      </>
                    )}
                  </div>
                )}
                
                {/* Contract Date */}
                {selectedContract.date && (
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3" />
                    <span>Contract Date: {new Date(selectedContract.date).toLocaleDateString()}</span>
                    {selectedContract.currency && (
                      <>
                        <span>•</span>
                        <span>Currency: {selectedContract.currency}</span>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
            
            {/* Status Information */}
            {selectedContract.status === 'draft' && (
              <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
                ⚠️ This contract is still in draft status. Work orders created from draft contracts may need approval.
              </div>
            )}
            
            {selectedContract.status === 'pending_approval' && (
              <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded border border-blue-200">
                ℹ️ This contract is pending approval. Work orders will be created but may be subject to contract changes.
              </div>
            )}
            
            {selectedContract.status === 'approved' && (
              <div className="text-xs text-green-600 bg-green-50 p-2 rounded border border-green-200">
                ✅ This contract is approved and ready for production.
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
