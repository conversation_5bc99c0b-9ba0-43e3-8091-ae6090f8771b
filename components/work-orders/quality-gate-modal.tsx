"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, CheckCircle, Clock, ExternalLink } from "lucide-react"
import { useI18n } from "@/components/i18n-provider"
import Link from "next/link"

interface QualityInspection {
  id: string
  status: string
  inspection_type: string
  inspector: string
  inspection_date: string
  notes?: string
}

interface QualityGateModalProps {
  isOpen: boolean
  onClose: () => void
  workOrderNumber: string
  productName: string
  productSku: string
  qualityInspections: QualityInspection[]
  canComplete: boolean
  onForceComplete?: () => void // For admin override if needed
}

export function QualityGateModal({
  isOpen,
  onClose,
  workOrderNumber,
  productName,
  productSku,
  qualityInspections,
  canComplete,
  onForceComplete
}: QualityGateModalProps) {
  const { t } = useI18n()
  const [isProcessing, setIsProcessing] = useState(false)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "passed":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "pending":
        return <Clock className="h-4 w-4 text-orange-600" />
      case "failed":
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case "passed":
        return "default"
      case "pending":
        return "secondary"
      case "failed":
        return "destructive"
      default:
        return "outline"
    }
  }

  const pendingInspections = qualityInspections.filter(qi => qi.status === "pending")
  const passedInspections = qualityInspections.filter(qi => qi.status === "passed")

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            {t("work_orders.quality_gate.title")}
          </DialogTitle>
          <DialogDescription>
            {t("work_orders.quality_gate.description")}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Work Order Information */}
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">{t("work_orders.quality_gate.work_order_info")}</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">{t("work_orders.table.number")}:</span>
                <span className="ml-2 font-mono">{workOrderNumber}</span>
              </div>
              <div>
                <span className="text-muted-foreground">{t("products.table.name")}:</span>
                <span className="ml-2">{productName}</span>
              </div>
              <div>
                <span className="text-muted-foreground">{t("products.table.sku")}:</span>
                <span className="ml-2 font-mono">{productSku}</span>
              </div>
            </div>
          </div>

          {/* Quality Inspections Status */}
          <div>
            <h4 className="font-medium mb-3">{t("work_orders.quality_gate.inspections_status")}</h4>

            {qualityInspections.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                <Clock className="h-8 w-8 mx-auto mb-2 text-orange-600" />
                <p>{t("work_orders.quality_gate.no_inspections")}</p>
              </div>
            ) : (
              <div className="space-y-2">
                {qualityInspections.map((inspection) => (
                  <div key={inspection.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(inspection.status)}
                      <div>
                        <div className="font-medium">
                          {t(`quality.inspection_types.${inspection.inspection_type}`)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {t("quality.inspector")}: {inspection.inspector}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getStatusVariant(inspection.status)}>
                        {t(`quality.status.${inspection.status}`)}
                      </Badge>
                      <Link href={`/quality/${inspection.id}`}>
                        <Button variant="ghost" size="sm">
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Status Summary */}
          <div className="bg-muted/50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">{t("work_orders.quality_gate.completion_status")}</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  {canComplete
                    ? t("work_orders.quality_gate.can_complete")
                    : t("work_orders.quality_gate.cannot_complete")
                  }
                </p>
              </div>
              <div className="text-right">
                {canComplete ? (
                  <CheckCircle className="h-8 w-8 text-green-600" />
                ) : (
                  <AlertTriangle className="h-8 w-8 text-orange-600" />
                )}
              </div>
            </div>

            {!canComplete && pendingInspections.length > 0 && (
              <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <p className="text-sm text-orange-800">
                  <strong>{t("work_orders.quality_gate.pending_inspections")}:</strong> {pendingInspections.length}
                </p>
                <p className="text-xs text-orange-700 mt-1">
                  {t("work_orders.quality_gate.complete_inspections_first")}
                </p>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            {t("common.close")}
          </Button>

          {!canComplete && (
            <Link href="/quality">
              <Button>
                <ExternalLink className="mr-2 h-4 w-4" />
                {t("work_orders.quality_gate.go_to_quality_control")}
              </Button>
            </Link>
          )}

          {canComplete && onForceComplete && (
            <Button onClick={onForceComplete} disabled={isProcessing}>
              {isProcessing ? t("common.processing") : t("work_orders.quality_gate.complete_work_order")}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
