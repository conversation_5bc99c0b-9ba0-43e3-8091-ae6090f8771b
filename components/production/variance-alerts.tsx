"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Alert<PERSON>riangle, TrendingUp, TrendingDown, Clock, CheckCircle } from "lucide-react"
import Link from "next/link"

interface ProductionVariance {
  workOrderId: string
  workOrderNumber: string
  salesContractId: string
  salesContractNumber: string
  productName: string
  variances: {
    quantity?: { planned: number, actual: number, variance: number }
    delivery?: { planned: string, projected: string, variance: number }
    status?: { contract: string, production: string }
  }
  severity: "low" | "medium" | "high"
  requiresAttention: boolean
}

export function VarianceAlerts() {
  const [variances, setVariances] = useState<ProductionVariance[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // TODO: Implement API call to get production variances
    // This would analyze work orders vs their source contracts
    const mockVariances: ProductionVariance[] = [
      {
        workOrderId: "wo_123",
        workOrderNumber: "WO-250825-5003-D002-40IL",
        salesContractId: "sc_456",
        salesContractNumber: "LOCAL SC-2025-003",
        productName: "MT-IND-002 - local silk1",
        variances: {
          quantity: { planned: 1000, actual: 1200, variance: 20 }
        },
        severity: "medium",
        requiresAttention: true
      }
    ]
    
    setVariances(mockVariances)
    setLoading(false)
  }, [])

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "high": return "destructive"
      case "medium": return "default"
      case "low": return "secondary"
      default: return "outline"
    }
  }

  const getVarianceIcon = (variance: number) => {
    if (variance > 0) return <TrendingUp className="h-4 w-4 text-orange-500" />
    if (variance < 0) return <TrendingDown className="h-4 w-4 text-blue-500" />
    return <CheckCircle className="h-4 w-4 text-green-500" />
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Production Variances
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">Loading variance analysis...</p>
        </CardContent>
      </Card>
    )
  }

  const requiresAttentionCount = variances.filter(v => v.requiresAttention).length

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Production Variances
            {requiresAttentionCount > 0 && (
              <Badge variant="destructive">{requiresAttentionCount}</Badge>
            )}
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {variances.length === 0 ? (
          <div className="text-center py-4">
            <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              No significant variances detected
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {variances.map((variance) => (
              <div
                key={variance.workOrderId}
                className="border rounded-lg p-4 space-y-3"
              >
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Link
                        href={`/production/${variance.workOrderId}`}
                        className="font-medium text-blue-600 hover:underline"
                      >
                        {variance.workOrderNumber}
                      </Link>
                      <Badge variant={getSeverityColor(variance.severity)}>
                        {variance.severity}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Contract: {" "}
                      <Link
                        href={`/sales-contracts/${variance.salesContractId}`}
                        className="text-blue-600 hover:underline"
                      >
                        {variance.salesContractNumber}
                      </Link>
                    </p>
                    <p className="text-sm font-medium">{variance.productName}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {variance.variances.quantity && (
                    <div className="flex items-center gap-2">
                      {getVarianceIcon(variance.variances.quantity.variance)}
                      <div className="text-sm">
                        <div className="font-medium">Quantity Variance</div>
                        <div className="text-muted-foreground">
                          {variance.variances.quantity.planned} → {variance.variances.quantity.actual}
                          ({variance.variances.quantity.variance > 0 ? '+' : ''}{variance.variances.quantity.variance}%)
                        </div>
                      </div>
                    </div>
                  )}

                  {variance.variances.delivery && (
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-orange-500" />
                      <div className="text-sm">
                        <div className="font-medium">Delivery Variance</div>
                        <div className="text-muted-foreground">
                          {variance.variances.delivery.variance} days
                        </div>
                      </div>
                    </div>
                  )}

                  {variance.variances.status && (
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-blue-500" />
                      <div className="text-sm">
                        <div className="font-medium">Status Mismatch</div>
                        <div className="text-muted-foreground">
                          Contract: {variance.variances.status.contract} | 
                          Production: {variance.variances.status.production}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {variance.requiresAttention && (
                  <div className="flex items-center gap-2 pt-2 border-t">
                    <Button size="sm" variant="outline">
                      Request Contract Update
                    </Button>
                    <Button size="sm" variant="ghost">
                      Notify Sales Team
                    </Button>
                    <Button size="sm" variant="ghost">
                      Mark Reviewed
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
