"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogDescription, <PERSON>alogFooter, <PERSON>alogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { Settings, Package, Calendar, AlertTriangle, CheckCircle, RefreshCw, Zap } from "lucide-react"
import { z } from "zod"

interface WorkOrderGenerationDialogProps {
  contract: any
  open?: boolean
  onOpenChange?: (open: boolean) => void
  onSuccess?: () => void
  trigger?: React.ReactNode
}

// ✅ VALIDATION SCHEMA
const generationOptionsSchema = z.object({
  priority: z.enum(["low", "normal", "high", "urgent"]).default("normal"),
  dueDateOffset: z.number().min(1).max(365).optional(),
  autoCreateQualityInspections: z.boolean().default(true),
  notes: z.string().optional(),
})

type GenerationOptions = z.infer<typeof generationOptionsSchema>

export function WorkOrderGenerationDialog({
  contract,
  open: externalOpen,
  onOpenChange: externalOnOpenChange,
  onSuccess,
  trigger
}: WorkOrderGenerationDialogProps) {
  const { success, error } = useSafeToast()
  const { t } = useI18n()

  // Early return if contract is not provided
  if (!contract) {
    return null
  }

  // Extract contract details
  const contractId = contract.id
  const contractNumber = contract.number
  const contractStatus = contract.status

  // Handle external vs internal open state
  const [internalOpen, setInternalOpen] = React.useState(false)
  const open = externalOpen !== undefined ? externalOpen : internalOpen
  const setOpen = externalOnOpenChange || setInternalOpen

  const [loading, setLoading] = React.useState(false)
  const [preview, setPreview] = React.useState<any>(null)
  const [previewLoading, setPreviewLoading] = React.useState(false)

  const [options, setOptions] = React.useState<GenerationOptions>({
    priority: "normal",
    dueDateOffset: 14,
    autoCreateQualityInspections: true,
    notes: "",
  })

  // ✅ LOAD PREVIEW WHEN DIALOG OPENS
  React.useEffect(() => {
    if (open && !preview) {
      loadPreview()
    }
  }, [open])

  const loadPreview = async () => {
    try {
      setPreviewLoading(true)
      const response = await fetch(`/api/contracts/sales/${contractId}/generate-work-orders`)

      if (!response.ok) {
        throw new Error("Failed to load preview")
      }

      const data = await response.json()
      setPreview(data.preview)
    } catch (err) {
      console.error("Error loading preview:", err)
      error(
        "Preview Error",
        "Failed to load work order preview"
      )
    } finally {
      setPreviewLoading(false)
    }
  }

  const handleGenerate = async () => {
    try {
      setLoading(true)

      const response = await fetch(`/api/contracts/sales/${contractId}/generate-work-orders`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(options),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.log("Error response data:", errorData)
        const errorMessage = errorData.message || errorData.error || "Failed to generate work orders"

        // Show professional error toast
        error(
          "Work Orders Already Exist",
          `Work orders have already been generated for contract ${contractNumber}. Each contract can only generate work orders once.`
        )

        // Close dialog and stop loading
        setLoading(false)
        setOpen(false)
        return
      }

      const result = await response.json()

      success(
        "Work Orders Generated!",
        `Successfully created ${result.totalGenerated} work orders for contract ${contractNumber}`
      )

      setOpen(false)
      onSuccess?.()
    } catch (err) {
      console.error("Error generating work orders:", err)
      error(
        "Generation Failed",
        err instanceof Error ? err.message : "Failed to generate work orders"
      )
    } finally {
      setLoading(false)
    }
  }

  const canGenerate = contractStatus === "approved"

  const defaultTrigger = (
    <Button
      variant={canGenerate ? "default" : "outline"}
      size="sm"
      disabled={!canGenerate}
    >
      <Zap className="mr-2 h-4 w-4" />
      Generate Work Orders
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>

      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Generate Work Orders
          </DialogTitle>
          <DialogDescription>
            Create production work orders from contract {contractNumber}
          </DialogDescription>
        </DialogHeader>

        {!canGenerate && (
          <div className="p-4 bg-amber-50 border border-amber-200 rounded-md">
            <div className="flex items-center gap-2 text-amber-800">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm font-medium">Contract Not Ready</span>
            </div>
            <p className="text-sm text-amber-700 mt-1">
              Contract must be approved before work orders can be generated. Current status: {contractStatus}
            </p>
          </div>
        )}

        {canGenerate && (
          <div className="space-y-6">
            {/* Preview Section */}
            {previewLoading ? (
              <Card>
                <CardContent className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">Loading preview...</p>
                  </div>
                </CardContent>
              </Card>
            ) : preview ? (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Work Orders to Create</CardTitle>
                  <CardDescription>
                    {preview.totalWorkOrders} work orders will be generated
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {preview.workOrdersToCreate.map((wo: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-md">
                      <div className="flex items-center gap-3">
                        <Package className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">{wo.product.sku} - {wo.product.name}</p>
                          <p className="text-xs text-muted-foreground">Quantity: {wo.quantity}</p>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {wo.operations.length} operations
                      </Badge>
                    </div>
                  ))}
                </CardContent>
              </Card>
            ) : null}

            {/* Options Section */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Generation Options</CardTitle>
                <CardDescription>
                  Configure work order generation settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="priority">Priority</Label>
                    <select
                      id="priority"
                      name="priority"
                      title="Select work order priority level"
                      value={options.priority}
                      onChange={(e) => setOptions(prev => ({ ...prev, priority: e.target.value as any }))}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    >
                      <option value="low">Low Priority</option>
                      <option value="normal">Normal Priority</option>
                      <option value="high">High Priority</option>
                      <option value="urgent">Urgent Priority</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="dueDateOffset" className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Due Date (Days from now)
                    </Label>
                    <Input
                      id="dueDateOffset"
                      type="number"
                      min="1"
                      max="365"
                      value={options.dueDateOffset || ""}
                      onChange={(e) => setOptions(prev => ({
                        ...prev,
                        dueDateOffset: e.target.value ? parseInt(e.target.value) : undefined
                      }))}
                      placeholder="14"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Production Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    value={options.notes}
                    onChange={(e) => setOptions(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Add any special instructions for production..."
                    rows={3}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="autoCreateQualityInspections"
                    name="autoCreateQualityInspections"
                    title="Automatically create quality inspections for work orders"
                    checked={options.autoCreateQualityInspections}
                    onChange={(e) => setOptions(prev => ({
                      ...prev,
                      autoCreateQualityInspections: e.target.checked
                    }))}
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <Label htmlFor="autoCreateQualityInspections" className="text-sm">
                    Auto-create quality inspections
                  </Label>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          {canGenerate && (
            <Button onClick={handleGenerate} disabled={loading || previewLoading}>
              {loading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Generate {preview?.totalWorkOrders || 0} Work Orders
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
