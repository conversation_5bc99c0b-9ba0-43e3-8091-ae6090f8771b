"use client"

import Link from "next/link"
import { useState } from "react"
import { <PERSON><PERSON>, User, LogOut } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>eader, She<PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>rigger } from "@/components/ui/sheet"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import type { ReactNode } from "react"
import { SideNav } from "./side-nav"
import { CommandMenu } from "./command-menu"
import { Breadcrumbs } from "./breadcrumbs"
import { LanguageSwitcher } from "./language-switcher"
import { useI18n } from "./i18n-provider"
import { FCChinaLogo } from "./fc-china-logo"
import { useUser } from '@auth0/nextjs-auth0/client'

export function AppShell({ children }: { children?: ReactNode }) {
  const [open, setOpen] = useState(false)
  const { t } = useI18n()
  const { user, isLoading } = useUser()

  return (
    <div className="min-h-screen bg-muted/40">
      {/* Professional ERP Header - Full Width */}
      <header className="sticky top-0 z-40 bg-background border-b">
        <div className="flex h-14 items-center gap-3 px-4">
          {/* Mobile Menu Button */}
          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild className="lg:hidden">
              <Button variant="ghost" size="icon" aria-label="Open navigation">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-72 p-0">
              <SheetHeader className="px-4 pt-4">
                <SheetTitle className="text-left">
                  <FCChinaLogo size="sm" href="/" />
                </SheetTitle>
              </SheetHeader>
              <SideNav onNavigate={() => setOpen(false)} />
            </SheetContent>
          </Sheet>

          {/* Professional FC-CHINA Logo */}
          <FCChinaLogo size="sm" href="/" />

          {/* Header Actions */}
          <div className="ml-auto flex items-center gap-2">
            <CommandMenu />
            <LanguageSwitcher />

            {/* User Menu */}
            {user && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.picture || ''} alt={user.name || ''} />
                      <AvatarFallback>
                        {user.name?.charAt(0) || user.email?.charAt(0) || 'U'}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{user.name}</p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/company-profile" className="flex items-center">
                      <User className="mr-2 h-4 w-4" />
                      <span>Company Profile</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/api/auth/logout" className="flex items-center">
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </header>

      {/* Professional ERP Layout - Full Width with Fixed Sidebar */}
      <div className="flex">
        {/* Desktop Sidebar - Fixed Left */}
        <div className="hidden lg:block w-64 border-r bg-background">
          <div className="sticky top-14 h-[calc(100vh-3.5rem)] overflow-y-auto">
            <SideNav />
          </div>
        </div>

        {/* Main Content Area - Full Width */}
        <main className="flex-1 min-w-0 p-4 md:p-6">
          <Breadcrumbs />
          {children}
        </main>
      </div>
    </div>
  )
}
