"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Download,
  FileText,
  X,
  AlertCircle,
  File,
  FileSpreadsheet,
  FileImage
} from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"

interface DocumentPreviewModalProps {
  isOpen: boolean
  onClose: () => void
  filename: string
  inspectionId: string
  fileType?: string
}

export function DocumentPreviewModal({
  isOpen,
  onClose,
  filename,
  inspectionId,
  fileType
}: DocumentPreviewModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { success: toastSuccess, error: toastError } = useSafeToast()
  const { t } = useI18n()

  // ✅ PROFESSIONAL ERP: Determine file type and icon
  const getFileInfo = (filename: string) => {
    const extension = filename.toLowerCase().split('.').pop() || ''

    const fileTypeMap: Record<string, { icon: any; type: string; previewable: boolean }> = {
      'pdf': { icon: FileText, type: 'PDF Document', previewable: true },
      'doc': { icon: FileText, type: 'Word Document', previewable: false },
      'docx': { icon: FileText, type: 'Word Document', previewable: false },
      'xls': { icon: FileSpreadsheet, type: 'Excel Spreadsheet', previewable: false },
      'xlsx': { icon: FileSpreadsheet, type: 'Excel Spreadsheet', previewable: false },
      'txt': { icon: File, type: 'Text Document', previewable: true },
      'jpg': { icon: FileImage, type: 'Image', previewable: true },
      'jpeg': { icon: FileImage, type: 'Image', previewable: true },
      'png': { icon: FileImage, type: 'Image', previewable: true },
      'gif': { icon: FileImage, type: 'Image', previewable: true },
    }

    return fileTypeMap[extension] || { icon: File, type: 'Document', previewable: false }
  }

  const fileInfo = getFileInfo(filename)
  const FileIcon = fileInfo.icon

  // ✅ PROFESSIONAL ERP: Download file handler
  const handleDownload = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/quality/inspections/${inspectionId}/files/${filename}`)

      if (!response.ok) {
        throw new Error('Download failed')
      }

      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toastSuccess(
        t("quality.attachments.download_success"),
        `${filename} ${t("quality.attachments.download_success_desc")}`
      )
    } catch (error) {
      console.error('Download error:', error)
      toastError(
        t("quality.attachments.download_failed"),
        t("quality.attachments.download_failed_desc")
      )
    } finally {
      setIsLoading(false)
    }
  }

  // ✅ PROFESSIONAL ERP: Render preview content based on file type
  const renderPreviewContent = () => {
    const extension = filename.toLowerCase().split('.').pop() || ''

    if (extension === 'pdf') {
      return (
        <div className="w-full h-[600px] border rounded-lg overflow-hidden">
          <iframe
            src={`/api/quality/inspections/${inspectionId}/files/${filename}`}
            className="w-full h-full"
            title={`Preview of ${filename}`}
          />
        </div>
      )
    }

    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
      return (
        <div className="flex justify-center items-center p-4">
          <img
            src={`/api/quality/inspections/${inspectionId}/files/${filename}`}
            alt={filename}
            className="max-w-full max-h-[600px] object-contain rounded-lg shadow-lg"
          />
        </div>
      )
    }

    if (extension === 'txt') {
      return (
        <div className="w-full h-[400px] border rounded-lg overflow-hidden">
          <iframe
            src={`/api/quality/inspections/${inspectionId}/files/${filename}`}
            className="w-full h-full"
            title={`Preview of ${filename}`}
          />
        </div>
      )
    }

    // ✅ FALLBACK: Non-previewable files
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4">
        <FileIcon className="h-16 w-16 text-muted-foreground" />
        <div className="text-center">
          <h3 className="text-lg font-medium">{filename}</h3>
          <p className="text-muted-foreground">{fileInfo.type}</p>
        </div>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            This file type cannot be previewed in the browser. Click the download button to view the file.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <FileIcon className="h-5 w-5" />
              {filename}
            </DialogTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {isLoading ? t("quality.attachments.uploading") : t("quality.attachments.download")}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-auto">
          {renderPreviewContent()}
        </div>
      </DialogContent>
    </Dialog>
  )
}
