"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { Archive, AlertTriangle } from "lucide-react"

interface ArchiveInspectionDialogProps {
  inspectionId: string
  isOpen: boolean
  onClose: () => void
  onArchived: () => void
}

const ARCHIVE_REASONS = [
  { value: "cancelled_by_customer", label: "Cancelled by Customer" },
  { value: "inspection_no_longer_needed", label: "Inspection No Longer Needed" },
  { value: "duplicate_inspection", label: "Duplicate Inspection" },
  { value: "data_entry_error", label: "Data Entry Error" },
  { value: "process_change", label: "Process Change" },
  { value: "other", label: "Other" },
]

export function ArchiveInspectionDialog({
  inspectionId,
  isOpen,
  onClose,
  onArchived,
}: ArchiveInspectionDialogProps) {
  const [reason, setReason] = useState("")
  const [notes, setNotes] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const { success: toastSuccess, error: toastError } = useSafeToast()

  const handleArchive = async () => {
    if (!reason) {
      toastError("Please select a reason for archiving this inspection.")
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch(`/api/quality/inspections/${inspectionId}/archive`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ reason, notes }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to archive inspection")
      }

      toastSuccess("Inspection archived successfully")

      onArchived()
      onClose()
      setReason("")
      setNotes("")

      // Force page refresh to show updated data
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    } catch (error) {
      console.error("Archive error:", error)
      toastError(error instanceof Error ? error.message : "Failed to archive inspection")
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    if (!isLoading) {
      onClose()
      setReason("")
      setNotes("")
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Archive className="h-5 w-5 text-orange-600" />
            Archive Inspection
          </DialogTitle>
          <DialogDescription>
            This inspection will be archived instead of deleted to maintain compliance and audit trails.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <p className="text-sm text-yellow-800">
              Archived inspections can be restored if needed for compliance purposes.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Archive Reason *</Label>
            <Select value={reason} onValueChange={setReason}>
              <SelectTrigger>
                <SelectValue placeholder="Select a reason for archiving" />
              </SelectTrigger>
              <SelectContent>
                {ARCHIVE_REASONS.map((reasonOption) => (
                  <SelectItem key={reasonOption.value} value={reasonOption.value}>
                    {reasonOption.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Additional Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Provide additional context for archiving this inspection..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleArchive} disabled={isLoading || !reason}>
            {isLoading ? "Archiving..." : "Archive Inspection"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
