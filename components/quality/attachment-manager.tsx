"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { DocumentPreviewModal } from "./document-preview-modal"
import {
  Upload,
  File,
  Image,
  X,
  Download,
  Camera,
  Paperclip,
  Eye,
  FileText,
  FileSpreadsheet
} from "lucide-react"

interface AttachmentManagerProps {
  inspectionId: string
  attachments?: string[]
  photos?: string[]
  onAttachmentsUpdate?: (attachments: string[], photos: string[]) => void
  readOnly?: boolean
}

export function AttachmentManager({
  inspectionId,
  attachments = [],
  photos = [],
  onAttachmentsUpdate,
  readOnly = false
}: AttachmentManagerProps) {
  const [uploading, setUploading] = useState(false)
  const [previewFile, setPreviewFile] = useState<string | null>(null)
  const { success: toastSuccess, error: toastError } = useSafeToast()
  const { t } = useI18n()

  // ✅ PROFESSIONAL ERP: Get file type and icon
  const getFileIcon = (filename: string) => {
    const extension = filename.toLowerCase().split('.').pop() || ''
    const iconMap: Record<string, any> = {
      'pdf': FileText,
      'doc': FileText,
      'docx': FileText,
      'xls': FileSpreadsheet,
      'xlsx': FileSpreadsheet,
      'txt': File,
    }
    return iconMap[extension] || File
  }

  // ✅ PROFESSIONAL ERP: Download file handler
  const handleDownload = async (filename: string) => {
    try {
      const response = await fetch(`/api/quality/inspections/${inspectionId}/files/${filename}`)

      if (!response.ok) {
        throw new Error('Download failed')
      }

      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toastSuccess(
        t("quality.attachments.download_success"),
        `${filename} ${t("quality.attachments.download_success_desc")}`
      )
    } catch (error) {
      console.error('Download error:', error)
      toastError(
        t("quality.attachments.download_failed"),
        t("quality.attachments.download_failed_desc")
      )
    }
  }

  // ✅ PROFESSIONAL ERP: File upload handler
  const handleFileUpload = async (files: FileList | null, type: 'attachment' | 'photo') => {
    if (!files || files.length === 0) return

    setUploading(true)

    try {
      const formData = new FormData()
      Array.from(files).forEach((file) => {
        formData.append('files', file)
      })
      formData.append('type', type)
      formData.append('inspectionId', inspectionId)

      const response = await fetch(`/api/quality/inspections/${inspectionId}/attachments`, {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const result = await response.json()

      toastSuccess(
        t("quality.attachments.upload_success"),
        `${files.length} ${t("quality.attachments.upload_success_desc")}`
      )

      // Update parent component
      if (onAttachmentsUpdate) {
        onAttachmentsUpdate(result.attachments || [], result.photos || [])
      }

    } catch (error) {
      console.error('Upload error:', error)
      toastError(
        "Upload Failed",
        "Failed to upload files. Please try again."
      )
    } finally {
      setUploading(false)
    }
  }

  // ✅ PROFESSIONAL ERP: Remove attachment
  const handleRemoveAttachment = async (filename: string, type: 'attachment' | 'photo') => {
    try {
      const response = await fetch(`/api/quality/inspections/${inspectionId}/attachments`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filename, type }),
      })

      if (!response.ok) {
        throw new Error('Remove failed')
      }

      const result = await response.json()

      toastSuccess(
        t("quality.attachments.remove_success"),
        t("quality.attachments.remove_success_desc")
      )

      // Update parent component
      if (onAttachmentsUpdate) {
        onAttachmentsUpdate(result.attachments || [], result.photos || [])
      }

    } catch (error) {
      console.error('Remove error:', error)
      toastError(
        t("quality.attachments.remove_failed"),
        t("quality.attachments.remove_failed_desc")
      )
    }
  }

  return (
    <div className="space-y-6">
      {/* ✅ DOCUMENT ATTACHMENTS */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Paperclip className="h-5 w-5" />
            {t("quality.attachments.documents.title")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!readOnly && (
            <div>
              <Label htmlFor="documents">{t("quality.attachments.documents.upload")}</Label>
              <div className="mt-2">
                <Input
                  id="documents"
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
                  onChange={(e) => handleFileUpload(e.target.files, 'attachment')}
                  disabled={uploading}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {t("quality.attachments.documents.formats")}
                </p>
              </div>
            </div>
          )}

          {attachments.length > 0 ? (
            <div className="space-y-2">
              {attachments.map((attachment, index) => {
                const FileIcon = getFileIcon(attachment)
                return (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <FileIcon className="h-4 w-4 text-blue-600 flex-shrink-0" />
                      <button
                        type="button"
                        onClick={() => setPreviewFile(attachment)}
                        className="text-sm font-medium text-left hover:text-blue-600 transition-colors truncate"
                        title={attachment}
                      >
                        {attachment}
                      </button>
                    </div>
                    <div className="flex items-center gap-1 flex-shrink-0">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setPreviewFile(attachment)}
                        title={t("quality.attachments.preview")}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDownload(attachment)}
                        title={t("quality.attachments.download")}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      {!readOnly && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveAttachment(attachment, 'attachment')}
                          className="text-destructive hover:text-destructive"
                          title={t("quality.attachments.remove")}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">{t("quality.attachments.documents.none")}</p>
          )}
        </CardContent>
      </Card>

      {/* ✅ PHOTO ATTACHMENTS */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            {t("quality.attachments.photos.title")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!readOnly && (
            <div>
              <Label htmlFor="photos">{t("quality.attachments.photos.upload")}</Label>
              <div className="mt-2">
                <Input
                  id="photos"
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => handleFileUpload(e.target.files, 'photo')}
                  disabled={uploading}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {t("quality.attachments.photos.formats")}
                </p>
              </div>
            </div>
          )}

          {photos.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {photos.map((photo, index) => (
                <div key={index} className="relative group">
                  <div className="aspect-square bg-muted rounded-lg overflow-hidden cursor-pointer">
                    <img
                      src={`/api/quality/inspections/${inspectionId}/files/${photo}`}
                      alt={`Inspection photo ${index + 1}`}
                      className="w-full h-full object-cover hover:scale-105 transition-transform"
                      onClick={() => setPreviewFile(photo)}
                    />
                  </div>

                  {/* Action buttons */}
                  <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => setPreviewFile(photo)}
                      title={t("quality.attachments.preview")}
                      className="h-8 w-8 p-0"
                    >
                      <Eye className="h-3 w-3" />
                    </Button>
                  </div>

                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => handleDownload(photo)}
                      title={t("quality.attachments.download")}
                      className="h-8 w-8 p-0"
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                    {!readOnly && (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleRemoveAttachment(photo, 'photo')}
                        title={t("quality.attachments.remove")}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">{t("quality.attachments.photos.none")}</p>
          )}
        </CardContent>
      </Card>

      {uploading && (
        <div className="flex items-center justify-center p-4 bg-muted rounded-lg">
          <Upload className="h-4 w-4 animate-pulse mr-2" />
          <span className="text-sm">{t("quality.attachments.uploading")}</span>
        </div>
      )}

      {/* ✅ DOCUMENT PREVIEW MODAL */}
      {previewFile && (
        <DocumentPreviewModal
          isOpen={!!previewFile}
          onClose={() => setPreviewFile(null)}
          filename={previewFile}
          inspectionId={inspectionId}
        />
      )}
    </div>
  )
}
