"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { <PERSON>ert<PERSON>riangle, RotateCcw } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface UnarchiveInspectionDialogProps {
  isOpen: boolean
  onClose: () => void
  onUnarchived: () => void
  inspectionId: string
  archiveInfo: {
    reason: string
    archivedAt: string
    archivedBy: string
  }
}

export function UnarchiveInspectionDialog({
  isOpen,
  onClose,
  onUnarchived,
  inspectionId,
  archiveInfo
}: UnarchiveInspectionDialogProps) {
  const [businessJustification, setBusinessJustification] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { success: toastSuccess, error: toastError } = useSafeToast()

  const handleUnarchive = async () => {
    if (!businessJustification.trim()) {
      toastError("Business Justification Required", "Please provide a reason for unarchiving this inspection")
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/quality/inspections/${inspectionId}/unarchive`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          businessJustification: businessJustification.trim(),
          originalArchiveReason: archiveInfo.reason,
          originalArchivedBy: archiveInfo.archivedBy,
          originalArchivedAt: archiveInfo.archivedAt
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to unarchive inspection")
      }

      toastSuccess("Inspection Unarchived", "The inspection has been successfully restored and is now active")

      onUnarchived()
      onClose()
      setBusinessJustification("")

      // ✅ ENSURE DATA REFRESH: Force page refresh to show updated data (same as archive dialog)
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    } catch (error) {
      console.error("Unarchive error:", error)
      toastError("Unarchive Failed", error instanceof Error ? error.message : "Failed to unarchive inspection")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <RotateCcw className="h-5 w-5 text-blue-600" />
            Unarchive Quality Inspection
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Archive History */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1 text-sm">
                <div><strong>Originally archived:</strong> {archiveInfo.archivedAt}</div>
                <div><strong>Archived by:</strong> {archiveInfo.archivedBy}</div>
                <div><strong>Archive reason:</strong> {archiveInfo.reason}</div>
              </div>
            </AlertDescription>
          </Alert>

          {/* Business Justification */}
          <div className="space-y-2">
            <Label htmlFor="justification">
              Business Justification <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="justification"
              placeholder="Explain why this inspection needs to be unarchived (e.g., 'Customer requested re-inspection', 'Archive was done in error', 'Quality issue requires investigation')"
              value={businessJustification}
              onChange={(e) => setBusinessJustification(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <p className="text-xs text-muted-foreground">
              This justification will be logged for audit purposes
            </p>
          </div>

          {/* Compliance Warning */}
          <Alert className="border-amber-200 bg-amber-50">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              <strong>Compliance Notice:</strong> Unarchiving will create a permanent audit trail.
              Ensure this action complies with your quality management system requirements.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUnarchive}
            disabled={isSubmitting || !businessJustification.trim()}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isSubmitting ? "Unarchiving..." : "Unarchive Inspection"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
