"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Clock, AlertTriangle, CheckCircle, XCircle, Loader2, Check, X, PlayCircle } from "lucide-react"
import { useSafeToast } from "@/hooks/use-safe-toast"

interface InlineStatusEditorProps {
  inspectionId: string
  currentStatus: string
  onStatusChange: (newStatus: string) => void
  disabled?: boolean
}

// ✅ MANUFACTURING ERP: Quality inspection status progression
const STATUS_OPTIONS = [
  { value: "pending", label: "Pending", icon: Clock, color: "bg-gray-100 text-gray-800" },
  { value: "in-progress", label: "In Progress", icon: PlayCircle, color: "bg-blue-100 text-blue-800" },
  { value: "passed", label: "Passed", icon: CheckCircle, color: "bg-green-100 text-green-800" },
  { value: "failed", label: "Failed", icon: XCircle, color: "bg-red-100 text-red-800" },
]

export function InlineStatusEditor({
  inspectionId,
  currentStatus,
  onStatusChange,
  disabled = false
}: InlineStatusEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [tempStatus, setTempStatus] = useState(currentStatus)
  const { success: toastSuccess, error: toastError } = useSafeToast()

  const currentStatusConfig = STATUS_OPTIONS.find(s => s.value === currentStatus) || STATUS_OPTIONS[0]
  const StatusIcon = currentStatusConfig.icon

  const handleStatusUpdate = async (newStatus: string) => {
    if (newStatus === currentStatus) {
      setIsEditing(false)
      return
    }

    setIsUpdating(true)

    // ✅ PROFESSIONAL ERP: Store original status for rollback
    const originalStatus = currentStatus

    try {
      // ✅ PROFESSIONAL ERP: Optimistic update
      onStatusChange(newStatus)

      // ✅ MANUFACTURING ERP: Quality inspection status update
      const response = await fetch(`/api/quality/inspections/${inspectionId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status: newStatus }),
      })

      if (!response.ok) {
        throw new Error("Failed to update inspection status")
      }

      const newStatusConfig = STATUS_OPTIONS.find(s => s.value === newStatus)
      toastSuccess(`Inspection status updated to ${newStatusConfig?.label || newStatus}`)
      setIsEditing(false)
    } catch (error) {
      // ✅ PROFESSIONAL ERP: Rollback on error
      onStatusChange(originalStatus)
      toastError("Failed to update inspection status")
      console.error("Status update error:", error)
    } finally {
      setIsUpdating(false)
    }
  }

  const handleCancel = () => {
    setTempStatus(currentStatus)
    setIsEditing(false)
  }

  const handleBadgeClick = () => {
    if (!disabled && !isUpdating) {
      setIsEditing(true)
      setTempStatus(currentStatus)
    }
  }

  if (isEditing) {
    return (
      <div className="flex items-center gap-2">
        <Select value={tempStatus} onValueChange={setTempStatus}>
          <SelectTrigger className="w-32 h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {STATUS_OPTIONS.map((status) => {
              const Icon = status.icon
              return (
                <SelectItem key={status.value} value={status.value}>
                  <div className="flex items-center gap-2">
                    <Icon className="h-3 w-3" />
                    {status.label}
                  </div>
                </SelectItem>
              )
            })}
          </SelectContent>
        </Select>
        <Button
          size="sm"
          variant="ghost"
          onClick={() => handleStatusUpdate(tempStatus)}
          className="h-8 w-8 p-0"
          disabled={isUpdating}
        >
          {isUpdating ? <Loader2 className="h-3 w-3 animate-spin" /> : <Check className="h-3 w-3" />}
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={handleCancel}
          className="h-8 w-8 p-0"
          disabled={isUpdating}
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    )
  }

  return (
    <Badge
      className={`${currentStatusConfig.color} cursor-pointer hover:opacity-80 transition-opacity flex items-center gap-1`}
      onClick={handleBadgeClick}
    >
      {isUpdating ? (
        <Loader2 className="h-3 w-3 animate-spin" />
      ) : (
        <StatusIcon className="h-3 w-3" />
      )}
      {currentStatusConfig.label}
    </Badge>
  )
}
