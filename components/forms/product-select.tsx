"use client"

import * as React from "react"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { Plus, Package, DollarSign } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { useI18n } from "@/components/i18n-provider"

interface Product {
  id: string
  name: string
  sku?: string
  unit?: string
  price?: number
  description?: string
  category?: string
}

interface ProductSelectProps {
  products: Product[]
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  onProductAdded?: (product: Product) => void
  showPrice?: boolean
  showAddNew?: boolean
}

// Quick add product form schema
const quickAddSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  sku: z.string().optional(),
  unit: z.string().optional(),
  price: z.number().min(0, "Price must be positive").optional(),
  description: z.string().optional(),
  category: z.string().optional(),
})

export function ProductSelect({
  products,
  value,
  onValueChange,
  placeholder = "Search products...",
  className,
  disabled = false,
  onProductAdded,
  showPrice = true,
  showAddNew = false,
}: ProductSelectProps) {
  const { t } = useI18n()
  const [showAddDialog, setShowAddDialog] = React.useState(false)
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  // Convert products to searchable options with rich display
  const productOptions: SearchableSelectOption[] = React.useMemo(() => {
    return products.map((product) => {
      const priceDisplay = showPrice && product.price ? ` • $${product.price}` : ""
      const unitDisplay = product.unit ? ` (${product.unit})` : ""

      return {
        value: product.id,
        label: product.name,
        subtitle: product.sku ? `${t("contracts.edit.sku_label", { sku: product.sku })}${unitDisplay}${priceDisplay}` : `${unitDisplay}${priceDisplay}`.trim(),
        description: product.description ? `📝 ${product.description}` : undefined,
      }
    })
  }, [products, showPrice])

  // Quick add form
  const form = useForm<z.infer<typeof quickAddSchema>>({
    resolver: zodResolver(quickAddSchema),
    defaultValues: {
      name: "",
      sku: "",
      unit: "",
      price: undefined,
      description: "",
      category: "",
    },
  })

  const handleAddNew = () => {
    setShowAddDialog(true)
    form.reset()
  }

  const onSubmit = async (values: z.infer<typeof quickAddSchema>) => {
    setIsSubmitting(true)
    try {
      const response = await fetch("/api/products", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(values),
      })

      if (response.ok) {
        const result = await response.json()
        const newProduct = result.data

        toast.success("Product added successfully!")
        setShowAddDialog(false)

        // Notify parent component about the new product
        onProductAdded?.(newProduct)

        // Auto-select the newly created product
        onValueChange?.(newProduct.id)
      } else {
        const errorData = await response.json()
        toast.error(errorData.message || "Failed to add product")
      }
    } catch (error) {
      console.error("Error adding product:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get selected product for additional display
  const selectedProduct = products.find(p => p.id === value)

  return (
    <>
      <div className="space-y-2">
        <SearchableSelect
          options={productOptions}
          value={value}
          onValueChange={onValueChange}
          placeholder={placeholder}
          searchPlaceholder={t("samples.edit.product_placeholder")}
          emptyMessage={t("samples.edit.search.no_results")}
          className={className}
          disabled={disabled}
          showAddNew={showAddNew}
          onAddNew={handleAddNew}
          addNewLabel={t("samples.edit.search.add_new_product")}
          allowClear={true}
        />

        {/* Selected Product Details */}
        {selectedProduct && (
          <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
            {selectedProduct.sku && (
              <Badge variant="outline" className="text-xs">
                {t("contracts.edit.sku_label", { sku: selectedProduct.sku })}
              </Badge>
            )}
            {selectedProduct.unit && (
              <Badge variant="outline" className="text-xs">
                {t("contracts.edit.unit_label", { unit: selectedProduct.unit })}
              </Badge>
            )}
            {showPrice && selectedProduct.price && (
              <Badge variant="outline" className="text-xs text-green-600">
                <DollarSign className="h-3 w-3 mr-1" />
                ${selectedProduct.price}
              </Badge>
            )}
            {selectedProduct.category && (
              <Badge variant="outline" className="text-xs">
                {selectedProduct.category}
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Quick Add Product Dialog */}
      {showAddNew && (
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <div className="flex items-center gap-2">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Package className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <DialogTitle>Add New Product</DialogTitle>
                  <DialogDescription>
                    Quickly add a new product to your catalog
                  </DialogDescription>
                </div>
              </div>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem className="col-span-2">
                        <FormLabel>Product Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter product name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="sku"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SKU</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., SILK-001" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="unit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Unit</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., meters, pieces" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Textiles, Electronics" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Product description and specifications"
                          className="resize-none"
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAddDialog(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Adding...
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Product
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      )}
    </>
  )
}
