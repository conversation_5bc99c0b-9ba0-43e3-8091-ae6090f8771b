"use client"

import { useState } from "react"
import { CheckCircle, XCircle, Clock, AlertTriangle, Edit, ArrowLeft, Calendar, User, Package, Building2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import Link from "next/link"

// ✅ ENHANCED SAMPLE DETAIL INTERFACE
interface SampleDetail {
  id: string
  code: string
  name: string
  date: string
  status: "active" | "inactive" | "archived"
  approval_status: "pending" | "approved" | "rejected" | "revision_required"
  sample_type: "development" | "production" | "quality" | "prototype"
  priority: "low" | "normal" | "high" | "urgent"

  // Relationship data
  customer?: {
    id: string
    name: string
    contact_name?: string
    contact_email?: string
    contact_phone?: string
  }
  product?: {
    id: string
    name: string
    sku: string
    unit?: string
    hs_code?: string
  }
  supplier?: {
    id: string
    name: string
    contact_name?: string
    contact_email?: string
  }

  // Business fields
  notes?: string
  quantity?: string
  unit?: string
  specifications?: string
  quality_requirements?: string
  delivery_date?: string
  cost?: string
  currency?: string

  // Approval workflow
  approved_by?: string
  approved_date?: string
  rejection_reason?: string

  // Timestamps
  created_at: string
  updated_at?: string

  // Workflow metadata
  workflow?: {
    current_status: string
    can_approve: boolean
    can_reject: boolean
    can_request_revision: boolean
    next_actions: string[]
  }

  approval_history?: Array<{
    action: string
    timestamp: string
    status: string
    user: string
    notes: string
  }>
}

interface SampleDetailProps {
  sample: SampleDetail
  onUpdate?: () => void
  loading?: boolean
}

// ✅ APPROVAL STATUS BADGE (reused from table)
function ApprovalStatusBadge({ status }: { status: SampleDetail["approval_status"] }) {
  const statusConfig = {
    pending: {
      variant: "secondary" as const,
      icon: Clock,
      className: "bg-yellow-100 text-yellow-800 border-yellow-200",
    },
    approved: {
      variant: "default" as const,
      icon: CheckCircle,
      className: "bg-green-100 text-green-800 border-green-200",
    },
    rejected: {
      variant: "destructive" as const,
      icon: XCircle,
      className: "bg-red-100 text-red-800 border-red-200",
    },
    revision_required: {
      variant: "secondary" as const,
      icon: AlertTriangle,
      className: "bg-orange-100 text-orange-800 border-orange-200",
    },
  }

  const config = statusConfig[status]
  const Icon = config.icon

  return (
    <Badge variant={config.variant} className={config.className}>
      <Icon className="w-4 h-4 mr-2" />
      {status.replace('_', ' ')}
    </Badge>
  )
}

export function SampleDetail({ sample, onUpdate, loading }: SampleDetailProps) {
  const { success, error } = useSafeToast()
  const { t } = useI18n()
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  const handleApprovalAction = async (action: 'approve' | 'reject' | 'revision') => {
    setActionLoading(action)
    try {
      const approvalData = {
        approval_status: action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'revision_required',
        approved_by: 'Current User', // TODO: Get from auth context
        approved_date: new Date().toISOString().split('T')[0],
        approval_reason: `${action} from sample detail view`,
        ...(action === 'reject' && { rejection_reason: 'Rejected from detail view' }),
      }

      const response = await fetch(`/api/samples/${sample.id}/approve`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(approvalData),
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} sample`)
      }

      // ✅ SUCCESS TOAST
      const successTitle = `Sample ${action === 'approve' ? 'Approved' : action === 'reject' ? 'Rejected' : 'Revision Requested'}`
      const successDescription = `Sample has been ${action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'marked for revision'} successfully.`

      if (action === 'reject') {
        error(successTitle, successDescription)
      } else {
        success(successTitle, successDescription)
      }

      onUpdate?.()
    } catch (err) {
      // ✅ ERROR TOAST
      error(
        "Action Failed",
        `Failed to ${action} sample. Please try again.`
      )
    } finally {
      setActionLoading(null)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="grid gap-6 md:grid-cols-2">
          <div className="h-64 bg-gray-200 rounded animate-pulse" />
          <div className="h-64 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* ✅ HEADER WITH ACTIONS */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/samples">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t("samples.view.back")}
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{sample.code}</h1>
            <p className="text-muted-foreground">{sample.name}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <ApprovalStatusBadge status={sample.approval_status} />

          {/* ✅ APPROVAL ACTIONS */}
          {sample.workflow?.can_approve && (
            <Button
              onClick={() => handleApprovalAction('approve')}
              disabled={actionLoading === 'approve'}
              className="bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              {t("samples.actions.approve")}
            </Button>
          )}

          {sample.workflow?.can_reject && (
            <Button
              variant="destructive"
              onClick={() => handleApprovalAction('reject')}
              disabled={actionLoading === 'reject'}
            >
              <XCircle className="h-4 w-4 mr-2" />
              {t("samples.actions.reject")}
            </Button>
          )}

          {sample.workflow?.can_request_revision && (
            <Button
              variant="outline"
              onClick={() => handleApprovalAction('revision')}
              disabled={actionLoading === 'revision'}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Request Revision
            </Button>
          )}

          <Button variant="outline" asChild>
            <Link href={`/samples/${sample.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              {t("samples.view.edit")}
            </Link>
          </Button>
        </div>
      </div>

      {/* ✅ MAIN CONTENT GRID */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* ✅ SAMPLE INFORMATION */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {t("samples.view.sample_info")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("samples.view.sample_type")}</label>
                <div className="mt-1">
                  <Badge variant="outline" className="capitalize">
                    {sample.sample_type}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("samples.view.priority")}</label>
                <div className="mt-1">
                  <Badge variant="outline" className="capitalize">
                    {sample.priority}
                  </Badge>
                </div>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-muted-foreground">{t("samples.view.sample_date")}</label>
              <p className="mt-1 flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                {new Date(sample.date).toLocaleDateString()}
              </p>
            </div>

            {sample.quantity && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("samples.view.quantity")}</label>
                <p className="mt-1">{sample.quantity} {sample.unit}</p>
              </div>
            )}

            {sample.delivery_date && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("samples.view.delivery_date")}</label>
                <p className="mt-1">{new Date(sample.delivery_date).toLocaleDateString()}</p>
              </div>
            )}

            {sample.cost && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("samples.view.cost")}</label>
                <p className="mt-1">{sample.cost} {sample.currency || 'USD'}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* ✅ RELATIONSHIPS */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              {t("samples.view.relationships")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {sample.customer && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("samples.view.customer")}</label>
                <div className="mt-1">
                  <Link
                    href={`/crm?customer=${sample.customer.id}`}
                    className="text-blue-600 hover:underline font-medium"
                  >
                    {sample.customer.name}
                  </Link>
                  {sample.customer.contact_name && (
                    <p className="text-sm text-muted-foreground">
                      {t("samples.view.contact")}: {sample.customer.contact_name}
                    </p>
                  )}
                  {sample.customer.contact_email && (
                    <p className="text-sm text-muted-foreground">
                      {t("samples.view.email")}: {sample.customer.contact_email}
                    </p>
                  )}
                </div>
              </div>
            )}

            {sample.product && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("samples.view.product")}</label>
                <div className="mt-1">
                  <Link
                    href={`/products?product=${sample.product.id}`}
                    className="text-blue-600 hover:underline font-medium"
                  >
                    {sample.product.name}
                  </Link>
                  <p className="text-sm text-muted-foreground">
                    {t("samples.view.sku")}: {sample.product.sku}
                  </p>
                  {sample.product.hs_code && (
                    <p className="text-sm text-muted-foreground">
                      HS Code: {sample.product.hs_code}
                    </p>
                  )}
                </div>
              </div>
            )}

            {sample.supplier && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("samples.view.supplier")}</label>
                <div className="mt-1">
                  <Link
                    href={`/suppliers?supplier=${sample.supplier.id}`}
                    className="text-blue-600 hover:underline font-medium"
                  >
                    {sample.supplier.name}
                  </Link>
                  {sample.supplier.contact_name && (
                    <p className="text-sm text-muted-foreground">
                      {t("samples.view.contact")}: {sample.supplier.contact_name}
                    </p>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* ✅ SPECIFICATIONS AND NOTES */}
      {(sample.specifications || sample.quality_requirements || sample.notes) && (
        <Card>
          <CardHeader>
            <CardTitle>{t("samples.view.specifications")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {sample.specifications && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("samples.view.technical_specs")}</label>
                <div className="mt-1 p-3 bg-gray-50 rounded-md">
                  <pre className="text-sm whitespace-pre-wrap">{sample.specifications}</pre>
                </div>
              </div>
            )}

            {sample.quality_requirements && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("samples.view.quality_requirements")}</label>
                <div className="mt-1 p-3 bg-gray-50 rounded-md">
                  <pre className="text-sm whitespace-pre-wrap">{sample.quality_requirements}</pre>
                </div>
              </div>
            )}

            {sample.notes && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t("samples.view.notes")}</label>
                <div className="mt-1 p-3 bg-gray-50 rounded-md">
                  <pre className="text-sm whitespace-pre-wrap">{sample.notes}</pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* ✅ APPROVAL HISTORY */}
      {sample.approval_history && sample.approval_history.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {t("samples.view.approval_history")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {sample.approval_history.map((entry, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium capitalize">
                        {t(`samples.view.status.${entry.action}`) || entry.action}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {t(`samples.view.status.${entry.status}`) || entry.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {entry.user === 'System'
                        ? t("samples.view.by_system_on")
                        : entry.user === 'Current User'
                          ? t("samples.view.by_current_user_on")
                          : t("samples.view.by_user_on", { user: entry.user })
                      } {new Date(entry.timestamp).toLocaleString()}
                    </p>
                    {entry.notes && (
                      <p className="text-sm mt-1">
                        {t(`samples.view.actions.${entry.notes.toLowerCase().replace(/\s+/g, '_')}`) || entry.notes}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ METADATA */}
      <Card>
        <CardHeader>
          <CardTitle>{t("samples.view.metadata")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <label className="font-medium text-muted-foreground">{t("samples.view.created_date")}</label>
              <p>{new Date(sample.created_at).toLocaleString()}</p>
            </div>
            {sample.updated_at && (
              <div>
                <label className="font-medium text-muted-foreground">Last Updated</label>
                <p>{new Date(sample.updated_at).toLocaleString()}</p>
              </div>
            )}
            {sample.approved_by && (
              <div>
                <label className="font-medium text-muted-foreground">{t("samples.view.approved_by")}</label>
                <p>{sample.approved_by}</p>
              </div>
            )}
            {sample.approved_date && (
              <div>
                <label className="font-medium text-muted-foreground">{t("samples.view.approved_date")}</label>
                <p>{new Date(sample.approved_date).toLocaleDateString()}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
