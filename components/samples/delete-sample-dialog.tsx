"use client"

import { useState } from "react"
import { <PERSON>rash2, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"

interface DeleteSampleDialogProps {
  sampleId: string
  sampleName: string
  sampleCode: string
  onDelete?: () => void
  trigger?: React.ReactNode
  disabled?: boolean
}

export function DeleteSampleDialog({
  sampleId,
  sampleName,
  sampleCode,
  onDelete,
  trigger,
  disabled = false
}: DeleteSampleDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const { success, error } = useSafeToast()
  const { t } = useI18n()

  const handleDelete = async () => {
    if (isDeleting) return

    setIsDeleting(true)

    try {
      const response = await fetch(`/api/samples/${sampleId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Failed to delete sample')
      }

      // ✅ SUCCESS: 204 status means successful deletion (no content to parse)

      // ✅ SUCCESS: Show success toast
      success(
        t("samples.delete.success.title"),
        t("samples.delete.success.description").replace('{name}', sampleName)
      )

      // ✅ CLOSE DIALOG AND TRIGGER CALLBACK
      setIsOpen(false)
      onDelete?.()

    } catch (err) {
      console.error("Error deleting sample:", err)

      // ✅ ERROR: Show error toast
      error(
        t("samples.delete.error.title"),
        err instanceof Error
          ? err.message
          : t("samples.delete.error.description")
      )
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        {trigger || (
          <Button
            variant="ghost"
            size="sm"
            disabled={disabled}
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </AlertDialogTrigger>

      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-100">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <AlertDialogTitle className="text-left">
                {t("samples.delete.dialog.title")}
              </AlertDialogTitle>
            </div>
          </div>
        </AlertDialogHeader>

        <div className="space-y-4">
          <AlertDialogDescription className="text-left">
            {t("samples.delete.dialog.description")}
          </AlertDialogDescription>

          <div className="p-3 bg-gray-50 rounded-md border">
            <div className="text-sm">
              <div className="font-medium text-gray-900">
                {sampleName}
              </div>
              <div className="text-gray-600">
                {t("samples.fields.code")}: {sampleCode}
              </div>
            </div>
          </div>

          <div className="text-sm text-red-600 font-medium">
            {t("samples.delete.dialog.warning")}
          </div>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            {t("common.cancel")}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {isDeleting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                {t("samples.delete.dialog.deleting")}
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                {t("samples.delete.dialog.confirm")}
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
