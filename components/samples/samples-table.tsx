"use client"

import { useState } from "react"
import { Eye, Edit, Trash2, CheckCircle, XCircle, Clock, Al<PERSON><PERSON><PERSON>gle, MoreHorizontal } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { useI18n } from "@/components/i18n-provider"
import { DeleteSampleDialog } from "./delete-sample-dialog"
import Link from "next/link"

// ✅ PROFESSIONAL BIDIRECTIONAL SAMPLE INTERFACE
interface Sample {
  id: string
  code: string
  name: string
  date: string
  status: "active" | "inactive" | "archived"
  approval_status: "pending" | "approved" | "rejected" | "revision_required"
  sample_type: "development" | "production" | "quality" | "prototype"
  priority: "low" | "normal" | "high" | "urgent"

  // ✅ BIDIRECTIONAL WORKFLOW FIELDS
  sample_direction: "outbound" | "inbound" | "internal"
  sample_purpose?: string
  sender_type?: "customer" | "supplier" | "internal"
  receiver_type?: "customer" | "supplier" | "internal"

  // ✅ INBOUND SAMPLE FIELDS
  received_date?: string
  testing_status?: "not_started" | "in_progress" | "completed" | "failed"
  testing_results?: string
  quote_requested?: boolean
  quote_provided?: boolean

  // ✅ RELATIONSHIP FIELDS
  customer?: {
    id: string
    name: string
  }
  product?: {
    id: string
    name: string
    sku: string
  }
  supplier?: {
    id: string
    name: string
  }
  workOrder?: {
    id: string
    number: string
    status: string
  }
  notes?: string
  created_at: string
  updated_at?: string
}

interface SamplesTableProps {
  samples: Sample[]
  onRefresh?: () => void
  onSampleDeleted?: () => void
  loading?: boolean
}

// ✅ APPROVAL STATUS BADGE COMPONENT
function ApprovalStatusBadge({ status }: { status: Sample["approval_status"] }) {
  const statusConfig = {
    pending: {
      variant: "secondary" as const,
      icon: Clock,
      className: "bg-yellow-100 text-yellow-800 border-yellow-200",
    },
    approved: {
      variant: "default" as const,
      icon: CheckCircle,
      className: "bg-green-100 text-green-800 border-green-200",
    },
    rejected: {
      variant: "destructive" as const,
      icon: XCircle,
      className: "bg-red-100 text-red-800 border-red-200",
    },
    revision_required: {
      variant: "secondary" as const,
      icon: AlertTriangle,
      className: "bg-orange-100 text-orange-800 border-orange-200",
    },
  }

  const config = statusConfig[status]
  const Icon = config.icon

  return (
    <Badge variant={config.variant} className={config.className}>
      <Icon className="w-3 h-3 mr-1" />
      {status.replace('_', ' ')}
    </Badge>
  )
}

// ✅ PRIORITY BADGE COMPONENT
function PriorityBadge({ priority }: { priority: Sample["priority"] }) {
  const priorityConfig = {
    low: { variant: "secondary" as const, className: "bg-gray-100 text-gray-800" },
    normal: { variant: "outline" as const, className: "bg-blue-50 text-blue-700" },
    high: { variant: "default" as const, className: "bg-orange-100 text-orange-800" },
    urgent: { variant: "destructive" as const, className: "bg-red-100 text-red-800" },
  }

  const config = priorityConfig[priority]

  return (
    <Badge variant={config.variant} className={config.className}>
      {priority}
    </Badge>
  )
}

// ✅ SAMPLE TYPE BADGE COMPONENT
function SampleTypeBadge({ type }: { type: Sample["sample_type"] }) {
  const typeConfig = {
    development: { className: "bg-purple-100 text-purple-800" },
    production: { className: "bg-blue-100 text-blue-800" },
    quality: { className: "bg-green-100 text-green-800" },
    prototype: { className: "bg-indigo-100 text-indigo-800" },
  }

  return (
    <Badge variant="outline" className={typeConfig[type].className}>
      {type}
    </Badge>
  )
}

// ✅ SAMPLE DIRECTION BADGE COMPONENT
function SampleDirectionBadge({ direction }: { direction: Sample["sample_direction"] }) {
  const directionConfig = {
    outbound: { icon: "📤", className: "bg-blue-100 text-blue-800" },
    inbound: { icon: "📥", className: "bg-green-100 text-green-800" },
    internal: { icon: "🏭", className: "bg-purple-100 text-purple-800" },
  }

  const config = directionConfig[direction]

  return (
    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.className}`}>
      <span>{config.icon}</span>
      <span className="capitalize">{direction}</span>
    </div>
  )
}

export function SamplesTable({ samples, onRefresh, onSampleDeleted, loading }: SamplesTableProps) {
  const { toast } = useSafeToast()
  const { t } = useI18n()
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  const handleQuickApprove = async (sampleId: string) => {
    setActionLoading(sampleId)
    try {
      const response = await fetch(`/api/samples/${sampleId}/approve`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          approval_status: 'approved',
          approved_by: 'Current User', // TODO: Get from auth context
          approved_date: new Date().toISOString().split('T')[0],
          approval_reason: 'Quick approval from samples table',
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to approve sample')
      }

      toast({
        title: "Sample Approved",
        description: "Sample has been approved successfully.",
      })

      onRefresh?.()
    } catch (error) {
      toast({
        title: "Approval Failed",
        description: "Failed to approve sample. Please try again.",
        variant: "destructive",
      })
    } finally {
      setActionLoading(null)
    }
  }

  const handleQuickReject = async (sampleId: string) => {
    setActionLoading(sampleId)
    try {
      const response = await fetch(`/api/samples/${sampleId}/approve`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          approval_status: 'rejected',
          approved_by: 'Current User', // TODO: Get from auth context
          approved_date: new Date().toISOString().split('T')[0],
          rejection_reason: 'Quick rejection from samples table',
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to reject sample')
      }

      toast({
        title: "Sample Rejected",
        description: "Sample has been rejected.",
        variant: "destructive",
      })

      onRefresh?.()
    } catch (error) {
      toast({
        title: "Rejection Failed",
        description: "Failed to reject sample. Please try again.",
        variant: "destructive",
      })
    } finally {
      setActionLoading(null)
    }
  }

  if (loading) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("samples.table.sample")}</TableHead>
              <TableHead>{t("samples.table.direction")}</TableHead>
              <TableHead>{t("samples.table.purpose")}</TableHead>
              <TableHead>{t("samples.table.relationship")}</TableHead>
              <TableHead>{t("samples.table.product")}</TableHead>
              <TableHead>{t("samples.table.status")}</TableHead>
              <TableHead>{t("samples.table.priority")}</TableHead>
              <TableHead>{t("samples.table.created")}</TableHead>
              <TableHead>{t("samples.table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...Array(5)].map((_, i) => (
              <TableRow key={i}>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t("samples.table.sample")}</TableHead>
            <TableHead>{t("samples.table.direction")}</TableHead>
            <TableHead>{t("samples.table.purpose")}</TableHead>
            <TableHead>{t("samples.table.relationship")}</TableHead>
            <TableHead>{t("samples.table.product")}</TableHead>
            <TableHead>{t("samples.table.status")}</TableHead>
            <TableHead>{t("samples.table.priority")}</TableHead>
            <TableHead>{t("samples.table.created")}</TableHead>
            <TableHead>{t("samples.table.actions")}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {samples.length === 0 ? (
            <TableRow>
              <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                {t("samples.table.empty")}
              </TableCell>
            </TableRow>
          ) : (
            samples.map((sample) => (
              <TableRow key={sample.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{sample.code}</div>
                    <div className="text-sm text-muted-foreground">{sample.name}</div>
                  </div>
                </TableCell>
                {/* ✅ DIRECTION COLUMN */}
                <TableCell>
                  <div className="flex items-center gap-1">
                    {sample.sample_direction === 'outbound' && <span className="text-blue-600">📤</span>}
                    {sample.sample_direction === 'inbound' && <span className="text-green-600">📥</span>}
                    {sample.sample_direction === 'internal' && <span className="text-purple-600">🏭</span>}
                    <span className="text-sm capitalize">{sample.sample_direction}</span>
                  </div>
                </TableCell>

                {/* ✅ PURPOSE COLUMN */}
                <TableCell>
                  <div className="text-sm">
                    {sample.sample_purpose ? (
                      <span className="capitalize">{sample.sample_purpose.replace(/_/g, ' ')}</span>
                    ) : (
                      <span className="text-muted-foreground">No purpose</span>
                    )}
                  </div>
                </TableCell>

                {/* ✅ SMART RELATIONSHIP COLUMN */}
                <TableCell>
                  {sample.sample_direction === 'outbound' && sample.customer ? (
                    <div className="flex items-center gap-1">
                      <span className="text-blue-600">→</span>
                      <Link
                        href={`/crm?customer=${sample.customer.id}`}
                        className="text-blue-600 hover:underline text-sm"
                      >
                        {sample.customer.name}
                      </Link>
                    </div>
                  ) : sample.sample_direction === 'inbound' && sample.customer ? (
                    <div className="flex items-center gap-1">
                      <Link
                        href={`/crm?customer=${sample.customer.id}`}
                        className="text-green-600 hover:underline text-sm"
                      >
                        {sample.customer.name}
                      </Link>
                      <span className="text-green-600">→</span>
                    </div>
                  ) : sample.sample_direction === 'inbound' && sample.supplier ? (
                    <div className="flex items-center gap-1">
                      <Link
                        href={`/suppliers?supplier=${sample.supplier.id}`}
                        className="text-green-600 hover:underline text-sm"
                      >
                        {sample.supplier.name}
                      </Link>
                      <span className="text-green-600">→</span>
                    </div>
                  ) : sample.sample_direction === 'internal' ? (
                    <div className="flex items-center gap-1">
                      <span className="text-purple-600">🏭</span>
                      <span className="text-sm text-purple-600">Internal R&D</span>
                    </div>
                  ) : (
                    <span className="text-muted-foreground text-sm">No relationship</span>
                  )}
                </TableCell>

                {/* ✅ PRODUCT COLUMN */}
                <TableCell>
                  {sample.product ? (
                    <Link
                      href={`/products?product=${sample.product.id}`}
                      className="text-blue-600 hover:underline"
                    >
                      <div className="text-sm">{sample.product.name}</div>
                      <div className="text-xs text-muted-foreground">{sample.product.sku}</div>
                    </Link>
                  ) : (
                    <span className="text-muted-foreground text-sm">No product</span>
                  )}
                </TableCell>
                <TableCell>
                  <ApprovalStatusBadge status={sample.approval_status} />
                </TableCell>
                <TableCell>
                  <PriorityBadge priority={sample.priority} />
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {new Date(sample.created_at).toLocaleDateString()}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/samples/${sample.id}`}>
                        <Eye className="h-4 w-4" />
                      </Link>
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/samples/${sample.id}/edit`}>
                            <Edit className="h-4 w-4 mr-2" />
                            {t("samples.actions.edit")}
                          </Link>
                        </DropdownMenuItem>

                        {sample.approval_status === 'pending' && (
                          <>
                            <DropdownMenuItem
                              onClick={() => handleQuickApprove(sample.id)}
                              disabled={actionLoading === sample.id}
                            >
                              <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                              {t("samples.actions.approve")}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleQuickReject(sample.id)}
                              disabled={actionLoading === sample.id}
                            >
                              <XCircle className="h-4 w-4 mr-2 text-red-600" />
                              {t("samples.actions.reject")}
                            </DropdownMenuItem>
                          </>
                        )}

                        <DeleteSampleDialog
                          sampleId={sample.id}
                          sampleName={sample.name}
                          sampleCode={sample.code}
                          onDelete={() => {
                            onSampleDeleted?.()
                            onRefresh?.()
                          }}
                          trigger={
                            <DropdownMenuItem
                              className="text-red-600 cursor-pointer"
                              onSelect={(e) => e.preventDefault()}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              {t("samples.actions.delete")}
                            </DropdownMenuItem>
                          }
                        />
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
