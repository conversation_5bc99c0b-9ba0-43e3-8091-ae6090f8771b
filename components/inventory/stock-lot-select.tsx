"use client"

import * as React from "react"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { Badge } from "@/components/ui/badge"
import { Package, MapPin, Calendar } from "lucide-react"
import { useI18n } from "@/components/i18n-provider"

/**
 * Manufacturing ERP - Stock Lot Selection Component
 * 
 * Features:
 * - Searchable stock lot selection with rich display
 * - Shows product information, quantities, and locations
 * - Filters by availability and status
 * - Professional display with badges and icons
 * - Bilingual support
 */

interface StockLot {
  id: string
  lot_number?: string
  qty: string
  location: string
  status?: string
  quality_status?: string
  expiry_date?: string
  created_at: string
  product: {
    id: string
    sku: string
    name: string
    unit: string
  }
}

interface StockLotSelectProps {
  stockLots: StockLot[]
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  filterByAvailability?: boolean
  filterByStatus?: string[]
  showQuantityInfo?: boolean
  showLocationInfo?: boolean
  showQualityStatus?: boolean
}

export function StockLotSelect({
  stockLots,
  value,
  onValueChange,
  placeholder = "Select stock lot...",
  className,
  disabled = false,
  filterByAvailability = true,
  filterByStatus = ["available"],
  showQuantityInfo = true,
  showLocationInfo = true,
  showQualityStatus = true,
}: StockLotSelectProps) {
  const { t } = useI18n()

  // Filter stock lots based on criteria
  const filteredStockLots = React.useMemo(() => {
    return stockLots.filter((lot) => {
      // Filter by availability
      if (filterByAvailability && parseFloat(lot.qty) <= 0) {
        return false
      }

      // Filter by status
      if (filterByStatus.length > 0 && lot.status && !filterByStatus.includes(lot.status)) {
        return false
      }

      return true
    })
  }, [stockLots, filterByAvailability, filterByStatus])

  // Convert stock lots to searchable options with rich display
  const stockLotOptions: SearchableSelectOption[] = React.useMemo(() => {
    return filteredStockLots.map((lot) => {
      // Build subtitle with key information
      const subtitleParts = []
      
      if (showQuantityInfo) {
        subtitleParts.push(`${parseFloat(lot.qty).toLocaleString()} ${lot.product.unit}`)
      }
      
      if (showLocationInfo && lot.location) {
        subtitleParts.push(`📍 ${lot.location}`)
      }

      if (showQualityStatus && lot.quality_status) {
        const qualityEmoji = lot.quality_status === 'approved' ? '✅' : 
                           lot.quality_status === 'pending' ? '⏳' : 
                           lot.quality_status === 'rejected' ? '❌' : '🔍'
        subtitleParts.push(`${qualityEmoji} ${lot.quality_status}`)
      }

      // Build description with additional details
      const descriptionParts = []
      
      if (lot.lot_number) {
        descriptionParts.push(`Lot: ${lot.lot_number}`)
      }
      
      if (lot.expiry_date) {
        const expiryDate = new Date(lot.expiry_date)
        const isExpiringSoon = expiryDate.getTime() - Date.now() < 30 * 24 * 60 * 60 * 1000 // 30 days
        const expiryEmoji = isExpiringSoon ? '⚠️' : '📅'
        descriptionParts.push(`${expiryEmoji} Expires: ${expiryDate.toLocaleDateString()}`)
      }

      return {
        value: lot.id,
        label: lot.product.name,
        subtitle: `${lot.product.sku} • ${subtitleParts.join(' • ')}`,
        description: descriptionParts.length > 0 ? descriptionParts.join(' • ') : undefined,
      }
    })
  }, [filteredStockLots, showQuantityInfo, showLocationInfo, showQualityStatus])

  // Get selected stock lot for additional display
  const selectedStockLot = filteredStockLots.find(lot => lot.id === value)

  return (
    <div className="space-y-2">
      <SearchableSelect
        options={stockLotOptions}
        value={value}
        onValueChange={onValueChange}
        placeholder={placeholder}
        searchPlaceholder="Search by product name, SKU, or lot number..."
        emptyMessage="No stock lots found matching your criteria"
        className={className}
        disabled={disabled}
        allowClear={true}
      />

      {/* Selected Stock Lot Details */}
      {selectedStockLot && (
        <div className="p-3 bg-muted/30 rounded-lg border">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{selectedStockLot.product.name}</span>
              </div>
              
              <div className="text-sm text-muted-foreground space-y-1">
                <div>SKU: {selectedStockLot.product.sku}</div>
                {selectedStockLot.lot_number && (
                  <div>Lot Number: {selectedStockLot.lot_number}</div>
                )}
                <div className="flex items-center gap-4">
                  <span>Available: {parseFloat(selectedStockLot.qty).toLocaleString()} {selectedStockLot.product.unit}</span>
                  {selectedStockLot.location && (
                    <span className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {selectedStockLot.location}
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-2">
              {selectedStockLot.status && (
                <Badge 
                  variant={selectedStockLot.status === 'available' ? 'default' : 'secondary'}
                  className="text-xs"
                >
                  {selectedStockLot.status}
                </Badge>
              )}
              
              {selectedStockLot.quality_status && (
                <Badge 
                  variant={
                    selectedStockLot.quality_status === 'approved' ? 'default' :
                    selectedStockLot.quality_status === 'pending' ? 'secondary' :
                    selectedStockLot.quality_status === 'rejected' ? 'destructive' :
                    'outline'
                  }
                  className="text-xs"
                >
                  {selectedStockLot.quality_status}
                </Badge>
              )}
            </div>
          </div>

          {/* Expiry Warning */}
          {selectedStockLot.expiry_date && (
            <div className="mt-2 pt-2 border-t">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">
                  Expires: {new Date(selectedStockLot.expiry_date).toLocaleDateString()}
                </span>
                {new Date(selectedStockLot.expiry_date).getTime() - Date.now() < 30 * 24 * 60 * 60 * 1000 && (
                  <Badge variant="destructive" className="text-xs">
                    Expiring Soon
                  </Badge>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Summary Information */}
      {stockLotOptions.length === 0 && filteredStockLots.length === 0 && (
        <div className="text-sm text-muted-foreground text-center py-4">
          No stock lots available matching the current criteria
        </div>
      )}
      
      {stockLotOptions.length > 0 && (
        <div className="text-xs text-muted-foreground">
          {stockLotOptions.length} stock lot{stockLotOptions.length !== 1 ? 's' : ''} available
        </div>
      )}
    </div>
  )
}
