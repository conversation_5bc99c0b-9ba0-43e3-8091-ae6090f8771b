"use client"

import { useState, useEffect } from "react"
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useI18n } from "@/components/i18n-provider"
import { useSafeToast } from "@/hooks/use-safe-toast"
import { 
  TransactionType, 
  TransactionStatus, 
  ReferenceType,
  inventoryTransactionSchema,
  inboundTransactionSchema,
  outboundTransactionSchema,
  transferTransactionSchema,
  adjustmentTransactionSchema
} from "@/lib/validations"
import { ArrowUpCircle, ArrowDownCircle, ArrowRightLeft, Settings, Package, MapPin } from "lucide-react"

/**
 * Manufacturing ERP - Comprehensive Transaction Form Component
 * 
 * Features:
 * - Dynamic form validation based on transaction type
 * - Searchable stock lot selection
 * - Location management for transfers
 * - Cost calculation
 * - Reference linking to work orders, quality inspections, etc.
 * - Bilingual support
 * - Professional validation and error handling
 */

interface StockLot {
  id: string
  lot_number?: string
  qty: string
  location: string
  product: {
    id: string
    sku: string
    name: string
    unit: string
  }
}

interface TransactionFormProps {
  stockLots: StockLot[]
  transactionType: TransactionType
  initialData?: any
  onSubmit: (data: any) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

// Transaction type configurations
const transactionTypeConfig = {
  [TransactionType.INBOUND]: {
    icon: ArrowUpCircle,
    title: "Inbound Transaction",
    description: "Receive inventory into stock",
    color: "text-green-600",
    schema: inboundTransactionSchema
  },
  [TransactionType.OUTBOUND]: {
    icon: ArrowDownCircle,
    title: "Outbound Transaction", 
    description: "Remove inventory from stock",
    color: "text-red-600",
    schema: outboundTransactionSchema
  },
  [TransactionType.TRANSFER]: {
    icon: ArrowRightLeft,
    title: "Transfer Transaction",
    description: "Move inventory between locations",
    color: "text-blue-600",
    schema: transferTransactionSchema
  },
  [TransactionType.ADJUSTMENT]: {
    icon: Settings,
    title: "Adjustment Transaction",
    description: "Adjust inventory quantities",
    color: "text-purple-600",
    schema: adjustmentTransactionSchema
  }
}

export function TransactionForm({
  stockLots,
  transactionType,
  initialData,
  onSubmit,
  onCancel,
  loading = false
}: TransactionFormProps) {
  const { t } = useI18n()
  const { toast } = useSafeToast()
  const [selectedStockLot, setSelectedStockLot] = useState<StockLot | null>(null)
  const [calculatedTotal, setCalculatedTotal] = useState<number>(0)

  const config = transactionTypeConfig[transactionType]
  const Icon = config.icon

  // Dynamic form schema based on transaction type
  const form = useForm({
    resolver: zodResolver(config.schema),
    defaultValues: {
      transactionType,
      stockLotId: initialData?.stockLotId || "",
      quantity: initialData?.quantity || 0,
      unitCost: initialData?.unitCost || undefined,
      locationFrom: initialData?.locationFrom || "",
      locationTo: initialData?.locationTo || "",
      referenceType: initialData?.referenceType || "",
      referenceId: initialData?.referenceId || "",
      referenceNumber: initialData?.referenceNumber || "",
      notes: initialData?.notes || "",
    }
  })

  // Convert stock lots to searchable options
  const stockLotOptions: SearchableSelectOption[] = stockLots.map((lot) => ({
    value: lot.id,
    label: `${lot.product.name} ${lot.lot_number ? `(${lot.lot_number})` : ''}`,
    subtitle: `${lot.product.sku} • ${lot.qty} ${lot.product.unit} • ${lot.location}`,
    description: `Available: ${lot.qty} ${lot.product.unit}`
  }))

  // Get unique locations from stock lots
  const uniqueLocations = Array.from(new Set(stockLots.map(lot => lot.location).filter(Boolean)))

  // Watch form values for calculations
  const watchedQuantity = form.watch("quantity")
  const watchedUnitCost = form.watch("unitCost")
  const watchedStockLotId = form.watch("stockLotId")

  // Update selected stock lot when stockLotId changes
  useEffect(() => {
    if (watchedStockLotId) {
      const lot = stockLots.find(l => l.id === watchedStockLotId)
      setSelectedStockLot(lot || null)
      
      // Auto-fill location for inbound/outbound transactions
      if (lot) {
        if (transactionType === TransactionType.INBOUND) {
          form.setValue("locationTo", lot.location)
        } else if (transactionType === TransactionType.OUTBOUND) {
          form.setValue("locationFrom", lot.location)
        }
      }
    }
  }, [watchedStockLotId, stockLots, transactionType, form])

  // Calculate total cost
  useEffect(() => {
    if (watchedQuantity && watchedUnitCost) {
      setCalculatedTotal(Math.abs(watchedQuantity) * watchedUnitCost)
    } else {
      setCalculatedTotal(0)
    }
  }, [watchedQuantity, watchedUnitCost])

  const handleSubmit = async (data: any) => {
    try {
      // Validate stock availability for outbound transactions
      if (transactionType === TransactionType.OUTBOUND && selectedStockLot) {
        const availableQty = parseFloat(selectedStockLot.qty)
        if (data.quantity > availableQty) {
          toast({
            title: "Insufficient Stock",
            description: `Only ${availableQty} ${selectedStockLot.product.unit} available`,
            variant: "destructive"
          })
          return
        }
      }

      await onSubmit(data)
    } catch (error) {
      console.error("Transaction form error:", error)
      toast({
        title: "Error",
        description: "Failed to process transaction",
        variant: "destructive"
      })
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon className={`h-5 w-5 ${config.color}`} />
          {config.title}
        </CardTitle>
        <CardDescription>{config.description}</CardDescription>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Stock Lot Selection */}
            <FormField
              control={form.control}
              name="stockLotId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Stock Lot *
                  </FormLabel>
                  <FormControl>
                    <SearchableSelect
                      options={stockLotOptions}
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder="Select stock lot..."
                      searchPlaceholder="Search by product name or SKU..."
                      emptyMessage="No stock lots found"
                      className="w-full"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Selected Stock Lot Info */}
            {selectedStockLot && (
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{selectedStockLot.product.name}</div>
                    <div className="text-sm text-muted-foreground">
                      SKU: {selectedStockLot.product.sku} • Available: {selectedStockLot.qty} {selectedStockLot.product.unit}
                    </div>
                  </div>
                  <Badge variant="outline">{selectedStockLot.location}</Badge>
                </div>
              </div>
            )}

            <Separator />

            {/* Quantity and Cost */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Quantity * {selectedStockLot && `(${selectedStockLot.product.unit})`}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="unitCost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit Cost (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Total Cost Display */}
            {calculatedTotal > 0 && (
              <div className="text-right">
                <span className="text-sm text-muted-foreground">Total Cost: </span>
                <span className="font-medium">${calculatedTotal.toFixed(2)}</span>
              </div>
            )}

            <Separator />

            {/* Location Fields */}
            {(transactionType === TransactionType.TRANSFER || transactionType === TransactionType.INBOUND || transactionType === TransactionType.OUTBOUND) && (
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span className="font-medium">Location Information</span>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {(transactionType === TransactionType.TRANSFER || transactionType === TransactionType.OUTBOUND) && (
                    <FormField
                      control={form.control}
                      name="locationFrom"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>From Location *</FormLabel>
                          <FormControl>
                            <Select value={field.value} onValueChange={field.onChange}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select location" />
                              </SelectTrigger>
                              <SelectContent>
                                {uniqueLocations.map((location) => (
                                  <SelectItem key={location} value={location}>
                                    {location}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {(transactionType === TransactionType.TRANSFER || transactionType === TransactionType.INBOUND) && (
                    <FormField
                      control={form.control}
                      name="locationTo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>To Location *</FormLabel>
                          <FormControl>
                            <Select value={field.value} onValueChange={field.onChange}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select location" />
                              </SelectTrigger>
                              <SelectContent>
                                {uniqueLocations.map((location) => (
                                  <SelectItem key={location} value={location}>
                                    {location}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </div>
            )}

            <Separator />

            {/* Reference Information */}
            <div className="space-y-4">
              <div className="font-medium">Reference Information (Optional)</div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="referenceType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reference Type</FormLabel>
                      <FormControl>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">None</SelectItem>
                            <SelectItem value={ReferenceType.WORK_ORDER}>Work Order</SelectItem>
                            <SelectItem value={ReferenceType.QUALITY_INSPECTION}>Quality Inspection</SelectItem>
                            <SelectItem value={ReferenceType.PURCHASE_RECEIPT}>Purchase Receipt</SelectItem>
                            <SelectItem value={ReferenceType.SALES_SHIPMENT}>Sales Shipment</SelectItem>
                            <SelectItem value={ReferenceType.MANUAL}>Manual</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="referenceNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reference Number</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. WO-2024-001" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes about this transaction..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Form Actions */}
            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Processing..." : "Create Transaction"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
