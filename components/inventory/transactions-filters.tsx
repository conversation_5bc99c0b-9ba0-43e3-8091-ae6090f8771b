"use client"

import { useState } from "react"
import { Search, Filter, X, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
// import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { useI18n } from "@/components/i18n-provider"
import { TransactionType, TransactionStatus, ReferenceType } from "@/lib/validations"
import { format } from "date-fns"

/**
 * Manufacturing ERP - Inventory Transactions Filters Component
 * 
 * Features:
 * - Search by transaction number or reference
 * - Filter by transaction type, status, reference type
 * - Date range filtering
 * - Location filtering
 * - Active filter badges with clear functionality
 * - Responsive design
 * - Bilingual support
 */

export interface TransactionFilters {
  search: string
  transactionType: string
  transactionStatus: string
  referenceType: string
  locationFrom: string
  locationTo: string
  dateFrom: Date | undefined
  dateTo: Date | undefined
}

interface TransactionsFiltersProps {
  filters: TransactionFilters
  onFiltersChange: (filters: TransactionFilters) => void
  onClearFilters: () => void
  stockLots?: Array<{ id: string; location: string }>
}

export function TransactionsFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  stockLots = []
}: TransactionsFiltersProps) {
  const { t } = useI18n()
  const [showAdvanced, setShowAdvanced] = useState(false)

  // Get unique locations from stock lots
  const uniqueLocations = Array.from(new Set(stockLots.map(lot => lot.location).filter(Boolean)))

  const updateFilter = (key: keyof TransactionFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.transactionType) count++
    if (filters.transactionStatus) count++
    if (filters.referenceType) count++
    if (filters.locationFrom) count++
    if (filters.locationTo) count++
    if (filters.dateFrom) count++
    if (filters.dateTo) count++
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <div className="space-y-4">
      {/* Primary Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search Input */}
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search transactions..."
            value={filters.search}
            onChange={(e) => updateFilter('search', e.target.value)}
            className="pl-8"
          />
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2">
          <Select
            value={filters.transactionType}
            onValueChange={(value) => updateFilter('transactionType', value === 'all' ? '' : value)}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value={TransactionType.INBOUND}>Inbound</SelectItem>
              <SelectItem value={TransactionType.OUTBOUND}>Outbound</SelectItem>
              <SelectItem value={TransactionType.TRANSFER}>Transfer</SelectItem>
              <SelectItem value={TransactionType.ADJUSTMENT}>Adjustment</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.transactionStatus}
            onValueChange={(value) => updateFilter('transactionStatus', value === 'all' ? '' : value)}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value={TransactionStatus.PENDING}>Pending</SelectItem>
              <SelectItem value={TransactionStatus.COMPLETED}>Completed</SelectItem>
              <SelectItem value={TransactionStatus.CANCELLED}>Cancelled</SelectItem>
            </SelectContent>
          </Select>

          {/* Advanced Filters Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="relative"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>

          {/* Clear Filters */}
          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="text-muted-foreground"
            >
              <X className="h-4 w-4 mr-2" />
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-4 border rounded-lg bg-muted/20">
          {/* Reference Type Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Reference Type</label>
            <Select
              value={filters.referenceType}
              onValueChange={(value) => updateFilter('referenceType', value === 'all' ? '' : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All References" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All References</SelectItem>
                <SelectItem value={ReferenceType.WORK_ORDER}>Work Order</SelectItem>
                <SelectItem value={ReferenceType.QUALITY_INSPECTION}>Quality Inspection</SelectItem>
                <SelectItem value={ReferenceType.PURCHASE_RECEIPT}>Purchase Receipt</SelectItem>
                <SelectItem value={ReferenceType.SALES_SHIPMENT}>Sales Shipment</SelectItem>
                <SelectItem value={ReferenceType.MANUAL}>Manual</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Location From Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">From Location</label>
            <Select
              value={filters.locationFrom}
              onValueChange={(value) => updateFilter('locationFrom', value === 'all' ? '' : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Locations" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Locations</SelectItem>
                {uniqueLocations.map((location) => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Location To Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">To Location</label>
            <Select
              value={filters.locationTo}
              onValueChange={(value) => updateFilter('locationTo', value === 'all' ? '' : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Locations" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Locations</SelectItem>
                {uniqueLocations.map((location) => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Date Range Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Date Range</label>
            <div className="flex gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Calendar className="h-4 w-4 mr-2" />
                    {filters.dateFrom ? format(filters.dateFrom, "MMM dd") : "From"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <div className="p-3">
                    <input
                      type="date"
                      value={filters.dateFrom ? format(filters.dateFrom, "yyyy-MM-dd") : ""}
                      onChange={(e) => updateFilter('dateFrom', e.target.value ? new Date(e.target.value) : undefined)}
                      className="w-full p-2 border rounded"
                      placeholder="Select start date"
                    />
                  </div>
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Calendar className="h-4 w-4 mr-2" />
                    {filters.dateTo ? format(filters.dateTo, "MMM dd") : "To"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <div className="p-3">
                    <input
                      type="date"
                      value={filters.dateTo ? format(filters.dateTo, "yyyy-MM-dd") : ""}
                      onChange={(e) => updateFilter('dateTo', e.target.value ? new Date(e.target.value) : undefined)}
                      className="w-full p-2 border rounded"
                      placeholder="Select end date"
                    />
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              Search: {filters.search}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter('search', '')}
              />
            </Badge>
          )}
          {filters.transactionType && (
            <Badge variant="secondary" className="gap-1">
              Type: {filters.transactionType}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter('transactionType', '')}
              />
            </Badge>
          )}
          {filters.transactionStatus && (
            <Badge variant="secondary" className="gap-1">
              Status: {filters.transactionStatus}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter('transactionStatus', '')}
              />
            </Badge>
          )}
          {filters.referenceType && (
            <Badge variant="secondary" className="gap-1">
              Reference: {filters.referenceType}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter('referenceType', '')}
              />
            </Badge>
          )}
          {filters.dateFrom && (
            <Badge variant="secondary" className="gap-1">
              From: {format(filters.dateFrom, "MMM dd, yyyy")}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter('dateFrom', undefined)}
              />
            </Badge>
          )}
          {filters.dateTo && (
            <Badge variant="secondary" className="gap-1">
              To: {format(filters.dateTo, "MMM dd, yyyy")}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter('dateTo', undefined)}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
