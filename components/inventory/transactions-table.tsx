"use client"

import { useState } from "react"
import { Eye, Edit, MoreHorizontal, ArrowUpCircle, ArrowDownCircle, ArrowRightLeft, Settings, CheckCircle, Clock, XCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useI18n } from "@/components/i18n-provider"
import { TransactionType, TransactionStatus } from "@/lib/validations"
import { formatDistanceToNow } from "date-fns"

/**
 * Manufacturing ERP - Professional Inventory Transactions Table
 * 
 * Features:
 * - Professional table layout with Shadcn/ui components
 * - Transaction type badges with icons
 * - Status indicators with colors
 * - Responsive design
 * - Action buttons for view/edit operations
 * - Bilingual support
 */

interface Transaction {
  id: string
  transaction_number: string
  transaction_type: TransactionType
  transaction_status: TransactionStatus
  quantity: string
  unit_cost?: string
  total_cost?: string
  location_from?: string
  location_to?: string
  reference_type?: string
  reference_number?: string
  notes?: string
  transaction_date: string
  created_at: string
  stockLot: {
    id: string
    lot_number?: string
    product: {
      id: string
      sku: string
      name: string
      unit: string
    }
  }
}

interface TransactionsTableProps {
  transactions: Transaction[]
  loading?: boolean
  onView?: (transaction: Transaction) => void
  onEdit?: (transaction: Transaction) => void
  onComplete?: (transactionId: string) => Promise<void>
  onCancel?: (transactionId: string) => Promise<void>
}

// Transaction type configuration
const transactionTypeConfig = {
  [TransactionType.INBOUND]: {
    icon: ArrowUpCircle,
    label: "Inbound",
    color: "text-green-600",
    bgColor: "bg-green-50",
    borderColor: "border-green-200"
  },
  [TransactionType.OUTBOUND]: {
    icon: ArrowDownCircle,
    label: "Outbound",
    color: "text-red-600",
    bgColor: "bg-red-50",
    borderColor: "border-red-200"
  },
  [TransactionType.TRANSFER]: {
    icon: ArrowRightLeft,
    label: "Transfer",
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200"
  },
  [TransactionType.ADJUSTMENT]: {
    icon: Settings,
    label: "Adjustment",
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200"
  }
}

// Transaction status configuration
const transactionStatusConfig = {
  [TransactionStatus.PENDING]: {
    icon: Clock,
    label: "Pending",
    variant: "secondary" as const,
    color: "text-yellow-600"
  },
  [TransactionStatus.COMPLETED]: {
    icon: CheckCircle,
    label: "Completed",
    variant: "default" as const,
    color: "text-green-600"
  },
  [TransactionStatus.CANCELLED]: {
    icon: XCircle,
    label: "Cancelled",
    variant: "destructive" as const,
    color: "text-red-600"
  }
}

function TransactionTypeBadge({ type }: { type: TransactionType }) {
  const config = transactionTypeConfig[type]
  const Icon = config.icon

  return (
    <div className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-md text-xs font-medium ${config.bgColor} ${config.color} ${config.borderColor} border`}>
      <Icon className="h-3 w-3" />
      {config.label}
    </div>
  )
}

function TransactionStatusBadge({ status }: { status: TransactionStatus }) {
  const config = transactionStatusConfig[status]
  const Icon = config.icon

  return (
    <Badge variant={config.variant} className="inline-flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  )
}

export function TransactionsTable({
  transactions,
  loading = false,
  onView,
  onEdit,
  onComplete,
  onCancel
}: TransactionsTableProps) {
  const { t } = useI18n()
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  const handleAction = async (action: () => Promise<void>, transactionId: string) => {
    setActionLoading(transactionId)
    try {
      await action()
    } finally {
      setActionLoading(null)
    }
  }

  if (loading) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Transaction</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Product</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...Array(5)].map((_, i) => (
              <TableRow key={i}>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
                <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse" /></TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Transaction</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Product</TableHead>
            <TableHead>Quantity</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Date</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                No inventory transactions found
              </TableCell>
            </TableRow>
          ) : (
            transactions.map((transaction) => (
              <TableRow key={transaction.id} className="hover:bg-muted/50">
                <TableCell>
                  <div>
                    <div className="font-medium font-mono text-sm">{transaction.transaction_number}</div>
                    {transaction.reference_number && (
                      <div className="text-xs text-muted-foreground">Ref: {transaction.reference_number}</div>
                    )}
                  </div>
                </TableCell>

                <TableCell>
                  <TransactionTypeBadge type={transaction.transaction_type} />
                </TableCell>

                <TableCell>
                  <div>
                    <div className="font-medium">{transaction.stockLot.product.name}</div>
                    <div className="text-sm text-muted-foreground font-mono">
                      {transaction.stockLot.product.sku}
                    </div>
                  </div>
                </TableCell>

                <TableCell>
                  <div className="font-medium">
                    {parseFloat(transaction.quantity).toLocaleString()} {transaction.stockLot.product.unit}
                  </div>
                  {transaction.unit_cost && (
                    <div className="text-xs text-muted-foreground">
                      @ ${parseFloat(transaction.unit_cost).toFixed(2)}
                    </div>
                  )}
                </TableCell>

                <TableCell>
                  <div className="text-sm">
                    {transaction.location_from && (
                      <div className="text-muted-foreground">From: {transaction.location_from}</div>
                    )}
                    {transaction.location_to && (
                      <div className="text-muted-foreground">To: {transaction.location_to}</div>
                    )}
                    {!transaction.location_from && !transaction.location_to && (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </div>
                </TableCell>

                <TableCell>
                  <TransactionStatusBadge status={transaction.transaction_status} />
                </TableCell>

                <TableCell>
                  <div className="text-sm">
                    {formatDistanceToNow(new Date(transaction.transaction_date), { addSuffix: true })}
                  </div>
                </TableCell>

                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        disabled={actionLoading === transaction.id}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {onView && (
                        <DropdownMenuItem onClick={() => onView(transaction)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                      )}
                      {onEdit && transaction.transaction_status === TransactionStatus.PENDING && (
                        <DropdownMenuItem onClick={() => onEdit(transaction)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                      )}
                      {onComplete && transaction.transaction_status === TransactionStatus.PENDING && (
                        <DropdownMenuItem
                          onClick={() => handleAction(() => onComplete(transaction.id), transaction.id)}
                        >
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Complete
                        </DropdownMenuItem>
                      )}
                      {onCancel && transaction.transaction_status === TransactionStatus.PENDING && (
                        <DropdownMenuItem
                          onClick={() => handleAction(() => onCancel(transaction.id), transaction.id)}
                          className="text-red-600"
                        >
                          <XCircle className="mr-2 h-4 w-4" />
                          Cancel
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
