"use client";

import { useCallback } from "react";
import { toast } from "sonner";

interface ToastOptions {
  title: string;
  description?: string;
  variant?: "default" | "destructive";
}

/**
 * Professional toast hook that prevents React setState during render errors
 * by properly scheduling toast notifications outside the render cycle
 */
export function useSafeToast() {
  const safeToast = useCallback((options: ToastOptions) => {
    // Use requestAnimationFrame to ensure toast is shown after render cycle
    requestAnimationFrame(() => {
      const { title, description, variant = "default" } = options;

      if (variant === "destructive") {
        toast.error(title, description ? { description } : undefined);
      } else {
        toast.success(title, description ? { description } : undefined);
      }
    });
  }, []);

  const showSuccess = useCallback((message: string, description?: string) => {
    // Use requestAnimationFrame to ensure toast is shown after render cycle
    requestAnimationFrame(() => {
      toast.success(message, description ? { description } : undefined);
    });
  }, []);

  const showError = useCallback((message: string, description?: string) => {
    // Use requestAnimationFrame to ensure toast is shown after render cycle
    requestAnimationFrame(() => {
      toast.error(message, description ? { description } : undefined);
    });
  }, []);

  const showInfo = useCallback((message: string, description?: string) => {
    // Use requestAnimationFrame to ensure toast is shown after render cycle
    requestAnimationFrame(() => {
      toast.info(message, description ? { description } : undefined);
    });
  }, []);

  const showWarning = useCallback((message: string, description?: string) => {
    // Use requestAnimationFrame to ensure toast is shown after render cycle
    requestAnimationFrame(() => {
      toast.warning(message, description ? { description } : undefined);
    });
  }, []);

  return {
    toast: safeToast, // Main toast function with shadcn/ui API
    success: showSuccess,
    error: showError,
    info: showInfo,
    warning: showWarning,
  };
}
